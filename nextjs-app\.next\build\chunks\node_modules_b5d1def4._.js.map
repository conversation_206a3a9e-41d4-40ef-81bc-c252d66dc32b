{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/picocolors/picocolors.js"], "sourcesContent": ["let p = process || {}, argv = p.argv || [], env = p.env || {}\nlet isColorSupported =\n\t!(!!env.NO_COLOR || argv.includes(\"--no-color\")) &&\n\t(!!env.FORCE_COLOR || argv.includes(\"--color\") || p.platform === \"win32\" || ((p.stdout || {}).isTTY && env.TERM !== \"dumb\") || !!env.CI)\n\nlet formatter = (open, close, replace = open) =>\n\tinput => {\n\t\tlet string = \"\" + input, index = string.indexOf(close, open.length)\n\t\treturn ~index ? open + replaceClose(string, close, replace, index) + close : open + string + close\n\t}\n\nlet replaceClose = (string, close, replace, index) => {\n\tlet result = \"\", cursor = 0\n\tdo {\n\t\tresult += string.substring(cursor, index) + replace\n\t\tcursor = index + close.length\n\t\tindex = string.indexOf(close, cursor)\n\t} while (~index)\n\treturn result + string.substring(cursor)\n}\n\nlet createColors = (enabled = isColorSupported) => {\n\tlet f = enabled ? formatter : () => String\n\treturn {\n\t\tisColorSupported: enabled,\n\t\treset: f(\"\\x1b[0m\", \"\\x1b[0m\"),\n\t\tbold: f(\"\\x1b[1m\", \"\\x1b[22m\", \"\\x1b[22m\\x1b[1m\"),\n\t\tdim: f(\"\\x1b[2m\", \"\\x1b[22m\", \"\\x1b[22m\\x1b[2m\"),\n\t\titalic: f(\"\\x1b[3m\", \"\\x1b[23m\"),\n\t\tunderline: f(\"\\x1b[4m\", \"\\x1b[24m\"),\n\t\tinverse: f(\"\\x1b[7m\", \"\\x1b[27m\"),\n\t\thidden: f(\"\\x1b[8m\", \"\\x1b[28m\"),\n\t\tstrikethrough: f(\"\\x1b[9m\", \"\\x1b[29m\"),\n\n\t\tblack: f(\"\\x1b[30m\", \"\\x1b[39m\"),\n\t\tred: f(\"\\x1b[31m\", \"\\x1b[39m\"),\n\t\tgreen: f(\"\\x1b[32m\", \"\\x1b[39m\"),\n\t\tyellow: f(\"\\x1b[33m\", \"\\x1b[39m\"),\n\t\tblue: f(\"\\x1b[34m\", \"\\x1b[39m\"),\n\t\tmagenta: f(\"\\x1b[35m\", \"\\x1b[39m\"),\n\t\tcyan: f(\"\\x1b[36m\", \"\\x1b[39m\"),\n\t\twhite: f(\"\\x1b[37m\", \"\\x1b[39m\"),\n\t\tgray: f(\"\\x1b[90m\", \"\\x1b[39m\"),\n\n\t\tbgBlack: f(\"\\x1b[40m\", \"\\x1b[49m\"),\n\t\tbgRed: f(\"\\x1b[41m\", \"\\x1b[49m\"),\n\t\tbgGreen: f(\"\\x1b[42m\", \"\\x1b[49m\"),\n\t\tbgYellow: f(\"\\x1b[43m\", \"\\x1b[49m\"),\n\t\tbgBlue: f(\"\\x1b[44m\", \"\\x1b[49m\"),\n\t\tbgMagenta: f(\"\\x1b[45m\", \"\\x1b[49m\"),\n\t\tbgCyan: f(\"\\x1b[46m\", \"\\x1b[49m\"),\n\t\tbgWhite: f(\"\\x1b[47m\", \"\\x1b[49m\"),\n\n\t\tblackBright: f(\"\\x1b[90m\", \"\\x1b[39m\"),\n\t\tredBright: f(\"\\x1b[91m\", \"\\x1b[39m\"),\n\t\tgreenBright: f(\"\\x1b[92m\", \"\\x1b[39m\"),\n\t\tyellowBright: f(\"\\x1b[93m\", \"\\x1b[39m\"),\n\t\tblueBright: f(\"\\x1b[94m\", \"\\x1b[39m\"),\n\t\tmagentaBright: f(\"\\x1b[95m\", \"\\x1b[39m\"),\n\t\tcyanBright: f(\"\\x1b[96m\", \"\\x1b[39m\"),\n\t\twhiteBright: f(\"\\x1b[97m\", \"\\x1b[39m\"),\n\n\t\tbgBlackBright: f(\"\\x1b[100m\", \"\\x1b[49m\"),\n\t\tbgRedBright: f(\"\\x1b[101m\", \"\\x1b[49m\"),\n\t\tbgGreenBright: f(\"\\x1b[102m\", \"\\x1b[49m\"),\n\t\tbgYellowBright: f(\"\\x1b[103m\", \"\\x1b[49m\"),\n\t\tbgBlueBright: f(\"\\x1b[104m\", \"\\x1b[49m\"),\n\t\tbgMagentaBright: f(\"\\x1b[105m\", \"\\x1b[49m\"),\n\t\tbgCyanBright: f(\"\\x1b[106m\", \"\\x1b[49m\"),\n\t\tbgWhiteBright: f(\"\\x1b[107m\", \"\\x1b[49m\"),\n\t}\n}\n\nmodule.exports = createColors()\nmodule.exports.createColors = createColors\n"], "names": [], "mappings": "AAAA,IAAI,IAAI,WAAW,CAAC,GAAG,OAAO,EAAE,IAAI,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AAC5D,IAAI,mBACH,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,KAAK,QAAQ,CAAC,aAAa,KAC/C,CAAC,CAAC,CAAC,IAAI,WAAW,IAAI,KAAK,QAAQ,CAAC,cAAc,EAAE,QAAQ,KAAK,WAAY,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,IAAI,KAAK,UAAW,CAAC,CAAC,IAAI,EAAE;AAExI,IAAI,YAAY,CAAC,MAAM,OAAO,UAAU,IAAI,GAC3C,CAAA;QACC,IAAI,SAAS,KAAK,OAAO,QAAQ,OAAO,OAAO,CAAC,OAAO,KAAK,MAAM;QAClE,OAAO,CAAC,QAAQ,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,QAAQ,OAAO,SAAS;IAC9F;AAED,IAAI,eAAe,CAAC,QAAQ,OAAO,SAAS;IAC3C,IAAI,SAAS,IAAI,SAAS;IAC1B,GAAG;QACF,UAAU,OAAO,SAAS,CAAC,QAAQ,SAAS;QAC5C,SAAS,QAAQ,MAAM,MAAM;QAC7B,QAAQ,OAAO,OAAO,CAAC,OAAO;IAC/B,QAAS,CAAC,MAAM;IAChB,OAAO,SAAS,OAAO,SAAS,CAAC;AAClC;AAEA,IAAI,eAAe,CAAC,UAAU,gBAAgB;IAC7C,IAAI,IAAI,UAAU,YAAY,IAAM;IACpC,OAAO;QACN,kBAAkB;QAClB,OAAO,EAAE,WAAW;QACpB,MAAM,EAAE,WAAW,YAAY;QAC/B,KAAK,EAAE,WAAW,YAAY;QAC9B,QAAQ,EAAE,WAAW;QACrB,WAAW,EAAE,WAAW;QACxB,SAAS,EAAE,WAAW;QACtB,QAAQ,EAAE,WAAW;QACrB,eAAe,EAAE,WAAW;QAE5B,OAAO,EAAE,YAAY;QACrB,KAAK,EAAE,YAAY;QACnB,OAAO,EAAE,YAAY;QACrB,QAAQ,EAAE,YAAY;QACtB,MAAM,EAAE,YAAY;QACpB,SAAS,EAAE,YAAY;QACvB,MAAM,EAAE,YAAY;QACpB,OAAO,EAAE,YAAY;QACrB,MAAM,EAAE,YAAY;QAEpB,SAAS,EAAE,YAAY;QACvB,OAAO,EAAE,YAAY;QACrB,SAAS,EAAE,YAAY;QACvB,UAAU,EAAE,YAAY;QACxB,QAAQ,EAAE,YAAY;QACtB,WAAW,EAAE,YAAY;QACzB,QAAQ,EAAE,YAAY;QACtB,SAAS,EAAE,YAAY;QAEvB,aAAa,EAAE,YAAY;QAC3B,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,YAAY;QAC3B,cAAc,EAAE,YAAY;QAC5B,YAAY,EAAE,YAAY;QAC1B,eAAe,EAAE,YAAY;QAC7B,YAAY,EAAE,YAAY;QAC1B,aAAa,EAAE,YAAY;QAE3B,eAAe,EAAE,aAAa;QAC9B,aAAa,EAAE,aAAa;QAC5B,eAAe,EAAE,aAAa;QAC9B,gBAAgB,EAAE,aAAa;QAC/B,cAAc,EAAE,aAAa;QAC7B,iBAAiB,EAAE,aAAa;QAChC,cAAc,EAAE,aAAa;QAC7B,eAAe,EAAE,aAAa;IAC/B;AACD;AAEA,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,YAAY,GAAG", "ignoreList": [0]}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/tokenize.js"], "sourcesContent": ["'use strict'\n\nconst SINGLE_QUOTE = \"'\".charCodeAt(0)\nconst DOUBLE_QUOTE = '\"'.charCodeAt(0)\nconst BACKSLASH = '\\\\'.charCodeAt(0)\nconst SLASH = '/'.charCodeAt(0)\nconst NEWLINE = '\\n'.charCodeAt(0)\nconst SPACE = ' '.charCodeAt(0)\nconst FEED = '\\f'.charCodeAt(0)\nconst TAB = '\\t'.charCodeAt(0)\nconst CR = '\\r'.charCodeAt(0)\nconst OPEN_SQUARE = '['.charCodeAt(0)\nconst CLOSE_SQUARE = ']'.charCodeAt(0)\nconst OPEN_PARENTHESES = '('.charCodeAt(0)\nconst CLOSE_PARENTHESES = ')'.charCodeAt(0)\nconst OPEN_CURLY = '{'.charCodeAt(0)\nconst CLOSE_CURLY = '}'.charCodeAt(0)\nconst SEMICOLON = ';'.charCodeAt(0)\nconst ASTERISK = '*'.charCodeAt(0)\nconst COLON = ':'.charCodeAt(0)\nconst AT = '@'.charCodeAt(0)\n\nconst RE_AT_END = /[\\t\\n\\f\\r \"#'()/;[\\\\\\]{}]/g\nconst RE_WORD_END = /[\\t\\n\\f\\r !\"#'():;@[\\\\\\]{}]|\\/(?=\\*)/g\nconst RE_BAD_BRACKET = /.[\\r\\n\"'(/\\\\]/\nconst RE_HEX_ESCAPE = /[\\da-f]/i\n\nmodule.exports = function tokenizer(input, options = {}) {\n  let css = input.css.valueOf()\n  let ignore = options.ignoreErrors\n\n  let code, content, escape, next, quote\n  let currentToken, escaped, escapePos, n, prev\n\n  let length = css.length\n  let pos = 0\n  let buffer = []\n  let returned = []\n\n  function position() {\n    return pos\n  }\n\n  function unclosed(what) {\n    throw input.error('Unclosed ' + what, pos)\n  }\n\n  function endOfFile() {\n    return returned.length === 0 && pos >= length\n  }\n\n  function nextToken(opts) {\n    if (returned.length) return returned.pop()\n    if (pos >= length) return\n\n    let ignoreUnclosed = opts ? opts.ignoreUnclosed : false\n\n    code = css.charCodeAt(pos)\n\n    switch (code) {\n      case NEWLINE:\n      case SPACE:\n      case TAB:\n      case CR:\n      case FEED: {\n        next = pos\n        do {\n          next += 1\n          code = css.charCodeAt(next)\n        } while (\n          code === SPACE ||\n          code === NEWLINE ||\n          code === TAB ||\n          code === CR ||\n          code === FEED\n        )\n\n        currentToken = ['space', css.slice(pos, next)]\n        pos = next - 1\n        break\n      }\n\n      case OPEN_SQUARE:\n      case CLOSE_SQUARE:\n      case OPEN_CURLY:\n      case CLOSE_CURLY:\n      case COLON:\n      case SEMICOLON:\n      case CLOSE_PARENTHESES: {\n        let controlChar = String.fromCharCode(code)\n        currentToken = [controlChar, controlChar, pos]\n        break\n      }\n\n      case OPEN_PARENTHESES: {\n        prev = buffer.length ? buffer.pop()[1] : ''\n        n = css.charCodeAt(pos + 1)\n        if (\n          prev === 'url' &&\n          n !== SINGLE_QUOTE &&\n          n !== DOUBLE_QUOTE &&\n          n !== SPACE &&\n          n !== NEWLINE &&\n          n !== TAB &&\n          n !== FEED &&\n          n !== CR\n        ) {\n          next = pos\n          do {\n            escaped = false\n            next = css.indexOf(')', next + 1)\n            if (next === -1) {\n              if (ignore || ignoreUnclosed) {\n                next = pos\n                break\n              } else {\n                unclosed('bracket')\n              }\n            }\n            escapePos = next\n            while (css.charCodeAt(escapePos - 1) === BACKSLASH) {\n              escapePos -= 1\n              escaped = !escaped\n            }\n          } while (escaped)\n\n          currentToken = ['brackets', css.slice(pos, next + 1), pos, next]\n\n          pos = next\n        } else {\n          next = css.indexOf(')', pos + 1)\n          content = css.slice(pos, next + 1)\n\n          if (next === -1 || RE_BAD_BRACKET.test(content)) {\n            currentToken = ['(', '(', pos]\n          } else {\n            currentToken = ['brackets', content, pos, next]\n            pos = next\n          }\n        }\n\n        break\n      }\n\n      case SINGLE_QUOTE:\n      case DOUBLE_QUOTE: {\n        quote = code === SINGLE_QUOTE ? \"'\" : '\"'\n        next = pos\n        do {\n          escaped = false\n          next = css.indexOf(quote, next + 1)\n          if (next === -1) {\n            if (ignore || ignoreUnclosed) {\n              next = pos + 1\n              break\n            } else {\n              unclosed('string')\n            }\n          }\n          escapePos = next\n          while (css.charCodeAt(escapePos - 1) === BACKSLASH) {\n            escapePos -= 1\n            escaped = !escaped\n          }\n        } while (escaped)\n\n        currentToken = ['string', css.slice(pos, next + 1), pos, next]\n        pos = next\n        break\n      }\n\n      case AT: {\n        RE_AT_END.lastIndex = pos + 1\n        RE_AT_END.test(css)\n        if (RE_AT_END.lastIndex === 0) {\n          next = css.length - 1\n        } else {\n          next = RE_AT_END.lastIndex - 2\n        }\n\n        currentToken = ['at-word', css.slice(pos, next + 1), pos, next]\n\n        pos = next\n        break\n      }\n\n      case BACKSLASH: {\n        next = pos\n        escape = true\n        while (css.charCodeAt(next + 1) === BACKSLASH) {\n          next += 1\n          escape = !escape\n        }\n        code = css.charCodeAt(next + 1)\n        if (\n          escape &&\n          code !== SLASH &&\n          code !== SPACE &&\n          code !== NEWLINE &&\n          code !== TAB &&\n          code !== CR &&\n          code !== FEED\n        ) {\n          next += 1\n          if (RE_HEX_ESCAPE.test(css.charAt(next))) {\n            while (RE_HEX_ESCAPE.test(css.charAt(next + 1))) {\n              next += 1\n            }\n            if (css.charCodeAt(next + 1) === SPACE) {\n              next += 1\n            }\n          }\n        }\n\n        currentToken = ['word', css.slice(pos, next + 1), pos, next]\n\n        pos = next\n        break\n      }\n\n      default: {\n        if (code === SLASH && css.charCodeAt(pos + 1) === ASTERISK) {\n          next = css.indexOf('*/', pos + 2) + 1\n          if (next === 0) {\n            if (ignore || ignoreUnclosed) {\n              next = css.length\n            } else {\n              unclosed('comment')\n            }\n          }\n\n          currentToken = ['comment', css.slice(pos, next + 1), pos, next]\n          pos = next\n        } else {\n          RE_WORD_END.lastIndex = pos + 1\n          RE_WORD_END.test(css)\n          if (RE_WORD_END.lastIndex === 0) {\n            next = css.length - 1\n          } else {\n            next = RE_WORD_END.lastIndex - 2\n          }\n\n          currentToken = ['word', css.slice(pos, next + 1), pos, next]\n          buffer.push(currentToken)\n          pos = next\n        }\n\n        break\n      }\n    }\n\n    pos++\n    return currentToken\n  }\n\n  function back(token) {\n    returned.push(token)\n  }\n\n  return {\n    back,\n    endOfFile,\n    nextToken,\n    position\n  }\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,eAAe,IAAI,UAAU,CAAC;AACpC,MAAM,eAAe,IAAI,UAAU,CAAC;AACpC,MAAM,YAAY,KAAK,UAAU,CAAC;AAClC,MAAM,QAAQ,IAAI,UAAU,CAAC;AAC7B,MAAM,UAAU,KAAK,UAAU,CAAC;AAChC,MAAM,QAAQ,IAAI,UAAU,CAAC;AAC7B,MAAM,OAAO,KAAK,UAAU,CAAC;AAC7B,MAAM,MAAM,KAAK,UAAU,CAAC;AAC5B,MAAM,KAAK,KAAK,UAAU,CAAC;AAC3B,MAAM,cAAc,IAAI,UAAU,CAAC;AACnC,MAAM,eAAe,IAAI,UAAU,CAAC;AACpC,MAAM,mBAAmB,IAAI,UAAU,CAAC;AACxC,MAAM,oBAAoB,IAAI,UAAU,CAAC;AACzC,MAAM,aAAa,IAAI,UAAU,CAAC;AAClC,MAAM,cAAc,IAAI,UAAU,CAAC;AACnC,MAAM,YAAY,IAAI,UAAU,CAAC;AACjC,MAAM,WAAW,IAAI,UAAU,CAAC;AAChC,MAAM,QAAQ,IAAI,UAAU,CAAC;AAC7B,MAAM,KAAK,IAAI,UAAU,CAAC;AAE1B,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,MAAM,iBAAiB;AACvB,MAAM,gBAAgB;AAEtB,OAAO,OAAO,GAAG,SAAS,UAAU,KAAK,EAAE,UAAU,CAAC,CAAC;IACrD,IAAI,MAAM,MAAM,GAAG,CAAC,OAAO;IAC3B,IAAI,SAAS,QAAQ,YAAY;IAEjC,IAAI,MAAM,SAAS,QAAQ,MAAM;IACjC,IAAI,cAAc,SAAS,WAAW,GAAG;IAEzC,IAAI,SAAS,IAAI,MAAM;IACvB,IAAI,MAAM;IACV,IAAI,SAAS,EAAE;IACf,IAAI,WAAW,EAAE;IAEjB,SAAS;QACP,OAAO;IACT;IAEA,SAAS,SAAS,IAAI;QACpB,MAAM,MAAM,KAAK,CAAC,cAAc,MAAM;IACxC;IAEA,SAAS;QACP,OAAO,SAAS,MAAM,KAAK,KAAK,OAAO;IACzC;IAEA,SAAS,UAAU,IAAI;QACrB,IAAI,SAAS,MAAM,EAAE,OAAO,SAAS,GAAG;QACxC,IAAI,OAAO,QAAQ;QAEnB,IAAI,iBAAiB,OAAO,KAAK,cAAc,GAAG;QAElD,OAAO,IAAI,UAAU,CAAC;QAEtB,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAM;oBACT,OAAO;oBACP,GAAG;wBACD,QAAQ;wBACR,OAAO,IAAI,UAAU,CAAC;oBACxB,QACE,SAAS,SACT,SAAS,WACT,SAAS,OACT,SAAS,MACT,SAAS,KACV;oBAED,eAAe;wBAAC;wBAAS,IAAI,KAAK,CAAC,KAAK;qBAAM;oBAC9C,MAAM,OAAO;oBACb;gBACF;YAEA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAmB;oBACtB,IAAI,cAAc,OAAO,YAAY,CAAC;oBACtC,eAAe;wBAAC;wBAAa;wBAAa;qBAAI;oBAC9C;gBACF;YAEA,KAAK;gBAAkB;oBACrB,OAAO,OAAO,MAAM,GAAG,OAAO,GAAG,EAAE,CAAC,EAAE,GAAG;oBACzC,IAAI,IAAI,UAAU,CAAC,MAAM;oBACzB,IACE,SAAS,SACT,MAAM,gBACN,MAAM,gBACN,MAAM,SACN,MAAM,WACN,MAAM,OACN,MAAM,QACN,MAAM,IACN;wBACA,OAAO;wBACP,GAAG;4BACD,UAAU;4BACV,OAAO,IAAI,OAAO,CAAC,KAAK,OAAO;4BAC/B,IAAI,SAAS,CAAC,GAAG;gCACf,IAAI,UAAU,gBAAgB;oCAC5B,OAAO;oCACP;gCACF,OAAO;oCACL,SAAS;gCACX;4BACF;4BACA,YAAY;4BACZ,MAAO,IAAI,UAAU,CAAC,YAAY,OAAO,UAAW;gCAClD,aAAa;gCACb,UAAU,CAAC;4BACb;wBACF,QAAS,QAAQ;wBAEjB,eAAe;4BAAC;4BAAY,IAAI,KAAK,CAAC,KAAK,OAAO;4BAAI;4BAAK;yBAAK;wBAEhE,MAAM;oBACR,OAAO;wBACL,OAAO,IAAI,OAAO,CAAC,KAAK,MAAM;wBAC9B,UAAU,IAAI,KAAK,CAAC,KAAK,OAAO;wBAEhC,IAAI,SAAS,CAAC,KAAK,eAAe,IAAI,CAAC,UAAU;4BAC/C,eAAe;gCAAC;gCAAK;gCAAK;6BAAI;wBAChC,OAAO;4BACL,eAAe;gCAAC;gCAAY;gCAAS;gCAAK;6BAAK;4BAC/C,MAAM;wBACR;oBACF;oBAEA;gBACF;YAEA,KAAK;YACL,KAAK;gBAAc;oBACjB,QAAQ,SAAS,eAAe,MAAM;oBACtC,OAAO;oBACP,GAAG;wBACD,UAAU;wBACV,OAAO,IAAI,OAAO,CAAC,OAAO,OAAO;wBACjC,IAAI,SAAS,CAAC,GAAG;4BACf,IAAI,UAAU,gBAAgB;gCAC5B,OAAO,MAAM;gCACb;4BACF,OAAO;gCACL,SAAS;4BACX;wBACF;wBACA,YAAY;wBACZ,MAAO,IAAI,UAAU,CAAC,YAAY,OAAO,UAAW;4BAClD,aAAa;4BACb,UAAU,CAAC;wBACb;oBACF,QAAS,QAAQ;oBAEjB,eAAe;wBAAC;wBAAU,IAAI,KAAK,CAAC,KAAK,OAAO;wBAAI;wBAAK;qBAAK;oBAC9D,MAAM;oBACN;gBACF;YAEA,KAAK;gBAAI;oBACP,UAAU,SAAS,GAAG,MAAM;oBAC5B,UAAU,IAAI,CAAC;oBACf,IAAI,UAAU,SAAS,KAAK,GAAG;wBAC7B,OAAO,IAAI,MAAM,GAAG;oBACtB,OAAO;wBACL,OAAO,UAAU,SAAS,GAAG;oBAC/B;oBAEA,eAAe;wBAAC;wBAAW,IAAI,KAAK,CAAC,KAAK,OAAO;wBAAI;wBAAK;qBAAK;oBAE/D,MAAM;oBACN;gBACF;YAEA,KAAK;gBAAW;oBACd,OAAO;oBACP,SAAS;oBACT,MAAO,IAAI,UAAU,CAAC,OAAO,OAAO,UAAW;wBAC7C,QAAQ;wBACR,SAAS,CAAC;oBACZ;oBACA,OAAO,IAAI,UAAU,CAAC,OAAO;oBAC7B,IACE,UACA,SAAS,SACT,SAAS,SACT,SAAS,WACT,SAAS,OACT,SAAS,MACT,SAAS,MACT;wBACA,QAAQ;wBACR,IAAI,cAAc,IAAI,CAAC,IAAI,MAAM,CAAC,QAAQ;4BACxC,MAAO,cAAc,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,IAAK;gCAC/C,QAAQ;4BACV;4BACA,IAAI,IAAI,UAAU,CAAC,OAAO,OAAO,OAAO;gCACtC,QAAQ;4BACV;wBACF;oBACF;oBAEA,eAAe;wBAAC;wBAAQ,IAAI,KAAK,CAAC,KAAK,OAAO;wBAAI;wBAAK;qBAAK;oBAE5D,MAAM;oBACN;gBACF;YAEA;gBAAS;oBACP,IAAI,SAAS,SAAS,IAAI,UAAU,CAAC,MAAM,OAAO,UAAU;wBAC1D,OAAO,IAAI,OAAO,CAAC,MAAM,MAAM,KAAK;wBACpC,IAAI,SAAS,GAAG;4BACd,IAAI,UAAU,gBAAgB;gCAC5B,OAAO,IAAI,MAAM;4BACnB,OAAO;gCACL,SAAS;4BACX;wBACF;wBAEA,eAAe;4BAAC;4BAAW,IAAI,KAAK,CAAC,KAAK,OAAO;4BAAI;4BAAK;yBAAK;wBAC/D,MAAM;oBACR,OAAO;wBACL,YAAY,SAAS,GAAG,MAAM;wBAC9B,YAAY,IAAI,CAAC;wBACjB,IAAI,YAAY,SAAS,KAAK,GAAG;4BAC/B,OAAO,IAAI,MAAM,GAAG;wBACtB,OAAO;4BACL,OAAO,YAAY,SAAS,GAAG;wBACjC;wBAEA,eAAe;4BAAC;4BAAQ,IAAI,KAAK,CAAC,KAAK,OAAO;4BAAI;4BAAK;yBAAK;wBAC5D,OAAO,IAAI,CAAC;wBACZ,MAAM;oBACR;oBAEA;gBACF;QACF;QAEA;QACA,OAAO;IACT;IAEA,SAAS,KAAK,KAAK;QACjB,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/terminal-highlight.js"], "sourcesContent": ["'use strict'\n\nlet pico = require('picocolors')\n\nlet tokenizer = require('./tokenize')\n\nlet Input\n\nfunction registerInput(dependant) {\n  Input = dependant\n}\n\nconst HIGHLIGHT_THEME = {\n  ';': pico.yellow,\n  ':': pico.yellow,\n  '(': pico.cyan,\n  ')': pico.cyan,\n  '[': pico.yellow,\n  ']': pico.yellow,\n  '{': pico.yellow,\n  '}': pico.yellow,\n  'at-word': pico.cyan,\n  'brackets': pico.cyan,\n  'call': pico.cyan,\n  'class': pico.yellow,\n  'comment': pico.gray,\n  'hash': pico.magenta,\n  'string': pico.green\n}\n\nfunction getTokenType([type, value], processor) {\n  if (type === 'word') {\n    if (value[0] === '.') {\n      return 'class'\n    }\n    if (value[0] === '#') {\n      return 'hash'\n    }\n  }\n\n  if (!processor.endOfFile()) {\n    let next = processor.nextToken()\n    processor.back(next)\n    if (next[0] === 'brackets' || next[0] === '(') return 'call'\n  }\n\n  return type\n}\n\nfunction terminalHighlight(css) {\n  let processor = tokenizer(new Input(css), { ignoreErrors: true })\n  let result = ''\n  while (!processor.endOfFile()) {\n    let token = processor.nextToken()\n    let color = HIGHLIGHT_THEME[getTokenType(token, processor)]\n    if (color) {\n      result += token[1]\n        .split(/\\r?\\n/)\n        .map(i => color(i))\n        .join('\\n')\n    } else {\n      result += token[1]\n    }\n  }\n  return result\n}\n\nterminalHighlight.registerInput = registerInput\n\nmodule.exports = terminalHighlight\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,IAAI;AAEJ,IAAI;AAEJ,SAAS,cAAc,SAAS;IAC9B,QAAQ;AACV;AAEA,MAAM,kBAAkB;IACtB,KAAK,KAAK,MAAM;IAChB,KAAK,KAAK,MAAM;IAChB,KAAK,KAAK,IAAI;IACd,KAAK,KAAK,IAAI;IACd,KAAK,KAAK,MAAM;IAChB,KAAK,KAAK,MAAM;IAChB,KAAK,KAAK,MAAM;IAChB,KAAK,KAAK,MAAM;IAChB,WAAW,KAAK,IAAI;IACpB,YAAY,KAAK,IAAI;IACrB,QAAQ,KAAK,IAAI;IACjB,SAAS,KAAK,MAAM;IACpB,WAAW,KAAK,IAAI;IACpB,QAAQ,KAAK,OAAO;IACpB,UAAU,KAAK,KAAK;AACtB;AAEA,SAAS,aAAa,CAAC,MAAM,MAAM,EAAE,SAAS;IAC5C,IAAI,SAAS,QAAQ;QACnB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,OAAO;QACT;QACA,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,OAAO;QACT;IACF;IAEA,IAAI,CAAC,UAAU,SAAS,IAAI;QAC1B,IAAI,OAAO,UAAU,SAAS;QAC9B,UAAU,IAAI,CAAC;QACf,IAAI,IAAI,CAAC,EAAE,KAAK,cAAc,IAAI,CAAC,EAAE,KAAK,KAAK,OAAO;IACxD;IAEA,OAAO;AACT;AAEA,SAAS,kBAAkB,GAAG;IAC5B,IAAI,YAAY,UAAU,IAAI,MAAM,MAAM;QAAE,cAAc;IAAK;IAC/D,IAAI,SAAS;IACb,MAAO,CAAC,UAAU,SAAS,GAAI;QAC7B,IAAI,QAAQ,UAAU,SAAS;QAC/B,IAAI,QAAQ,eAAe,CAAC,aAAa,OAAO,WAAW;QAC3D,IAAI,OAAO;YACT,UAAU,KAAK,CAAC,EAAE,CACf,KAAK,CAAC,SACN,GAAG,CAAC,CAAA,IAAK,MAAM,IACf,IAAI,CAAC;QACV,OAAO;YACL,UAAU,KAAK,CAAC,EAAE;QACpB;IACF;IACA,OAAO;AACT;AAEA,kBAAkB,aAAa,GAAG;AAElC,OAAO,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/css-syntax-error.js"], "sourcesContent": ["'use strict'\n\nlet pico = require('picocolors')\n\nlet terminalHighlight = require('./terminal-highlight')\n\nclass CssSyntaxError extends Error {\n  constructor(message, line, column, source, file, plugin) {\n    super(message)\n    this.name = 'CssSyntaxError'\n    this.reason = message\n\n    if (file) {\n      this.file = file\n    }\n    if (source) {\n      this.source = source\n    }\n    if (plugin) {\n      this.plugin = plugin\n    }\n    if (typeof line !== 'undefined' && typeof column !== 'undefined') {\n      if (typeof line === 'number') {\n        this.line = line\n        this.column = column\n      } else {\n        this.line = line.line\n        this.column = line.column\n        this.endLine = column.line\n        this.endColumn = column.column\n      }\n    }\n\n    this.setMessage()\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, CssSyntaxError)\n    }\n  }\n\n  setMessage() {\n    this.message = this.plugin ? this.plugin + ': ' : ''\n    this.message += this.file ? this.file : '<css input>'\n    if (typeof this.line !== 'undefined') {\n      this.message += ':' + this.line + ':' + this.column\n    }\n    this.message += ': ' + this.reason\n  }\n\n  showSourceCode(color) {\n    if (!this.source) return ''\n\n    let css = this.source\n    if (color == null) color = pico.isColorSupported\n\n    let aside = text => text\n    let mark = text => text\n    let highlight = text => text\n    if (color) {\n      let { bold, gray, red } = pico.createColors(true)\n      mark = text => bold(red(text))\n      aside = text => gray(text)\n      if (terminalHighlight) {\n        highlight = text => terminalHighlight(text)\n      }\n    }\n\n    let lines = css.split(/\\r?\\n/)\n    let start = Math.max(this.line - 3, 0)\n    let end = Math.min(this.line + 2, lines.length)\n    let maxWidth = String(end).length\n\n    return lines\n      .slice(start, end)\n      .map((line, index) => {\n        let number = start + 1 + index\n        let gutter = ' ' + (' ' + number).slice(-maxWidth) + ' | '\n        if (number === this.line) {\n          if (line.length > 160) {\n            let padding = 20\n            let subLineStart = Math.max(0, this.column - padding)\n            let subLineEnd = Math.max(\n              this.column + padding,\n              this.endColumn + padding\n            )\n            let subLine = line.slice(subLineStart, subLineEnd)\n\n            let spacing =\n              aside(gutter.replace(/\\d/g, ' ')) +\n              line\n                .slice(0, Math.min(this.column - 1, padding - 1))\n                .replace(/[^\\t]/g, ' ')\n\n            return (\n              mark('>') +\n              aside(gutter) +\n              highlight(subLine) +\n              '\\n ' +\n              spacing +\n              mark('^')\n            )\n          }\n\n          let spacing =\n            aside(gutter.replace(/\\d/g, ' ')) +\n            line.slice(0, this.column - 1).replace(/[^\\t]/g, ' ')\n\n          return (\n            mark('>') +\n            aside(gutter) +\n            highlight(line) +\n            '\\n ' +\n            spacing +\n            mark('^')\n          )\n        }\n\n        return ' ' + aside(gutter) + highlight(line)\n      })\n      .join('\\n')\n  }\n\n  toString() {\n    let code = this.showSourceCode()\n    if (code) {\n      code = '\\n\\n' + code + '\\n'\n    }\n    return this.name + ': ' + this.message + code\n  }\n}\n\nmodule.exports = CssSyntaxError\nCssSyntaxError.default = CssSyntaxError\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,IAAI;AAEJ,MAAM,uBAAuB;IAC3B,YAAY,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAE;QACvD,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,MAAM;YACR,IAAI,CAAC,IAAI,GAAG;QACd;QACA,IAAI,QAAQ;YACV,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,QAAQ;YACV,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,OAAO,SAAS,eAAe,OAAO,WAAW,aAAa;YAChE,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,CAAC,IAAI,GAAG;gBACZ,IAAI,CAAC,MAAM,GAAG;YAChB,OAAO;gBACL,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;gBACrB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;gBACzB,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI;gBAC1B,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM;YAChC;QACF;QAEA,IAAI,CAAC,UAAU;QAEf,IAAI,MAAM,iBAAiB,EAAE;YAC3B,MAAM,iBAAiB,CAAC,IAAI,EAAE;QAChC;IACF;IAEA,aAAa;QACX,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,OAAO;QAClD,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;QACxC,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa;YACpC,IAAI,CAAC,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM;QACrD;QACA,IAAI,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,MAAM;IACpC;IAEA,eAAe,KAAK,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QAEzB,IAAI,MAAM,IAAI,CAAC,MAAM;QACrB,IAAI,SAAS,MAAM,QAAQ,KAAK,gBAAgB;QAEhD,IAAI,QAAQ,CAAA,OAAQ;QACpB,IAAI,OAAO,CAAA,OAAQ;QACnB,IAAI,YAAY,CAAA,OAAQ;QACxB,IAAI,OAAO;YACT,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,KAAK,YAAY,CAAC;YAC5C,OAAO,CAAA,OAAQ,KAAK,IAAI;YACxB,QAAQ,CAAA,OAAQ,KAAK;YACrB,IAAI,mBAAmB;gBACrB,YAAY,CAAA,OAAQ,kBAAkB;YACxC;QACF;QAEA,IAAI,QAAQ,IAAI,KAAK,CAAC;QACtB,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG;QACpC,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,MAAM,MAAM;QAC9C,IAAI,WAAW,OAAO,KAAK,MAAM;QAEjC,OAAO,MACJ,KAAK,CAAC,OAAO,KACb,GAAG,CAAC,CAAC,MAAM;YACV,IAAI,SAAS,QAAQ,IAAI;YACzB,IAAI,SAAS,MAAM,CAAC,MAAM,MAAM,EAAE,KAAK,CAAC,CAAC,YAAY;YACrD,IAAI,WAAW,IAAI,CAAC,IAAI,EAAE;gBACxB,IAAI,KAAK,MAAM,GAAG,KAAK;oBACrB,IAAI,UAAU;oBACd,IAAI,eAAe,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG;oBAC7C,IAAI,aAAa,KAAK,GAAG,CACvB,IAAI,CAAC,MAAM,GAAG,SACd,IAAI,CAAC,SAAS,GAAG;oBAEnB,IAAI,UAAU,KAAK,KAAK,CAAC,cAAc;oBAEvC,IAAI,UACF,MAAM,OAAO,OAAO,CAAC,OAAO,QAC5B,KACG,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,UAAU,IAC7C,OAAO,CAAC,UAAU;oBAEvB,OACE,KAAK,OACL,MAAM,UACN,UAAU,WACV,QACA,UACA,KAAK;gBAET;gBAEA,IAAI,UACF,MAAM,OAAO,OAAO,CAAC,OAAO,QAC5B,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU;gBAEnD,OACE,KAAK,OACL,MAAM,UACN,UAAU,QACV,QACA,UACA,KAAK;YAET;YAEA,OAAO,MAAM,MAAM,UAAU,UAAU;QACzC,GACC,IAAI,CAAC;IACV;IAEA,WAAW;QACT,IAAI,OAAO,IAAI,CAAC,cAAc;QAC9B,IAAI,MAAM;YACR,OAAO,SAAS,OAAO;QACzB;QACA,OAAO,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,CAAC,OAAO,GAAG;IAC3C;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,eAAe,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/stringifier.js"], "sourcesContent": ["'use strict'\n\nconst DEFAULT_RAW = {\n  after: '\\n',\n  beforeClose: '\\n',\n  beforeComment: '\\n',\n  beforeDecl: '\\n',\n  beforeOpen: ' ',\n  beforeRule: '\\n',\n  colon: ': ',\n  commentLeft: ' ',\n  commentRight: ' ',\n  emptyBody: '',\n  indent: '    ',\n  semicolon: false\n}\n\nfunction capitalize(str) {\n  return str[0].toUpperCase() + str.slice(1)\n}\n\nclass Stringifier {\n  constructor(builder) {\n    this.builder = builder\n  }\n\n  atrule(node, semicolon) {\n    let name = '@' + node.name\n    let params = node.params ? this.rawValue(node, 'params') : ''\n\n    if (typeof node.raws.afterName !== 'undefined') {\n      name += node.raws.afterName\n    } else if (params) {\n      name += ' '\n    }\n\n    if (node.nodes) {\n      this.block(node, name + params)\n    } else {\n      let end = (node.raws.between || '') + (semicolon ? ';' : '')\n      this.builder(name + params + end, node)\n    }\n  }\n\n  beforeAfter(node, detect) {\n    let value\n    if (node.type === 'decl') {\n      value = this.raw(node, null, 'beforeDecl')\n    } else if (node.type === 'comment') {\n      value = this.raw(node, null, 'beforeComment')\n    } else if (detect === 'before') {\n      value = this.raw(node, null, 'beforeRule')\n    } else {\n      value = this.raw(node, null, 'beforeClose')\n    }\n\n    let buf = node.parent\n    let depth = 0\n    while (buf && buf.type !== 'root') {\n      depth += 1\n      buf = buf.parent\n    }\n\n    if (value.includes('\\n')) {\n      let indent = this.raw(node, null, 'indent')\n      if (indent.length) {\n        for (let step = 0; step < depth; step++) value += indent\n      }\n    }\n\n    return value\n  }\n\n  block(node, start) {\n    let between = this.raw(node, 'between', 'beforeOpen')\n    this.builder(start + between + '{', node, 'start')\n\n    let after\n    if (node.nodes && node.nodes.length) {\n      this.body(node)\n      after = this.raw(node, 'after')\n    } else {\n      after = this.raw(node, 'after', 'emptyBody')\n    }\n\n    if (after) this.builder(after)\n    this.builder('}', node, 'end')\n  }\n\n  body(node) {\n    let last = node.nodes.length - 1\n    while (last > 0) {\n      if (node.nodes[last].type !== 'comment') break\n      last -= 1\n    }\n\n    let semicolon = this.raw(node, 'semicolon')\n    for (let i = 0; i < node.nodes.length; i++) {\n      let child = node.nodes[i]\n      let before = this.raw(child, 'before')\n      if (before) this.builder(before)\n      this.stringify(child, last !== i || semicolon)\n    }\n  }\n\n  comment(node) {\n    let left = this.raw(node, 'left', 'commentLeft')\n    let right = this.raw(node, 'right', 'commentRight')\n    this.builder('/*' + left + node.text + right + '*/', node)\n  }\n\n  decl(node, semicolon) {\n    let between = this.raw(node, 'between', 'colon')\n    let string = node.prop + between + this.rawValue(node, 'value')\n\n    if (node.important) {\n      string += node.raws.important || ' !important'\n    }\n\n    if (semicolon) string += ';'\n    this.builder(string, node)\n  }\n\n  document(node) {\n    this.body(node)\n  }\n\n  raw(node, own, detect) {\n    let value\n    if (!detect) detect = own\n\n    // Already had\n    if (own) {\n      value = node.raws[own]\n      if (typeof value !== 'undefined') return value\n    }\n\n    let parent = node.parent\n\n    if (detect === 'before') {\n      // Hack for first rule in CSS\n      if (!parent || (parent.type === 'root' && parent.first === node)) {\n        return ''\n      }\n\n      // `root` nodes in `document` should use only their own raws\n      if (parent && parent.type === 'document') {\n        return ''\n      }\n    }\n\n    // Floating child without parent\n    if (!parent) return DEFAULT_RAW[detect]\n\n    // Detect style by other nodes\n    let root = node.root()\n    if (!root.rawCache) root.rawCache = {}\n    if (typeof root.rawCache[detect] !== 'undefined') {\n      return root.rawCache[detect]\n    }\n\n    if (detect === 'before' || detect === 'after') {\n      return this.beforeAfter(node, detect)\n    } else {\n      let method = 'raw' + capitalize(detect)\n      if (this[method]) {\n        value = this[method](root, node)\n      } else {\n        root.walk(i => {\n          value = i.raws[own]\n          if (typeof value !== 'undefined') return false\n        })\n      }\n    }\n\n    if (typeof value === 'undefined') value = DEFAULT_RAW[detect]\n\n    root.rawCache[detect] = value\n    return value\n  }\n\n  rawBeforeClose(root) {\n    let value\n    root.walk(i => {\n      if (i.nodes && i.nodes.length > 0) {\n        if (typeof i.raws.after !== 'undefined') {\n          value = i.raws.after\n          if (value.includes('\\n')) {\n            value = value.replace(/[^\\n]+$/, '')\n          }\n          return false\n        }\n      }\n    })\n    if (value) value = value.replace(/\\S/g, '')\n    return value\n  }\n\n  rawBeforeComment(root, node) {\n    let value\n    root.walkComments(i => {\n      if (typeof i.raws.before !== 'undefined') {\n        value = i.raws.before\n        if (value.includes('\\n')) {\n          value = value.replace(/[^\\n]+$/, '')\n        }\n        return false\n      }\n    })\n    if (typeof value === 'undefined') {\n      value = this.raw(node, null, 'beforeDecl')\n    } else if (value) {\n      value = value.replace(/\\S/g, '')\n    }\n    return value\n  }\n\n  rawBeforeDecl(root, node) {\n    let value\n    root.walkDecls(i => {\n      if (typeof i.raws.before !== 'undefined') {\n        value = i.raws.before\n        if (value.includes('\\n')) {\n          value = value.replace(/[^\\n]+$/, '')\n        }\n        return false\n      }\n    })\n    if (typeof value === 'undefined') {\n      value = this.raw(node, null, 'beforeRule')\n    } else if (value) {\n      value = value.replace(/\\S/g, '')\n    }\n    return value\n  }\n\n  rawBeforeOpen(root) {\n    let value\n    root.walk(i => {\n      if (i.type !== 'decl') {\n        value = i.raws.between\n        if (typeof value !== 'undefined') return false\n      }\n    })\n    return value\n  }\n\n  rawBeforeRule(root) {\n    let value\n    root.walk(i => {\n      if (i.nodes && (i.parent !== root || root.first !== i)) {\n        if (typeof i.raws.before !== 'undefined') {\n          value = i.raws.before\n          if (value.includes('\\n')) {\n            value = value.replace(/[^\\n]+$/, '')\n          }\n          return false\n        }\n      }\n    })\n    if (value) value = value.replace(/\\S/g, '')\n    return value\n  }\n\n  rawColon(root) {\n    let value\n    root.walkDecls(i => {\n      if (typeof i.raws.between !== 'undefined') {\n        value = i.raws.between.replace(/[^\\s:]/g, '')\n        return false\n      }\n    })\n    return value\n  }\n\n  rawEmptyBody(root) {\n    let value\n    root.walk(i => {\n      if (i.nodes && i.nodes.length === 0) {\n        value = i.raws.after\n        if (typeof value !== 'undefined') return false\n      }\n    })\n    return value\n  }\n\n  rawIndent(root) {\n    if (root.raws.indent) return root.raws.indent\n    let value\n    root.walk(i => {\n      let p = i.parent\n      if (p && p !== root && p.parent && p.parent === root) {\n        if (typeof i.raws.before !== 'undefined') {\n          let parts = i.raws.before.split('\\n')\n          value = parts[parts.length - 1]\n          value = value.replace(/\\S/g, '')\n          return false\n        }\n      }\n    })\n    return value\n  }\n\n  rawSemicolon(root) {\n    let value\n    root.walk(i => {\n      if (i.nodes && i.nodes.length && i.last.type === 'decl') {\n        value = i.raws.semicolon\n        if (typeof value !== 'undefined') return false\n      }\n    })\n    return value\n  }\n\n  rawValue(node, prop) {\n    let value = node[prop]\n    let raw = node.raws[prop]\n    if (raw && raw.value === value) {\n      return raw.raw\n    }\n\n    return value\n  }\n\n  root(node) {\n    this.body(node)\n    if (node.raws.after) this.builder(node.raws.after)\n  }\n\n  rule(node) {\n    this.block(node, this.rawValue(node, 'selector'))\n    if (node.raws.ownSemicolon) {\n      this.builder(node.raws.ownSemicolon, node, 'end')\n    }\n  }\n\n  stringify(node, semicolon) {\n    /* c8 ignore start */\n    if (!this[node.type]) {\n      throw new Error(\n        'Unknown AST node type ' +\n          node.type +\n          '. ' +\n          'Maybe you need to change PostCSS stringifier.'\n      )\n    }\n    /* c8 ignore stop */\n    this[node.type](node, semicolon)\n  }\n}\n\nmodule.exports = Stringifier\nStringifier.default = Stringifier\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,cAAc;IAClB,OAAO;IACP,aAAa;IACb,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,aAAa;IACb,cAAc;IACd,WAAW;IACX,QAAQ;IACR,WAAW;AACb;AAEA,SAAS,WAAW,GAAG;IACrB,OAAO,GAAG,CAAC,EAAE,CAAC,WAAW,KAAK,IAAI,KAAK,CAAC;AAC1C;AAEA,MAAM;IACJ,YAAY,OAAO,CAAE;QACnB,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,OAAO,IAAI,EAAE,SAAS,EAAE;QACtB,IAAI,OAAO,MAAM,KAAK,IAAI;QAC1B,IAAI,SAAS,KAAK,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,YAAY;QAE3D,IAAI,OAAO,KAAK,IAAI,CAAC,SAAS,KAAK,aAAa;YAC9C,QAAQ,KAAK,IAAI,CAAC,SAAS;QAC7B,OAAO,IAAI,QAAQ;YACjB,QAAQ;QACV;QAEA,IAAI,KAAK,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,MAAM,OAAO;QAC1B,OAAO;YACL,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC,YAAY,MAAM,EAAE;YAC3D,IAAI,CAAC,OAAO,CAAC,OAAO,SAAS,KAAK;QACpC;IACF;IAEA,YAAY,IAAI,EAAE,MAAM,EAAE;QACxB,IAAI;QACJ,IAAI,KAAK,IAAI,KAAK,QAAQ;YACxB,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;QAC/B,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;YAClC,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;QAC/B,OAAO,IAAI,WAAW,UAAU;YAC9B,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;QAC/B,OAAO;YACL,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;QAC/B;QAEA,IAAI,MAAM,KAAK,MAAM;QACrB,IAAI,QAAQ;QACZ,MAAO,OAAO,IAAI,IAAI,KAAK,OAAQ;YACjC,SAAS;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,MAAM,QAAQ,CAAC,OAAO;YACxB,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;YAClC,IAAI,OAAO,MAAM,EAAE;gBACjB,IAAK,IAAI,OAAO,GAAG,OAAO,OAAO,OAAQ,SAAS;YACpD;QACF;QAEA,OAAO;IACT;IAEA,MAAM,IAAI,EAAE,KAAK,EAAE;QACjB,IAAI,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM,WAAW;QACxC,IAAI,CAAC,OAAO,CAAC,QAAQ,UAAU,KAAK,MAAM;QAE1C,IAAI;QACJ,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC;YACV,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM;QACzB,OAAO;YACL,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,SAAS;QAClC;QAEA,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,KAAK,MAAM;IAC1B;IAEA,KAAK,IAAI,EAAE;QACT,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM,GAAG;QAC/B,MAAO,OAAO,EAAG;YACf,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,WAAW;YACzC,QAAQ;QACV;QAEA,IAAI,YAAY,IAAI,CAAC,GAAG,CAAC,MAAM;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,IAAK;YAC1C,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE;YACzB,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,OAAO;YAC7B,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,OAAO,SAAS,KAAK;QACtC;IACF;IAEA,QAAQ,IAAI,EAAE;QACZ,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,QAAQ;QAClC,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,SAAS;QACpC,IAAI,CAAC,OAAO,CAAC,OAAO,OAAO,KAAK,IAAI,GAAG,QAAQ,MAAM;IACvD;IAEA,KAAK,IAAI,EAAE,SAAS,EAAE;QACpB,IAAI,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM,WAAW;QACxC,IAAI,SAAS,KAAK,IAAI,GAAG,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM;QAEvD,IAAI,KAAK,SAAS,EAAE;YAClB,UAAU,KAAK,IAAI,CAAC,SAAS,IAAI;QACnC;QAEA,IAAI,WAAW,UAAU;QACzB,IAAI,CAAC,OAAO,CAAC,QAAQ;IACvB;IAEA,SAAS,IAAI,EAAE;QACb,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA,IAAI,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE;QACrB,IAAI;QACJ,IAAI,CAAC,QAAQ,SAAS;QAEtB,cAAc;QACd,IAAI,KAAK;YACP,QAAQ,KAAK,IAAI,CAAC,IAAI;YACtB,IAAI,OAAO,UAAU,aAAa,OAAO;QAC3C;QAEA,IAAI,SAAS,KAAK,MAAM;QAExB,IAAI,WAAW,UAAU;YACvB,6BAA6B;YAC7B,IAAI,CAAC,UAAW,OAAO,IAAI,KAAK,UAAU,OAAO,KAAK,KAAK,MAAO;gBAChE,OAAO;YACT;YAEA,4DAA4D;YAC5D,IAAI,UAAU,OAAO,IAAI,KAAK,YAAY;gBACxC,OAAO;YACT;QACF;QAEA,gCAAgC;QAChC,IAAI,CAAC,QAAQ,OAAO,WAAW,CAAC,OAAO;QAEvC,8BAA8B;QAC9B,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,CAAC,KAAK,QAAQ,EAAE,KAAK,QAAQ,GAAG,CAAC;QACrC,IAAI,OAAO,KAAK,QAAQ,CAAC,OAAO,KAAK,aAAa;YAChD,OAAO,KAAK,QAAQ,CAAC,OAAO;QAC9B;QAEA,IAAI,WAAW,YAAY,WAAW,SAAS;YAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;QAChC,OAAO;YACL,IAAI,SAAS,QAAQ,WAAW;YAChC,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM;YAC7B,OAAO;gBACL,KAAK,IAAI,CAAC,CAAA;oBACR,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,IAAI,OAAO,UAAU,aAAa,OAAO;gBAC3C;YACF;QACF;QAEA,IAAI,OAAO,UAAU,aAAa,QAAQ,WAAW,CAAC,OAAO;QAE7D,KAAK,QAAQ,CAAC,OAAO,GAAG;QACxB,OAAO;IACT;IAEA,eAAe,IAAI,EAAE;QACnB,IAAI;QACJ,KAAK,IAAI,CAAC,CAAA;YACR,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,MAAM,GAAG,GAAG;gBACjC,IAAI,OAAO,EAAE,IAAI,CAAC,KAAK,KAAK,aAAa;oBACvC,QAAQ,EAAE,IAAI,CAAC,KAAK;oBACpB,IAAI,MAAM,QAAQ,CAAC,OAAO;wBACxB,QAAQ,MAAM,OAAO,CAAC,WAAW;oBACnC;oBACA,OAAO;gBACT;YACF;QACF;QACA,IAAI,OAAO,QAAQ,MAAM,OAAO,CAAC,OAAO;QACxC,OAAO;IACT;IAEA,iBAAiB,IAAI,EAAE,IAAI,EAAE;QAC3B,IAAI;QACJ,KAAK,YAAY,CAAC,CAAA;YAChB,IAAI,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,aAAa;gBACxC,QAAQ,EAAE,IAAI,CAAC,MAAM;gBACrB,IAAI,MAAM,QAAQ,CAAC,OAAO;oBACxB,QAAQ,MAAM,OAAO,CAAC,WAAW;gBACnC;gBACA,OAAO;YACT;QACF;QACA,IAAI,OAAO,UAAU,aAAa;YAChC,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;QAC/B,OAAO,IAAI,OAAO;YAChB,QAAQ,MAAM,OAAO,CAAC,OAAO;QAC/B;QACA,OAAO;IACT;IAEA,cAAc,IAAI,EAAE,IAAI,EAAE;QACxB,IAAI;QACJ,KAAK,SAAS,CAAC,CAAA;YACb,IAAI,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,aAAa;gBACxC,QAAQ,EAAE,IAAI,CAAC,MAAM;gBACrB,IAAI,MAAM,QAAQ,CAAC,OAAO;oBACxB,QAAQ,MAAM,OAAO,CAAC,WAAW;gBACnC;gBACA,OAAO;YACT;QACF;QACA,IAAI,OAAO,UAAU,aAAa;YAChC,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;QAC/B,OAAO,IAAI,OAAO;YAChB,QAAQ,MAAM,OAAO,CAAC,OAAO;QAC/B;QACA,OAAO;IACT;IAEA,cAAc,IAAI,EAAE;QAClB,IAAI;QACJ,KAAK,IAAI,CAAC,CAAA;YACR,IAAI,EAAE,IAAI,KAAK,QAAQ;gBACrB,QAAQ,EAAE,IAAI,CAAC,OAAO;gBACtB,IAAI,OAAO,UAAU,aAAa,OAAO;YAC3C;QACF;QACA,OAAO;IACT;IAEA,cAAc,IAAI,EAAE;QAClB,IAAI;QACJ,KAAK,IAAI,CAAC,CAAA;YACR,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,MAAM,KAAK,QAAQ,KAAK,KAAK,KAAK,CAAC,GAAG;gBACtD,IAAI,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,aAAa;oBACxC,QAAQ,EAAE,IAAI,CAAC,MAAM;oBACrB,IAAI,MAAM,QAAQ,CAAC,OAAO;wBACxB,QAAQ,MAAM,OAAO,CAAC,WAAW;oBACnC;oBACA,OAAO;gBACT;YACF;QACF;QACA,IAAI,OAAO,QAAQ,MAAM,OAAO,CAAC,OAAO;QACxC,OAAO;IACT;IAEA,SAAS,IAAI,EAAE;QACb,IAAI;QACJ,KAAK,SAAS,CAAC,CAAA;YACb,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,aAAa;gBACzC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW;gBAC1C,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,aAAa,IAAI,EAAE;QACjB,IAAI;QACJ,KAAK,IAAI,CAAC,CAAA;YACR,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,MAAM,KAAK,GAAG;gBACnC,QAAQ,EAAE,IAAI,CAAC,KAAK;gBACpB,IAAI,OAAO,UAAU,aAAa,OAAO;YAC3C;QACF;QACA,OAAO;IACT;IAEA,UAAU,IAAI,EAAE;QACd,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,CAAC,MAAM;QAC7C,IAAI;QACJ,KAAK,IAAI,CAAC,CAAA;YACR,IAAI,IAAI,EAAE,MAAM;YAChB,IAAI,KAAK,MAAM,QAAQ,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK,MAAM;gBACpD,IAAI,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,aAAa;oBACxC,IAAI,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;oBAChC,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;oBAC/B,QAAQ,MAAM,OAAO,CAAC,OAAO;oBAC7B,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,aAAa,IAAI,EAAE;QACjB,IAAI;QACJ,KAAK,IAAI,CAAC,CAAA;YACR,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ;gBACvD,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,IAAI,OAAO,UAAU,aAAa,OAAO;YAC3C;QACF;QACA,OAAO;IACT;IAEA,SAAS,IAAI,EAAE,IAAI,EAAE;QACnB,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,MAAM,KAAK,IAAI,CAAC,KAAK;QACzB,IAAI,OAAO,IAAI,KAAK,KAAK,OAAO;YAC9B,OAAO,IAAI,GAAG;QAChB;QAEA,OAAO;IACT;IAEA,KAAK,IAAI,EAAE;QACT,IAAI,CAAC,IAAI,CAAC;QACV,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,KAAK;IACnD;IAEA,KAAK,IAAI,EAAE;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM;QACrC,IAAI,KAAK,IAAI,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE,MAAM;QAC7C;IACF;IAEA,UAAU,IAAI,EAAE,SAAS,EAAE;QACzB,mBAAmB,GACnB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE;YACpB,MAAM,IAAI,MACR,2BACE,KAAK,IAAI,GACT,OACA;QAEN;QACA,kBAAkB,GAClB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM;IACxB;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,YAAY,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/stringify.js"], "sourcesContent": ["'use strict'\n\nlet Stringifier = require('./stringifier')\n\nfunction stringify(node, builder) {\n  let str = new Stringifier(builder)\n  str.stringify(node)\n}\n\nmodule.exports = stringify\nstringify.default = stringify\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,SAAS,UAAU,IAAI,EAAE,OAAO;IAC9B,IAAI,MAAM,IAAI,YAAY;IAC1B,IAAI,SAAS,CAAC;AAChB;AAEA,OAAO,OAAO,GAAG;AACjB,UAAU,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/symbols.js"], "sourcesContent": ["'use strict'\n\nmodule.exports.isClean = Symbol('isClean')\n\nmodule.exports.my = Symbol('my')\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,CAAC,OAAO,GAAG,OAAO;AAEhC,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO", "ignoreList": [0]}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/node.js"], "sourcesContent": ["'use strict'\n\nlet CssSyntaxError = require('./css-syntax-error')\nlet Stringifier = require('./stringifier')\nlet stringify = require('./stringify')\nlet { isClean, my } = require('./symbols')\n\nfunction cloneNode(obj, parent) {\n  let cloned = new obj.constructor()\n\n  for (let i in obj) {\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) {\n      /* c8 ignore next 2 */\n      continue\n    }\n    if (i === 'proxyCache') continue\n    let value = obj[i]\n    let type = typeof value\n\n    if (i === 'parent' && type === 'object') {\n      if (parent) cloned[i] = parent\n    } else if (i === 'source') {\n      cloned[i] = value\n    } else if (Array.isArray(value)) {\n      cloned[i] = value.map(j => cloneNode(j, cloned))\n    } else {\n      if (type === 'object' && value !== null) value = cloneNode(value)\n      cloned[i] = value\n    }\n  }\n\n  return cloned\n}\n\nfunction sourceOffset(inputCSS, position) {\n  // Not all custom syntaxes support `offset` in `source.start` and `source.end`\n  if (position && typeof position.offset !== 'undefined') {\n    return position.offset\n  }\n\n  let column = 1\n  let line = 1\n  let offset = 0\n\n  for (let i = 0; i < inputCSS.length; i++) {\n    if (line === position.line && column === position.column) {\n      offset = i\n      break\n    }\n\n    if (inputCSS[i] === '\\n') {\n      column = 1\n      line += 1\n    } else {\n      column += 1\n    }\n  }\n\n  return offset\n}\n\nclass Node {\n  get proxyOf() {\n    return this\n  }\n\n  constructor(defaults = {}) {\n    this.raws = {}\n    this[isClean] = false\n    this[my] = true\n\n    for (let name in defaults) {\n      if (name === 'nodes') {\n        this.nodes = []\n        for (let node of defaults[name]) {\n          if (typeof node.clone === 'function') {\n            this.append(node.clone())\n          } else {\n            this.append(node)\n          }\n        }\n      } else {\n        this[name] = defaults[name]\n      }\n    }\n  }\n\n  addToError(error) {\n    error.postcssNode = this\n    if (error.stack && this.source && /\\n\\s{4}at /.test(error.stack)) {\n      let s = this.source\n      error.stack = error.stack.replace(\n        /\\n\\s{4}at /,\n        `$&${s.input.from}:${s.start.line}:${s.start.column}$&`\n      )\n    }\n    return error\n  }\n\n  after(add) {\n    this.parent.insertAfter(this, add)\n    return this\n  }\n\n  assign(overrides = {}) {\n    for (let name in overrides) {\n      this[name] = overrides[name]\n    }\n    return this\n  }\n\n  before(add) {\n    this.parent.insertBefore(this, add)\n    return this\n  }\n\n  cleanRaws(keepBetween) {\n    delete this.raws.before\n    delete this.raws.after\n    if (!keepBetween) delete this.raws.between\n  }\n\n  clone(overrides = {}) {\n    let cloned = cloneNode(this)\n    for (let name in overrides) {\n      cloned[name] = overrides[name]\n    }\n    return cloned\n  }\n\n  cloneAfter(overrides = {}) {\n    let cloned = this.clone(overrides)\n    this.parent.insertAfter(this, cloned)\n    return cloned\n  }\n\n  cloneBefore(overrides = {}) {\n    let cloned = this.clone(overrides)\n    this.parent.insertBefore(this, cloned)\n    return cloned\n  }\n\n  error(message, opts = {}) {\n    if (this.source) {\n      let { end, start } = this.rangeBy(opts)\n      return this.source.input.error(\n        message,\n        { column: start.column, line: start.line },\n        { column: end.column, line: end.line },\n        opts\n      )\n    }\n    return new CssSyntaxError(message)\n  }\n\n  getProxyProcessor() {\n    return {\n      get(node, prop) {\n        if (prop === 'proxyOf') {\n          return node\n        } else if (prop === 'root') {\n          return () => node.root().toProxy()\n        } else {\n          return node[prop]\n        }\n      },\n\n      set(node, prop, value) {\n        if (node[prop] === value) return true\n        node[prop] = value\n        if (\n          prop === 'prop' ||\n          prop === 'value' ||\n          prop === 'name' ||\n          prop === 'params' ||\n          prop === 'important' ||\n          /* c8 ignore next */\n          prop === 'text'\n        ) {\n          node.markDirty()\n        }\n        return true\n      }\n    }\n  }\n\n  /* c8 ignore next 3 */\n  markClean() {\n    this[isClean] = true\n  }\n\n  markDirty() {\n    if (this[isClean]) {\n      this[isClean] = false\n      let next = this\n      while ((next = next.parent)) {\n        next[isClean] = false\n      }\n    }\n  }\n\n  next() {\n    if (!this.parent) return undefined\n    let index = this.parent.index(this)\n    return this.parent.nodes[index + 1]\n  }\n\n  positionBy(opts = {}) {\n    let pos = this.source.start\n    if (opts.index) {\n      pos = this.positionInside(opts.index)\n    } else if (opts.word) {\n      let inputString =\n        'document' in this.source.input\n          ? this.source.input.document\n          : this.source.input.css\n      let stringRepresentation = inputString.slice(\n        sourceOffset(inputString, this.source.start),\n        sourceOffset(inputString, this.source.end)\n      )\n      let index = stringRepresentation.indexOf(opts.word)\n      if (index !== -1) pos = this.positionInside(index)\n    }\n    return pos\n  }\n\n  positionInside(index) {\n    let column = this.source.start.column\n    let line = this.source.start.line\n    let inputString =\n      'document' in this.source.input\n        ? this.source.input.document\n        : this.source.input.css\n    let offset = sourceOffset(inputString, this.source.start)\n    let end = offset + index\n\n    for (let i = offset; i < end; i++) {\n      if (inputString[i] === '\\n') {\n        column = 1\n        line += 1\n      } else {\n        column += 1\n      }\n    }\n\n    return { column, line, offset: end }\n  }\n\n  prev() {\n    if (!this.parent) return undefined\n    let index = this.parent.index(this)\n    return this.parent.nodes[index - 1]\n  }\n\n  rangeBy(opts = {}) {\n    let inputString =\n      'document' in this.source.input\n        ? this.source.input.document\n        : this.source.input.css\n    let start = {\n      column: this.source.start.column,\n      line: this.source.start.line,\n      offset: sourceOffset(inputString, this.source.start)\n    }\n    let end = this.source.end\n      ? {\n          column: this.source.end.column + 1,\n          line: this.source.end.line,\n          offset:\n            typeof this.source.end.offset === 'number'\n              ? // `source.end.offset` is exclusive, so we don't need to add 1\n                this.source.end.offset\n              : // Since line/column in this.source.end is inclusive,\n                // the `sourceOffset(... , this.source.end)` returns an inclusive offset.\n                // So, we add 1 to convert it to exclusive.\n                sourceOffset(inputString, this.source.end) + 1\n        }\n      : {\n          column: start.column + 1,\n          line: start.line,\n          offset: start.offset + 1\n        }\n\n    if (opts.word) {\n      let stringRepresentation = inputString.slice(\n        sourceOffset(inputString, this.source.start),\n        sourceOffset(inputString, this.source.end)\n      )\n      let index = stringRepresentation.indexOf(opts.word)\n      if (index !== -1) {\n        start = this.positionInside(index)\n        end = this.positionInside(index + opts.word.length)\n      }\n    } else {\n      if (opts.start) {\n        start = {\n          column: opts.start.column,\n          line: opts.start.line,\n          offset: sourceOffset(inputString, opts.start)\n        }\n      } else if (opts.index) {\n        start = this.positionInside(opts.index)\n      }\n\n      if (opts.end) {\n        end = {\n          column: opts.end.column,\n          line: opts.end.line,\n          offset: sourceOffset(inputString, opts.end)\n        }\n      } else if (typeof opts.endIndex === 'number') {\n        end = this.positionInside(opts.endIndex)\n      } else if (opts.index) {\n        end = this.positionInside(opts.index + 1)\n      }\n    }\n\n    if (\n      end.line < start.line ||\n      (end.line === start.line && end.column <= start.column)\n    ) {\n      end = {\n        column: start.column + 1,\n        line: start.line,\n        offset: start.offset + 1\n      }\n    }\n\n    return { end, start }\n  }\n\n  raw(prop, defaultType) {\n    let str = new Stringifier()\n    return str.raw(this, prop, defaultType)\n  }\n\n  remove() {\n    if (this.parent) {\n      this.parent.removeChild(this)\n    }\n    this.parent = undefined\n    return this\n  }\n\n  replaceWith(...nodes) {\n    if (this.parent) {\n      let bookmark = this\n      let foundSelf = false\n      for (let node of nodes) {\n        if (node === this) {\n          foundSelf = true\n        } else if (foundSelf) {\n          this.parent.insertAfter(bookmark, node)\n          bookmark = node\n        } else {\n          this.parent.insertBefore(bookmark, node)\n        }\n      }\n\n      if (!foundSelf) {\n        this.remove()\n      }\n    }\n\n    return this\n  }\n\n  root() {\n    let result = this\n    while (result.parent && result.parent.type !== 'document') {\n      result = result.parent\n    }\n    return result\n  }\n\n  toJSON(_, inputs) {\n    let fixed = {}\n    let emitInputs = inputs == null\n    inputs = inputs || new Map()\n    let inputsNextIndex = 0\n\n    for (let name in this) {\n      if (!Object.prototype.hasOwnProperty.call(this, name)) {\n        /* c8 ignore next 2 */\n        continue\n      }\n      if (name === 'parent' || name === 'proxyCache') continue\n      let value = this[name]\n\n      if (Array.isArray(value)) {\n        fixed[name] = value.map(i => {\n          if (typeof i === 'object' && i.toJSON) {\n            return i.toJSON(null, inputs)\n          } else {\n            return i\n          }\n        })\n      } else if (typeof value === 'object' && value.toJSON) {\n        fixed[name] = value.toJSON(null, inputs)\n      } else if (name === 'source') {\n        if (value == null) continue\n        let inputId = inputs.get(value.input)\n        if (inputId == null) {\n          inputId = inputsNextIndex\n          inputs.set(value.input, inputsNextIndex)\n          inputsNextIndex++\n        }\n        fixed[name] = {\n          end: value.end,\n          inputId,\n          start: value.start\n        }\n      } else {\n        fixed[name] = value\n      }\n    }\n\n    if (emitInputs) {\n      fixed.inputs = [...inputs.keys()].map(input => input.toJSON())\n    }\n\n    return fixed\n  }\n\n  toProxy() {\n    if (!this.proxyCache) {\n      this.proxyCache = new Proxy(this, this.getProxyProcessor())\n    }\n    return this.proxyCache\n  }\n\n  toString(stringifier = stringify) {\n    if (stringifier.stringify) stringifier = stringifier.stringify\n    let result = ''\n    stringifier(this, i => {\n      result += i\n    })\n    return result\n  }\n\n  warn(result, text, opts = {}) {\n    let data = { node: this }\n    for (let i in opts) data[i] = opts[i]\n    return result.warn(text, data)\n  }\n}\n\nmodule.exports = Node\nNode.default = Node\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;AAEnB,SAAS,UAAU,GAAG,EAAE,MAAM;IAC5B,IAAI,SAAS,IAAI,IAAI,WAAW;IAEhC,IAAK,IAAI,KAAK,IAAK;QACjB,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,IAAI;YAEjD;QACF;QACA,IAAI,MAAM,cAAc;QACxB,IAAI,QAAQ,GAAG,CAAC,EAAE;QAClB,IAAI,OAAO,OAAO;QAElB,IAAI,MAAM,YAAY,SAAS,UAAU;YACvC,IAAI,QAAQ,MAAM,CAAC,EAAE,GAAG;QAC1B,OAAO,IAAI,MAAM,UAAU;YACzB,MAAM,CAAC,EAAE,GAAG;QACd,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;YAC/B,MAAM,CAAC,EAAE,GAAG,MAAM,GAAG,CAAC,CAAA,IAAK,UAAU,GAAG;QAC1C,OAAO;YACL,IAAI,SAAS,YAAY,UAAU,MAAM,QAAQ,UAAU;YAC3D,MAAM,CAAC,EAAE,GAAG;QACd;IACF;IAEA,OAAO;AACT;AAEA,SAAS,aAAa,QAAQ,EAAE,QAAQ;IACtC,8EAA8E;IAC9E,IAAI,YAAY,OAAO,SAAS,MAAM,KAAK,aAAa;QACtD,OAAO,SAAS,MAAM;IACxB;IAEA,IAAI,SAAS;IACb,IAAI,OAAO;IACX,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,SAAS,SAAS,IAAI,IAAI,WAAW,SAAS,MAAM,EAAE;YACxD,SAAS;YACT;QACF;QAEA,IAAI,QAAQ,CAAC,EAAE,KAAK,MAAM;YACxB,SAAS;YACT,QAAQ;QACV,OAAO;YACL,UAAU;QACZ;IACF;IAEA,OAAO;AACT;AAEA,MAAM;IACJ,IAAI,UAAU;QACZ,OAAO,IAAI;IACb;IAEA,YAAY,WAAW,CAAC,CAAC,CAAE;QACzB,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QAEX,IAAK,IAAI,QAAQ,SAAU;YACzB,IAAI,SAAS,SAAS;gBACpB,IAAI,CAAC,KAAK,GAAG,EAAE;gBACf,KAAK,IAAI,QAAQ,QAAQ,CAAC,KAAK,CAAE;oBAC/B,IAAI,OAAO,KAAK,KAAK,KAAK,YAAY;wBACpC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK;oBACxB,OAAO;wBACL,IAAI,CAAC,MAAM,CAAC;oBACd;gBACF;YACF,OAAO;gBACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;YAC7B;QACF;IACF;IAEA,WAAW,KAAK,EAAE;QAChB,MAAM,WAAW,GAAG,IAAI;QACxB,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,IAAI,aAAa,IAAI,CAAC,MAAM,KAAK,GAAG;YAChE,IAAI,IAAI,IAAI,CAAC,MAAM;YACnB,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,OAAO,CAC/B,cACA,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;QAE3D;QACA,OAAO;IACT;IAEA,MAAM,GAAG,EAAE;QACT,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE;QAC9B,OAAO,IAAI;IACb;IAEA,OAAO,YAAY,CAAC,CAAC,EAAE;QACrB,IAAK,IAAI,QAAQ,UAAW;YAC1B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QACA,OAAO,IAAI;IACb;IAEA,OAAO,GAAG,EAAE;QACV,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE;QAC/B,OAAO,IAAI;IACb;IAEA,UAAU,WAAW,EAAE;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;QACvB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;QACtB,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;IAC5C;IAEA,MAAM,YAAY,CAAC,CAAC,EAAE;QACpB,IAAI,SAAS,UAAU,IAAI;QAC3B,IAAK,IAAI,QAAQ,UAAW;YAC1B,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAChC;QACA,OAAO;IACT;IAEA,WAAW,YAAY,CAAC,CAAC,EAAE;QACzB,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE;QAC9B,OAAO;IACT;IAEA,YAAY,YAAY,CAAC,CAAC,EAAE;QAC1B,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE;QAC/B,OAAO;IACT;IAEA,MAAM,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YAClC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAC5B,SACA;gBAAE,QAAQ,MAAM,MAAM;gBAAE,MAAM,MAAM,IAAI;YAAC,GACzC;gBAAE,QAAQ,IAAI,MAAM;gBAAE,MAAM,IAAI,IAAI;YAAC,GACrC;QAEJ;QACA,OAAO,IAAI,eAAe;IAC5B;IAEA,oBAAoB;QAClB,OAAO;YACL,KAAI,IAAI,EAAE,IAAI;gBACZ,IAAI,SAAS,WAAW;oBACtB,OAAO;gBACT,OAAO,IAAI,SAAS,QAAQ;oBAC1B,OAAO,IAAM,KAAK,IAAI,GAAG,OAAO;gBAClC,OAAO;oBACL,OAAO,IAAI,CAAC,KAAK;gBACnB;YACF;YAEA,KAAI,IAAI,EAAE,IAAI,EAAE,KAAK;gBACnB,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,OAAO;gBACjC,IAAI,CAAC,KAAK,GAAG;gBACb,IACE,SAAS,UACT,SAAS,WACT,SAAS,UACT,SAAS,YACT,SAAS,eACT,kBAAkB,GAClB,SAAS,QACT;oBACA,KAAK,SAAS;gBAChB;gBACA,OAAO;YACT;QACF;IACF;IAEA,oBAAoB,GACpB,YAAY;QACV,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,YAAY;QACV,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,OAAO,IAAI;YACf,MAAQ,OAAO,KAAK,MAAM,CAAG;gBAC3B,IAAI,CAAC,QAAQ,GAAG;YAClB;QACF;IACF;IAEA,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QACzB,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;QAClC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;IACrC;IAEA,WAAW,OAAO,CAAC,CAAC,EAAE;QACpB,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK;QAC3B,IAAI,KAAK,KAAK,EAAE;YACd,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK;QACtC,OAAO,IAAI,KAAK,IAAI,EAAE;YACpB,IAAI,cACF,cAAc,IAAI,CAAC,MAAM,CAAC,KAAK,GAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;YAC3B,IAAI,uBAAuB,YAAY,KAAK,CAC1C,aAAa,aAAa,IAAI,CAAC,MAAM,CAAC,KAAK,GAC3C,aAAa,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG;YAE3C,IAAI,QAAQ,qBAAqB,OAAO,CAAC,KAAK,IAAI;YAClD,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC;QAC9C;QACA,OAAO;IACT;IAEA,eAAe,KAAK,EAAE;QACpB,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;QACrC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;QACjC,IAAI,cACF,cAAc,IAAI,CAAC,MAAM,CAAC,KAAK,GAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;QAC3B,IAAI,SAAS,aAAa,aAAa,IAAI,CAAC,MAAM,CAAC,KAAK;QACxD,IAAI,MAAM,SAAS;QAEnB,IAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,IAAK;YACjC,IAAI,WAAW,CAAC,EAAE,KAAK,MAAM;gBAC3B,SAAS;gBACT,QAAQ;YACV,OAAO;gBACL,UAAU;YACZ;QACF;QAEA,OAAO;YAAE;YAAQ;YAAM,QAAQ;QAAI;IACrC;IAEA,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QACzB,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;QAClC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;IACrC;IAEA,QAAQ,OAAO,CAAC,CAAC,EAAE;QACjB,IAAI,cACF,cAAc,IAAI,CAAC,MAAM,CAAC,KAAK,GAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;QAC3B,IAAI,QAAQ;YACV,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;YAChC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;YAC5B,QAAQ,aAAa,aAAa,IAAI,CAAC,MAAM,CAAC,KAAK;QACrD;QACA,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,GACrB;YACE,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG;YACjC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI;YAC1B,QACE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,KAAK,WAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAEtB,yEAAyE;YACzE,2CAA2C;YAC3C,aAAa,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;QACrD,IACA;YACE,QAAQ,MAAM,MAAM,GAAG;YACvB,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,MAAM,GAAG;QACzB;QAEJ,IAAI,KAAK,IAAI,EAAE;YACb,IAAI,uBAAuB,YAAY,KAAK,CAC1C,aAAa,aAAa,IAAI,CAAC,MAAM,CAAC,KAAK,GAC3C,aAAa,aAAa,IAAI,CAAC,MAAM,CAAC,GAAG;YAE3C,IAAI,QAAQ,qBAAqB,OAAO,CAAC,KAAK,IAAI;YAClD,IAAI,UAAU,CAAC,GAAG;gBAChB,QAAQ,IAAI,CAAC,cAAc,CAAC;gBAC5B,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM;YACpD;QACF,OAAO;YACL,IAAI,KAAK,KAAK,EAAE;gBACd,QAAQ;oBACN,QAAQ,KAAK,KAAK,CAAC,MAAM;oBACzB,MAAM,KAAK,KAAK,CAAC,IAAI;oBACrB,QAAQ,aAAa,aAAa,KAAK,KAAK;gBAC9C;YACF,OAAO,IAAI,KAAK,KAAK,EAAE;gBACrB,QAAQ,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK;YACxC;YAEA,IAAI,KAAK,GAAG,EAAE;gBACZ,MAAM;oBACJ,QAAQ,KAAK,GAAG,CAAC,MAAM;oBACvB,MAAM,KAAK,GAAG,CAAC,IAAI;oBACnB,QAAQ,aAAa,aAAa,KAAK,GAAG;gBAC5C;YACF,OAAO,IAAI,OAAO,KAAK,QAAQ,KAAK,UAAU;gBAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,QAAQ;YACzC,OAAO,IAAI,KAAK,KAAK,EAAE;gBACrB,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK,GAAG;YACzC;QACF;QAEA,IACE,IAAI,IAAI,GAAG,MAAM,IAAI,IACpB,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM,MAAM,EACtD;YACA,MAAM;gBACJ,QAAQ,MAAM,MAAM,GAAG;gBACvB,MAAM,MAAM,IAAI;gBAChB,QAAQ,MAAM,MAAM,GAAG;YACzB;QACF;QAEA,OAAO;YAAE;YAAK;QAAM;IACtB;IAEA,IAAI,IAAI,EAAE,WAAW,EAAE;QACrB,IAAI,MAAM,IAAI;QACd,OAAO,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM;IAC7B;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI;QAC9B;QACA,IAAI,CAAC,MAAM,GAAG;QACd,OAAO,IAAI;IACb;IAEA,YAAY,GAAG,KAAK,EAAE;QACpB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,WAAW,IAAI;YACnB,IAAI,YAAY;YAChB,KAAK,IAAI,QAAQ,MAAO;gBACtB,IAAI,SAAS,IAAI,EAAE;oBACjB,YAAY;gBACd,OAAO,IAAI,WAAW;oBACpB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU;oBAClC,WAAW;gBACb,OAAO;oBACL,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU;gBACrC;YACF;YAEA,IAAI,CAAC,WAAW;gBACd,IAAI,CAAC,MAAM;YACb;QACF;QAEA,OAAO,IAAI;IACb;IAEA,OAAO;QACL,IAAI,SAAS,IAAI;QACjB,MAAO,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,WAAY;YACzD,SAAS,OAAO,MAAM;QACxB;QACA,OAAO;IACT;IAEA,OAAO,CAAC,EAAE,MAAM,EAAE;QAChB,IAAI,QAAQ,CAAC;QACb,IAAI,aAAa,UAAU;QAC3B,SAAS,UAAU,IAAI;QACvB,IAAI,kBAAkB;QAEtB,IAAK,IAAI,QAAQ,IAAI,CAAE;YACrB,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;gBAErD;YACF;YACA,IAAI,SAAS,YAAY,SAAS,cAAc;YAChD,IAAI,QAAQ,IAAI,CAAC,KAAK;YAEtB,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,KAAK,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAA;oBACtB,IAAI,OAAO,MAAM,YAAY,EAAE,MAAM,EAAE;wBACrC,OAAO,EAAE,MAAM,CAAC,MAAM;oBACxB,OAAO;wBACL,OAAO;oBACT;gBACF;YACF,OAAO,IAAI,OAAO,UAAU,YAAY,MAAM,MAAM,EAAE;gBACpD,KAAK,CAAC,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM;YACnC,OAAO,IAAI,SAAS,UAAU;gBAC5B,IAAI,SAAS,MAAM;gBACnB,IAAI,UAAU,OAAO,GAAG,CAAC,MAAM,KAAK;gBACpC,IAAI,WAAW,MAAM;oBACnB,UAAU;oBACV,OAAO,GAAG,CAAC,MAAM,KAAK,EAAE;oBACxB;gBACF;gBACA,KAAK,CAAC,KAAK,GAAG;oBACZ,KAAK,MAAM,GAAG;oBACd;oBACA,OAAO,MAAM,KAAK;gBACpB;YACF,OAAO;gBACL,KAAK,CAAC,KAAK,GAAG;YAChB;QACF;QAEA,IAAI,YAAY;YACd,MAAM,MAAM,GAAG;mBAAI,OAAO,IAAI;aAAG,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;QAC7D;QAEA,OAAO;IACT;IAEA,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,IAAI,EAAE,IAAI,CAAC,iBAAiB;QAC1D;QACA,OAAO,IAAI,CAAC,UAAU;IACxB;IAEA,SAAS,cAAc,SAAS,EAAE;QAChC,IAAI,YAAY,SAAS,EAAE,cAAc,YAAY,SAAS;QAC9D,IAAI,SAAS;QACb,YAAY,IAAI,EAAE,CAAA;YAChB,UAAU;QACZ;QACA,OAAO;IACT;IAEA,KAAK,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;QAC5B,IAAI,OAAO;YAAE,MAAM,IAAI;QAAC;QACxB,IAAK,IAAI,KAAK,KAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACrC,OAAO,OAAO,IAAI,CAAC,MAAM;IAC3B;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,KAAK,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/comment.js"], "sourcesContent": ["'use strict'\n\nlet Node = require('./node')\n\nclass Comment extends Node {\n  constructor(defaults) {\n    super(defaults)\n    this.type = 'comment'\n  }\n}\n\nmodule.exports = Comment\nComment.default = Comment\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,MAAM,gBAAgB;IACpB,YAAY,QAAQ,CAAE;QACpB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,QAAQ,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/declaration.js"], "sourcesContent": ["'use strict'\n\nlet Node = require('./node')\n\nclass Declaration extends Node {\n  get variable() {\n    return this.prop.startsWith('--') || this.prop[0] === '$'\n  }\n\n  constructor(defaults) {\n    if (\n      defaults &&\n      typeof defaults.value !== 'undefined' &&\n      typeof defaults.value !== 'string'\n    ) {\n      defaults = { ...defaults, value: String(defaults.value) }\n    }\n    super(defaults)\n    this.type = 'decl'\n  }\n}\n\nmodule.exports = Declaration\nDeclaration.default = Declaration\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,MAAM,oBAAoB;IACxB,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK;IACxD;IAEA,YAAY,QAAQ,CAAE;QACpB,IACE,YACA,OAAO,SAAS,KAAK,KAAK,eAC1B,OAAO,SAAS,KAAK,KAAK,UAC1B;YACA,WAAW;gBAAE,GAAG,QAAQ;gBAAE,OAAO,OAAO,SAAS,KAAK;YAAE;QAC1D;QACA,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,YAAY,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/container.js"], "sourcesContent": ["'use strict'\n\nlet Comment = require('./comment')\nlet Declaration = require('./declaration')\nlet Node = require('./node')\nlet { isClean, my } = require('./symbols')\n\nlet AtRule, parse, Root, Rule\n\nfunction cleanSource(nodes) {\n  return nodes.map(i => {\n    if (i.nodes) i.nodes = cleanSource(i.nodes)\n    delete i.source\n    return i\n  })\n}\n\nfunction markTreeDirty(node) {\n  node[isClean] = false\n  if (node.proxyOf.nodes) {\n    for (let i of node.proxyOf.nodes) {\n      markTreeDirty(i)\n    }\n  }\n}\n\nclass Container extends Node {\n  get first() {\n    if (!this.proxyOf.nodes) return undefined\n    return this.proxyOf.nodes[0]\n  }\n\n  get last() {\n    if (!this.proxyOf.nodes) return undefined\n    return this.proxyOf.nodes[this.proxyOf.nodes.length - 1]\n  }\n\n  append(...children) {\n    for (let child of children) {\n      let nodes = this.normalize(child, this.last)\n      for (let node of nodes) this.proxyOf.nodes.push(node)\n    }\n\n    this.markDirty()\n\n    return this\n  }\n\n  cleanRaws(keepBetween) {\n    super.cleanRaws(keepBetween)\n    if (this.nodes) {\n      for (let node of this.nodes) node.cleanRaws(keepBetween)\n    }\n  }\n\n  each(callback) {\n    if (!this.proxyOf.nodes) return undefined\n    let iterator = this.getIterator()\n\n    let index, result\n    while (this.indexes[iterator] < this.proxyOf.nodes.length) {\n      index = this.indexes[iterator]\n      result = callback(this.proxyOf.nodes[index], index)\n      if (result === false) break\n\n      this.indexes[iterator] += 1\n    }\n\n    delete this.indexes[iterator]\n    return result\n  }\n\n  every(condition) {\n    return this.nodes.every(condition)\n  }\n\n  getIterator() {\n    if (!this.lastEach) this.lastEach = 0\n    if (!this.indexes) this.indexes = {}\n\n    this.lastEach += 1\n    let iterator = this.lastEach\n    this.indexes[iterator] = 0\n\n    return iterator\n  }\n\n  getProxyProcessor() {\n    return {\n      get(node, prop) {\n        if (prop === 'proxyOf') {\n          return node\n        } else if (!node[prop]) {\n          return node[prop]\n        } else if (\n          prop === 'each' ||\n          (typeof prop === 'string' && prop.startsWith('walk'))\n        ) {\n          return (...args) => {\n            return node[prop](\n              ...args.map(i => {\n                if (typeof i === 'function') {\n                  return (child, index) => i(child.toProxy(), index)\n                } else {\n                  return i\n                }\n              })\n            )\n          }\n        } else if (prop === 'every' || prop === 'some') {\n          return cb => {\n            return node[prop]((child, ...other) =>\n              cb(child.toProxy(), ...other)\n            )\n          }\n        } else if (prop === 'root') {\n          return () => node.root().toProxy()\n        } else if (prop === 'nodes') {\n          return node.nodes.map(i => i.toProxy())\n        } else if (prop === 'first' || prop === 'last') {\n          return node[prop].toProxy()\n        } else {\n          return node[prop]\n        }\n      },\n\n      set(node, prop, value) {\n        if (node[prop] === value) return true\n        node[prop] = value\n        if (prop === 'name' || prop === 'params' || prop === 'selector') {\n          node.markDirty()\n        }\n        return true\n      }\n    }\n  }\n\n  index(child) {\n    if (typeof child === 'number') return child\n    if (child.proxyOf) child = child.proxyOf\n    return this.proxyOf.nodes.indexOf(child)\n  }\n\n  insertAfter(exist, add) {\n    let existIndex = this.index(exist)\n    let nodes = this.normalize(add, this.proxyOf.nodes[existIndex]).reverse()\n    existIndex = this.index(exist)\n    for (let node of nodes) this.proxyOf.nodes.splice(existIndex + 1, 0, node)\n\n    let index\n    for (let id in this.indexes) {\n      index = this.indexes[id]\n      if (existIndex < index) {\n        this.indexes[id] = index + nodes.length\n      }\n    }\n\n    this.markDirty()\n\n    return this\n  }\n\n  insertBefore(exist, add) {\n    let existIndex = this.index(exist)\n    let type = existIndex === 0 ? 'prepend' : false\n    let nodes = this.normalize(\n      add,\n      this.proxyOf.nodes[existIndex],\n      type\n    ).reverse()\n    existIndex = this.index(exist)\n    for (let node of nodes) this.proxyOf.nodes.splice(existIndex, 0, node)\n\n    let index\n    for (let id in this.indexes) {\n      index = this.indexes[id]\n      if (existIndex <= index) {\n        this.indexes[id] = index + nodes.length\n      }\n    }\n\n    this.markDirty()\n\n    return this\n  }\n\n  normalize(nodes, sample) {\n    if (typeof nodes === 'string') {\n      nodes = cleanSource(parse(nodes).nodes)\n    } else if (typeof nodes === 'undefined') {\n      nodes = []\n    } else if (Array.isArray(nodes)) {\n      nodes = nodes.slice(0)\n      for (let i of nodes) {\n        if (i.parent) i.parent.removeChild(i, 'ignore')\n      }\n    } else if (nodes.type === 'root' && this.type !== 'document') {\n      nodes = nodes.nodes.slice(0)\n      for (let i of nodes) {\n        if (i.parent) i.parent.removeChild(i, 'ignore')\n      }\n    } else if (nodes.type) {\n      nodes = [nodes]\n    } else if (nodes.prop) {\n      if (typeof nodes.value === 'undefined') {\n        throw new Error('Value field is missed in node creation')\n      } else if (typeof nodes.value !== 'string') {\n        nodes.value = String(nodes.value)\n      }\n      nodes = [new Declaration(nodes)]\n    } else if (nodes.selector || nodes.selectors) {\n      nodes = [new Rule(nodes)]\n    } else if (nodes.name) {\n      nodes = [new AtRule(nodes)]\n    } else if (nodes.text) {\n      nodes = [new Comment(nodes)]\n    } else {\n      throw new Error('Unknown node type in node creation')\n    }\n\n    let processed = nodes.map(i => {\n      /* c8 ignore next */\n      if (!i[my]) Container.rebuild(i)\n      i = i.proxyOf\n      if (i.parent) i.parent.removeChild(i)\n      if (i[isClean]) markTreeDirty(i)\n\n      if (!i.raws) i.raws = {}\n      if (typeof i.raws.before === 'undefined') {\n        if (sample && typeof sample.raws.before !== 'undefined') {\n          i.raws.before = sample.raws.before.replace(/\\S/g, '')\n        }\n      }\n      i.parent = this.proxyOf\n      return i\n    })\n\n    return processed\n  }\n\n  prepend(...children) {\n    children = children.reverse()\n    for (let child of children) {\n      let nodes = this.normalize(child, this.first, 'prepend').reverse()\n      for (let node of nodes) this.proxyOf.nodes.unshift(node)\n      for (let id in this.indexes) {\n        this.indexes[id] = this.indexes[id] + nodes.length\n      }\n    }\n\n    this.markDirty()\n\n    return this\n  }\n\n  push(child) {\n    child.parent = this\n    this.proxyOf.nodes.push(child)\n    return this\n  }\n\n  removeAll() {\n    for (let node of this.proxyOf.nodes) node.parent = undefined\n    this.proxyOf.nodes = []\n\n    this.markDirty()\n\n    return this\n  }\n\n  removeChild(child) {\n    child = this.index(child)\n    this.proxyOf.nodes[child].parent = undefined\n    this.proxyOf.nodes.splice(child, 1)\n\n    let index\n    for (let id in this.indexes) {\n      index = this.indexes[id]\n      if (index >= child) {\n        this.indexes[id] = index - 1\n      }\n    }\n\n    this.markDirty()\n\n    return this\n  }\n\n  replaceValues(pattern, opts, callback) {\n    if (!callback) {\n      callback = opts\n      opts = {}\n    }\n\n    this.walkDecls(decl => {\n      if (opts.props && !opts.props.includes(decl.prop)) return\n      if (opts.fast && !decl.value.includes(opts.fast)) return\n\n      decl.value = decl.value.replace(pattern, callback)\n    })\n\n    this.markDirty()\n\n    return this\n  }\n\n  some(condition) {\n    return this.nodes.some(condition)\n  }\n\n  walk(callback) {\n    return this.each((child, i) => {\n      let result\n      try {\n        result = callback(child, i)\n      } catch (e) {\n        throw child.addToError(e)\n      }\n      if (result !== false && child.walk) {\n        result = child.walk(callback)\n      }\n\n      return result\n    })\n  }\n\n  walkAtRules(name, callback) {\n    if (!callback) {\n      callback = name\n      return this.walk((child, i) => {\n        if (child.type === 'atrule') {\n          return callback(child, i)\n        }\n      })\n    }\n    if (name instanceof RegExp) {\n      return this.walk((child, i) => {\n        if (child.type === 'atrule' && name.test(child.name)) {\n          return callback(child, i)\n        }\n      })\n    }\n    return this.walk((child, i) => {\n      if (child.type === 'atrule' && child.name === name) {\n        return callback(child, i)\n      }\n    })\n  }\n\n  walkComments(callback) {\n    return this.walk((child, i) => {\n      if (child.type === 'comment') {\n        return callback(child, i)\n      }\n    })\n  }\n\n  walkDecls(prop, callback) {\n    if (!callback) {\n      callback = prop\n      return this.walk((child, i) => {\n        if (child.type === 'decl') {\n          return callback(child, i)\n        }\n      })\n    }\n    if (prop instanceof RegExp) {\n      return this.walk((child, i) => {\n        if (child.type === 'decl' && prop.test(child.prop)) {\n          return callback(child, i)\n        }\n      })\n    }\n    return this.walk((child, i) => {\n      if (child.type === 'decl' && child.prop === prop) {\n        return callback(child, i)\n      }\n    })\n  }\n\n  walkRules(selector, callback) {\n    if (!callback) {\n      callback = selector\n\n      return this.walk((child, i) => {\n        if (child.type === 'rule') {\n          return callback(child, i)\n        }\n      })\n    }\n    if (selector instanceof RegExp) {\n      return this.walk((child, i) => {\n        if (child.type === 'rule' && selector.test(child.selector)) {\n          return callback(child, i)\n        }\n      })\n    }\n    return this.walk((child, i) => {\n      if (child.type === 'rule' && child.selector === selector) {\n        return callback(child, i)\n      }\n    })\n  }\n}\n\nContainer.registerParse = dependant => {\n  parse = dependant\n}\n\nContainer.registerRule = dependant => {\n  Rule = dependant\n}\n\nContainer.registerAtRule = dependant => {\n  AtRule = dependant\n}\n\nContainer.registerRoot = dependant => {\n  Root = dependant\n}\n\nmodule.exports = Container\nContainer.default = Container\n\n/* c8 ignore start */\nContainer.rebuild = node => {\n  if (node.type === 'atrule') {\n    Object.setPrototypeOf(node, AtRule.prototype)\n  } else if (node.type === 'rule') {\n    Object.setPrototypeOf(node, Rule.prototype)\n  } else if (node.type === 'decl') {\n    Object.setPrototypeOf(node, Declaration.prototype)\n  } else if (node.type === 'comment') {\n    Object.setPrototypeOf(node, Comment.prototype)\n  } else if (node.type === 'root') {\n    Object.setPrototypeOf(node, Root.prototype)\n  }\n\n  node[my] = true\n\n  if (node.nodes) {\n    node.nodes.forEach(child => {\n      Container.rebuild(child)\n    })\n  }\n}\n/* c8 ignore stop */\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;AAEnB,IAAI,QAAQ,OAAO,MAAM;AAEzB,SAAS,YAAY,KAAK;IACxB,OAAO,MAAM,GAAG,CAAC,CAAA;QACf,IAAI,EAAE,KAAK,EAAE,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK;QAC1C,OAAO,EAAE,MAAM;QACf,OAAO;IACT;AACF;AAEA,SAAS,cAAc,IAAI;IACzB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,KAAK,OAAO,CAAC,KAAK,EAAE;QACtB,KAAK,IAAI,KAAK,KAAK,OAAO,CAAC,KAAK,CAAE;YAChC,cAAc;QAChB;IACF;AACF;AAEA,MAAM,kBAAkB;IACtB,IAAI,QAAQ;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;IAC9B;IAEA,IAAI,OAAO;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC1D;IAEA,OAAO,GAAG,QAAQ,EAAE;QAClB,KAAK,IAAI,SAAS,SAAU;YAC1B,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI;YAC3C,KAAK,IAAI,QAAQ,MAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;QAClD;QAEA,IAAI,CAAC,SAAS;QAEd,OAAO,IAAI;IACb;IAEA,UAAU,WAAW,EAAE;QACrB,KAAK,CAAC,UAAU;QAChB,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,KAAK,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAE,KAAK,SAAS,CAAC;QAC9C;IACF;IAEA,KAAK,QAAQ,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO;QAChC,IAAI,WAAW,IAAI,CAAC,WAAW;QAE/B,IAAI,OAAO;QACX,MAAO,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAE;YACzD,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS;YAC9B,SAAS,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;YAC7C,IAAI,WAAW,OAAO;YAEtB,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI;QAC5B;QAEA,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS;QAC7B,OAAO;IACT;IAEA,MAAM,SAAS,EAAE;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B;IAEA,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG;QACpC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC;QAEnC,IAAI,CAAC,QAAQ,IAAI;QACjB,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;QAEzB,OAAO;IACT;IAEA,oBAAoB;QAClB,OAAO;YACL,KAAI,IAAI,EAAE,IAAI;gBACZ,IAAI,SAAS,WAAW;oBACtB,OAAO;gBACT,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;oBACtB,OAAO,IAAI,CAAC,KAAK;gBACnB,OAAO,IACL,SAAS,UACR,OAAO,SAAS,YAAY,KAAK,UAAU,CAAC,SAC7C;oBACA,OAAO,CAAC,GAAG;wBACT,OAAO,IAAI,CAAC,KAAK,IACZ,KAAK,GAAG,CAAC,CAAA;4BACV,IAAI,OAAO,MAAM,YAAY;gCAC3B,OAAO,CAAC,OAAO,QAAU,EAAE,MAAM,OAAO,IAAI;4BAC9C,OAAO;gCACL,OAAO;4BACT;wBACF;oBAEJ;gBACF,OAAO,IAAI,SAAS,WAAW,SAAS,QAAQ;oBAC9C,OAAO,CAAA;wBACL,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,QAC3B,GAAG,MAAM,OAAO,OAAO;oBAE3B;gBACF,OAAO,IAAI,SAAS,QAAQ;oBAC1B,OAAO,IAAM,KAAK,IAAI,GAAG,OAAO;gBAClC,OAAO,IAAI,SAAS,SAAS;oBAC3B,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;gBACtC,OAAO,IAAI,SAAS,WAAW,SAAS,QAAQ;oBAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO;gBAC3B,OAAO;oBACL,OAAO,IAAI,CAAC,KAAK;gBACnB;YACF;YAEA,KAAI,IAAI,EAAE,IAAI,EAAE,KAAK;gBACnB,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,OAAO;gBACjC,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,SAAS,UAAU,SAAS,YAAY,SAAS,YAAY;oBAC/D,KAAK,SAAS;gBAChB;gBACA,OAAO;YACT;QACF;IACF;IAEA,MAAM,KAAK,EAAE;QACX,IAAI,OAAO,UAAU,UAAU,OAAO;QACtC,IAAI,MAAM,OAAO,EAAE,QAAQ,MAAM,OAAO;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;IACpC;IAEA,YAAY,KAAK,EAAE,GAAG,EAAE;QACtB,IAAI,aAAa,IAAI,CAAC,KAAK,CAAC;QAC5B,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO;QACvE,aAAa,IAAI,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,QAAQ,MAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG;QAErE,IAAI;QACJ,IAAK,IAAI,MAAM,IAAI,CAAC,OAAO,CAAE;YAC3B,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;YACxB,IAAI,aAAa,OAAO;gBACtB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,QAAQ,MAAM,MAAM;YACzC;QACF;QAEA,IAAI,CAAC,SAAS;QAEd,OAAO,IAAI;IACb;IAEA,aAAa,KAAK,EAAE,GAAG,EAAE;QACvB,IAAI,aAAa,IAAI,CAAC,KAAK,CAAC;QAC5B,IAAI,OAAO,eAAe,IAAI,YAAY;QAC1C,IAAI,QAAQ,IAAI,CAAC,SAAS,CACxB,KACA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAC9B,MACA,OAAO;QACT,aAAa,IAAI,CAAC,KAAK,CAAC;QACxB,KAAK,IAAI,QAAQ,MAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,GAAG;QAEjE,IAAI;QACJ,IAAK,IAAI,MAAM,IAAI,CAAC,OAAO,CAAE;YAC3B,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;YACxB,IAAI,cAAc,OAAO;gBACvB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,QAAQ,MAAM,MAAM;YACzC;QACF;QAEA,IAAI,CAAC,SAAS;QAEd,OAAO,IAAI;IACb;IAEA,UAAU,KAAK,EAAE,MAAM,EAAE;QACvB,IAAI,OAAO,UAAU,UAAU;YAC7B,QAAQ,YAAY,MAAM,OAAO,KAAK;QACxC,OAAO,IAAI,OAAO,UAAU,aAAa;YACvC,QAAQ,EAAE;QACZ,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;YAC/B,QAAQ,MAAM,KAAK,CAAC;YACpB,KAAK,IAAI,KAAK,MAAO;gBACnB,IAAI,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG;YACxC;QACF,OAAO,IAAI,MAAM,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,KAAK,YAAY;YAC5D,QAAQ,MAAM,KAAK,CAAC,KAAK,CAAC;YAC1B,KAAK,IAAI,KAAK,MAAO;gBACnB,IAAI,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG;YACxC;QACF,OAAO,IAAI,MAAM,IAAI,EAAE;YACrB,QAAQ;gBAAC;aAAM;QACjB,OAAO,IAAI,MAAM,IAAI,EAAE;YACrB,IAAI,OAAO,MAAM,KAAK,KAAK,aAAa;gBACtC,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,OAAO,MAAM,KAAK,KAAK,UAAU;gBAC1C,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK;YAClC;YACA,QAAQ;gBAAC,IAAI,YAAY;aAAO;QAClC,OAAO,IAAI,MAAM,QAAQ,IAAI,MAAM,SAAS,EAAE;YAC5C,QAAQ;gBAAC,IAAI,KAAK;aAAO;QAC3B,OAAO,IAAI,MAAM,IAAI,EAAE;YACrB,QAAQ;gBAAC,IAAI,OAAO;aAAO;QAC7B,OAAO,IAAI,MAAM,IAAI,EAAE;YACrB,QAAQ;gBAAC,IAAI,QAAQ;aAAO;QAC9B,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,YAAY,MAAM,GAAG,CAAC,CAAA;YACxB,kBAAkB,GAClB,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,UAAU,OAAO,CAAC;YAC9B,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,WAAW,CAAC;YACnC,IAAI,CAAC,CAAC,QAAQ,EAAE,cAAc;YAE9B,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,GAAG,CAAC;YACvB,IAAI,OAAO,EAAE,IAAI,CAAC,MAAM,KAAK,aAAa;gBACxC,IAAI,UAAU,OAAO,OAAO,IAAI,CAAC,MAAM,KAAK,aAAa;oBACvD,EAAE,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;gBACpD;YACF;YACA,EAAE,MAAM,GAAG,IAAI,CAAC,OAAO;YACvB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,QAAQ,GAAG,QAAQ,EAAE;QACnB,WAAW,SAAS,OAAO;QAC3B,KAAK,IAAI,SAAS,SAAU;YAC1B,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,KAAK,EAAE,WAAW,OAAO;YAChE,KAAK,IAAI,QAAQ,MAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;YACnD,IAAK,IAAI,MAAM,IAAI,CAAC,OAAO,CAAE;gBAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,MAAM,MAAM;YACpD;QACF;QAEA,IAAI,CAAC,SAAS;QAEd,OAAO,IAAI;IACb;IAEA,KAAK,KAAK,EAAE;QACV,MAAM,MAAM,GAAG,IAAI;QACnB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;QACxB,OAAO,IAAI;IACb;IAEA,YAAY;QACV,KAAK,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAE,KAAK,MAAM,GAAG;QACnD,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE;QAEvB,IAAI,CAAC,SAAS;QAEd,OAAO,IAAI;IACb;IAEA,YAAY,KAAK,EAAE;QACjB,QAAQ,IAAI,CAAC,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG;QACnC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;QAEjC,IAAI;QACJ,IAAK,IAAI,MAAM,IAAI,CAAC,OAAO,CAAE;YAC3B,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;YACxB,IAAI,SAAS,OAAO;gBAClB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,QAAQ;YAC7B;QACF;QAEA,IAAI,CAAC,SAAS;QAEd,OAAO,IAAI;IACb;IAEA,cAAc,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE;QACrC,IAAI,CAAC,UAAU;YACb,WAAW;YACX,OAAO,CAAC;QACV;QAEA,IAAI,CAAC,SAAS,CAAC,CAAA;YACb,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACnD,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YAElD,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,SAAS;QAC3C;QAEA,IAAI,CAAC,SAAS;QAEd,OAAO,IAAI;IACb;IAEA,KAAK,SAAS,EAAE;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB;IAEA,KAAK,QAAQ,EAAE;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;YACvB,IAAI;YACJ,IAAI;gBACF,SAAS,SAAS,OAAO;YAC3B,EAAE,OAAO,GAAG;gBACV,MAAM,MAAM,UAAU,CAAC;YACzB;YACA,IAAI,WAAW,SAAS,MAAM,IAAI,EAAE;gBAClC,SAAS,MAAM,IAAI,CAAC;YACtB;YAEA,OAAO;QACT;IACF;IAEA,YAAY,IAAI,EAAE,QAAQ,EAAE;QAC1B,IAAI,CAAC,UAAU;YACb,WAAW;YACX,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;gBACvB,IAAI,MAAM,IAAI,KAAK,UAAU;oBAC3B,OAAO,SAAS,OAAO;gBACzB;YACF;QACF;QACA,IAAI,gBAAgB,QAAQ;YAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;gBACvB,IAAI,MAAM,IAAI,KAAK,YAAY,KAAK,IAAI,CAAC,MAAM,IAAI,GAAG;oBACpD,OAAO,SAAS,OAAO;gBACzB;YACF;QACF;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;YACvB,IAAI,MAAM,IAAI,KAAK,YAAY,MAAM,IAAI,KAAK,MAAM;gBAClD,OAAO,SAAS,OAAO;YACzB;QACF;IACF;IAEA,aAAa,QAAQ,EAAE;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;YACvB,IAAI,MAAM,IAAI,KAAK,WAAW;gBAC5B,OAAO,SAAS,OAAO;YACzB;QACF;IACF;IAEA,UAAU,IAAI,EAAE,QAAQ,EAAE;QACxB,IAAI,CAAC,UAAU;YACb,WAAW;YACX,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;gBACvB,IAAI,MAAM,IAAI,KAAK,QAAQ;oBACzB,OAAO,SAAS,OAAO;gBACzB;YACF;QACF;QACA,IAAI,gBAAgB,QAAQ;YAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;gBACvB,IAAI,MAAM,IAAI,KAAK,UAAU,KAAK,IAAI,CAAC,MAAM,IAAI,GAAG;oBAClD,OAAO,SAAS,OAAO;gBACzB;YACF;QACF;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;YACvB,IAAI,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,KAAK,MAAM;gBAChD,OAAO,SAAS,OAAO;YACzB;QACF;IACF;IAEA,UAAU,QAAQ,EAAE,QAAQ,EAAE;QAC5B,IAAI,CAAC,UAAU;YACb,WAAW;YAEX,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;gBACvB,IAAI,MAAM,IAAI,KAAK,QAAQ;oBACzB,OAAO,SAAS,OAAO;gBACzB;YACF;QACF;QACA,IAAI,oBAAoB,QAAQ;YAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;gBACvB,IAAI,MAAM,IAAI,KAAK,UAAU,SAAS,IAAI,CAAC,MAAM,QAAQ,GAAG;oBAC1D,OAAO,SAAS,OAAO;gBACzB;YACF;QACF;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO;YACvB,IAAI,MAAM,IAAI,KAAK,UAAU,MAAM,QAAQ,KAAK,UAAU;gBACxD,OAAO,SAAS,OAAO;YACzB;QACF;IACF;AACF;AAEA,UAAU,aAAa,GAAG,CAAA;IACxB,QAAQ;AACV;AAEA,UAAU,YAAY,GAAG,CAAA;IACvB,OAAO;AACT;AAEA,UAAU,cAAc,GAAG,CAAA;IACzB,SAAS;AACX;AAEA,UAAU,YAAY,GAAG,CAAA;IACvB,OAAO;AACT;AAEA,OAAO,OAAO,GAAG;AACjB,UAAU,OAAO,GAAG;AAEpB,mBAAmB,GACnB,UAAU,OAAO,GAAG,CAAA;IAClB,IAAI,KAAK,IAAI,KAAK,UAAU;QAC1B,OAAO,cAAc,CAAC,MAAM,OAAO,SAAS;IAC9C,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;QAC/B,OAAO,cAAc,CAAC,MAAM,KAAK,SAAS;IAC5C,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;QAC/B,OAAO,cAAc,CAAC,MAAM,YAAY,SAAS;IACnD,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;QAClC,OAAO,cAAc,CAAC,MAAM,QAAQ,SAAS;IAC/C,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;QAC/B,OAAO,cAAc,CAAC,MAAM,KAAK,SAAS;IAC5C;IAEA,IAAI,CAAC,GAAG,GAAG;IAEX,IAAI,KAAK,KAAK,EAAE;QACd,KAAK,KAAK,CAAC,OAAO,CAAC,CAAA;YACjB,UAAU,OAAO,CAAC;QACpB;IACF;AACF,EACA,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 1622, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/at-rule.js"], "sourcesContent": ["'use strict'\n\nlet Container = require('./container')\n\nclass AtRule extends Container {\n  constructor(defaults) {\n    super(defaults)\n    this.type = 'atrule'\n  }\n\n  append(...children) {\n    if (!this.proxyOf.nodes) this.nodes = []\n    return super.append(...children)\n  }\n\n  prepend(...children) {\n    if (!this.proxyOf.nodes) this.nodes = []\n    return super.prepend(...children)\n  }\n}\n\nmodule.exports = AtRule\nAtRule.default = AtRule\n\nContainer.registerAtRule(AtRule)\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,MAAM,eAAe;IACnB,YAAY,QAAQ,CAAE;QACpB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,OAAO,GAAG,QAAQ,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE;QACxC,OAAO,KAAK,CAAC,UAAU;IACzB;IAEA,QAAQ,GAAG,QAAQ,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE;QACxC,OAAO,KAAK,CAAC,WAAW;IAC1B;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,GAAG;AAEjB,UAAU,cAAc,CAAC", "ignoreList": [0]}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/document.js"], "sourcesContent": ["'use strict'\n\nlet Container = require('./container')\n\nlet LazyR<PERSON>ult, Processor\n\nclass Document extends Container {\n  constructor(defaults) {\n    // type needs to be passed to super, otherwise child roots won't be normalized correctly\n    super({ type: 'document', ...defaults })\n\n    if (!this.nodes) {\n      this.nodes = []\n    }\n  }\n\n  toResult(opts = {}) {\n    let lazy = new LazyResult(new Processor(), this, opts)\n\n    return lazy.stringify()\n  }\n}\n\nDocument.registerLazyResult = dependant => {\n  LazyResult = dependant\n}\n\nDocument.registerProcessor = dependant => {\n  Processor = dependant\n}\n\nmodule.exports = Document\nDocument.default = Document\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,IAAI,YAAY;AAEhB,MAAM,iBAAiB;IACrB,YAAY,QAAQ,CAAE;QACpB,wFAAwF;QACxF,KAAK,CAAC;YAAE,MAAM;YAAY,GAAG,QAAQ;QAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,IAAI,CAAC,KAAK,GAAG,EAAE;QACjB;IACF;IAEA,SAAS,OAAO,CAAC,CAAC,EAAE;QAClB,IAAI,OAAO,IAAI,WAAW,IAAI,aAAa,IAAI,EAAE;QAEjD,OAAO,KAAK,SAAS;IACvB;AACF;AAEA,SAAS,kBAAkB,GAAG,CAAA;IAC5B,aAAa;AACf;AAEA,SAAS,iBAAiB,GAAG,CAAA;IAC3B,YAAY;AACd;AAEA,OAAO,OAAO,GAAG;AACjB,SAAS,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 1678, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/nanoid/non-secure/index.cjs"], "sourcesContent": ["// This alphabet uses `A-Za-z0-9_-` symbols.\n// The order of characters is optimized for better gzip and brotli compression.\n// References to the same file (works both for gzip and brotli):\n// `'use`, `andom`, and `rict'`\n// References to the brotli default dictionary:\n// `-26T`, `1983`, `40px`, `75px`, `bush`, `jack`, `mind`, `very`, and `wolf`\nlet urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n\nlet customAlphabet = (alphabet, defaultSize = 21) => {\n  return (size = defaultSize) => {\n    let id = ''\n    // A compact alternative for `for (var i = 0; i < step; i++)`.\n    let i = size | 0\n    while (i--) {\n      // `| 0` is more compact and faster than `Math.floor()`.\n      id += alphabet[(Math.random() * alphabet.length) | 0]\n    }\n    return id\n  }\n}\n\nlet nanoid = (size = 21) => {\n  let id = ''\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\n  let i = size | 0\n  while (i--) {\n    // `| 0` is more compact and faster than `Math.floor()`.\n    id += urlAlphabet[(Math.random() * 64) | 0]\n  }\n  return id\n}\n\nmodule.exports = { nanoid, customAlphabet }\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,+EAA+E;AAC/E,gEAAgE;AAChE,+BAA+B;AAC/B,+CAA+C;AAC/C,6EAA6E;AAC7E,IAAI,cACF;AAEF,IAAI,iBAAiB,CAAC,UAAU,cAAc,EAAE;IAC9C,OAAO,CAAC,OAAO,WAAW;QACxB,IAAI,KAAK;QACT,8DAA8D;QAC9D,IAAI,IAAI,OAAO;QACf,MAAO,IAAK;YACV,wDAAwD;YACxD,MAAM,QAAQ,CAAC,AAAC,KAAK,MAAM,KAAK,SAAS,MAAM,GAAI,EAAE;QACvD;QACA,OAAO;IACT;AACF;AAEA,IAAI,SAAS,CAAC,OAAO,EAAE;IACrB,IAAI,KAAK;IACT,8DAA8D;IAC9D,IAAI,IAAI,OAAO;IACf,MAAO,IAAK;QACV,wDAAwD;QACxD,MAAM,WAAW,CAAC,AAAC,KAAK,MAAM,KAAK,KAAM,EAAE;IAC7C;IACA,OAAO;AACT;AAEA,OAAO,OAAO,GAAG;IAAE;IAAQ;AAAe", "ignoreList": [0]}}, {"offset": {"line": 1716, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/base64.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar intToCharMap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'.split('');\n\n/**\n * Encode an integer in the range of 0 to 63 to a single base 64 digit.\n */\nexports.encode = function (number) {\n  if (0 <= number && number < intToCharMap.length) {\n    return intToCharMap[number];\n  }\n  throw new TypeError(\"Must be between 0 and 63: \" + number);\n};\n\n/**\n * Decode a single base 64 character code digit to an integer. Returns -1 on\n * failure.\n */\nexports.decode = function (charCode) {\n  var bigA = 65;     // 'A'\n  var bigZ = 90;     // 'Z'\n\n  var littleA = 97;  // 'a'\n  var littleZ = 122; // 'z'\n\n  var zero = 48;     // '0'\n  var nine = 57;     // '9'\n\n  var plus = 43;     // '+'\n  var slash = 47;    // '/'\n\n  var littleOffset = 26;\n  var numberOffset = 52;\n\n  // 0 - 25: ABCDEFGHIJKLMNOPQRSTUVWXYZ\n  if (bigA <= charCode && charCode <= bigZ) {\n    return (charCode - bigA);\n  }\n\n  // 26 - 51: abcdefghijklmnopqrstuvwxyz\n  if (littleA <= charCode && charCode <= littleZ) {\n    return (charCode - littleA + littleOffset);\n  }\n\n  // 52 - 61: 0123456789\n  if (zero <= charCode && charCode <= nine) {\n    return (charCode - zero + numberOffset);\n  }\n\n  // 62: +\n  if (charCode == plus) {\n    return 62;\n  }\n\n  // 63: /\n  if (charCode == slash) {\n    return 63;\n  }\n\n  // Invalid base64 digit.\n  return -1;\n};\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED,IAAI,eAAe,mEAAmE,KAAK,CAAC;AAE5F;;CAEC,GACD,QAAQ,MAAM,GAAG,SAAU,MAAM;IAC/B,IAAI,KAAK,UAAU,SAAS,aAAa,MAAM,EAAE;QAC/C,OAAO,YAAY,CAAC,OAAO;IAC7B;IACA,MAAM,IAAI,UAAU,+BAA+B;AACrD;AAEA;;;CAGC,GACD,QAAQ,MAAM,GAAG,SAAU,QAAQ;IACjC,IAAI,OAAO,IAAQ,MAAM;IACzB,IAAI,OAAO,IAAQ,MAAM;IAEzB,IAAI,UAAU,IAAK,MAAM;IACzB,IAAI,UAAU,KAAK,MAAM;IAEzB,IAAI,OAAO,IAAQ,MAAM;IACzB,IAAI,OAAO,IAAQ,MAAM;IAEzB,IAAI,OAAO,IAAQ,MAAM;IACzB,IAAI,QAAQ,IAAO,MAAM;IAEzB,IAAI,eAAe;IACnB,IAAI,eAAe;IAEnB,qCAAqC;IACrC,IAAI,QAAQ,YAAY,YAAY,MAAM;QACxC,OAAQ,WAAW;IACrB;IAEA,sCAAsC;IACtC,IAAI,WAAW,YAAY,YAAY,SAAS;QAC9C,OAAQ,WAAW,UAAU;IAC/B;IAEA,sBAAsB;IACtB,IAAI,QAAQ,YAAY,YAAY,MAAM;QACxC,OAAQ,WAAW,OAAO;IAC5B;IAEA,QAAQ;IACR,IAAI,YAAY,MAAM;QACpB,OAAO;IACT;IAEA,QAAQ;IACR,IAAI,YAAY,OAAO;QACrB,OAAO;IACT;IAEA,wBAAwB;IACxB,OAAO,CAAC;AACV", "ignoreList": [0]}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/base64-vlq.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n *\n * Based on the Base 64 VLQ implementation in Closure Compiler:\n * https://code.google.com/p/closure-compiler/source/browse/trunk/src/com/google/debugging/sourcemap/Base64VLQ.java\n *\n * Copyright 2011 The Closure Compiler Authors. All rights reserved.\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *  * Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above\n *    copyright notice, this list of conditions and the following\n *    disclaimer in the documentation and/or other materials provided\n *    with the distribution.\n *  * Neither the name of Google Inc. nor the names of its\n *    contributors may be used to endorse or promote products derived\n *    from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n * \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar base64 = require('./base64');\n\n// A single base 64 digit can contain 6 bits of data. For the base 64 variable\n// length quantities we use in the source map spec, the first bit is the sign,\n// the next four bits are the actual value, and the 6th bit is the\n// continuation bit. The continuation bit tells us whether there are more\n// digits in this value following this digit.\n//\n//   Continuation\n//   |    Sign\n//   |    |\n//   V    V\n//   101011\n\nvar VLQ_BASE_SHIFT = 5;\n\n// binary: 100000\nvar VLQ_BASE = 1 << VLQ_BASE_SHIFT;\n\n// binary: 011111\nvar VLQ_BASE_MASK = VLQ_BASE - 1;\n\n// binary: 100000\nvar VLQ_CONTINUATION_BIT = VLQ_BASE;\n\n/**\n * Converts from a two-complement value to a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   1 becomes 2 (10 binary), -1 becomes 3 (11 binary)\n *   2 becomes 4 (100 binary), -2 becomes 5 (101 binary)\n */\nfunction toVLQSigned(aValue) {\n  return aValue < 0\n    ? ((-aValue) << 1) + 1\n    : (aValue << 1) + 0;\n}\n\n/**\n * Converts to a two-complement value from a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   2 (10 binary) becomes 1, 3 (11 binary) becomes -1\n *   4 (100 binary) becomes 2, 5 (101 binary) becomes -2\n */\nfunction fromVLQSigned(aValue) {\n  var isNegative = (aValue & 1) === 1;\n  var shifted = aValue >> 1;\n  return isNegative\n    ? -shifted\n    : shifted;\n}\n\n/**\n * Returns the base 64 VLQ encoded value.\n */\nexports.encode = function base64VLQ_encode(aValue) {\n  var encoded = \"\";\n  var digit;\n\n  var vlq = toVLQSigned(aValue);\n\n  do {\n    digit = vlq & VLQ_BASE_MASK;\n    vlq >>>= VLQ_BASE_SHIFT;\n    if (vlq > 0) {\n      // There are still more digits in this value, so we must make sure the\n      // continuation bit is marked.\n      digit |= VLQ_CONTINUATION_BIT;\n    }\n    encoded += base64.encode(digit);\n  } while (vlq > 0);\n\n  return encoded;\n};\n\n/**\n * Decodes the next base 64 VLQ value from the given string and returns the\n * value and the rest of the string via the out parameter.\n */\nexports.decode = function base64VLQ_decode(aStr, aIndex, aOutParam) {\n  var strLen = aStr.length;\n  var result = 0;\n  var shift = 0;\n  var continuation, digit;\n\n  do {\n    if (aIndex >= strLen) {\n      throw new Error(\"Expected more digits in base 64 VLQ value.\");\n    }\n\n    digit = base64.decode(aStr.charCodeAt(aIndex++));\n    if (digit === -1) {\n      throw new Error(\"Invalid base64 digit: \" + aStr.charAt(aIndex - 1));\n    }\n\n    continuation = !!(digit & VLQ_CONTINUATION_BIT);\n    digit &= VLQ_BASE_MASK;\n    result = result + (digit << shift);\n    shift += VLQ_BASE_SHIFT;\n  } while (continuation);\n\n  aOutParam.value = fromVLQSigned(result);\n  aOutParam.rest = aIndex;\n};\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCC,GAED,IAAI;AAEJ,8EAA8E;AAC9E,8EAA8E;AAC9E,kEAAkE;AAClE,yEAAyE;AACzE,6CAA6C;AAC7C,EAAE;AACF,iBAAiB;AACjB,cAAc;AACd,WAAW;AACX,WAAW;AACX,WAAW;AAEX,IAAI,iBAAiB;AAErB,iBAAiB;AACjB,IAAI,WAAW,KAAK;AAEpB,iBAAiB;AACjB,IAAI,gBAAgB,WAAW;AAE/B,iBAAiB;AACjB,IAAI,uBAAuB;AAE3B;;;;;CAKC,GACD,SAAS,YAAY,MAAM;IACzB,OAAO,SAAS,IACZ,CAAC,AAAC,CAAC,UAAW,CAAC,IAAI,IACnB,CAAC,UAAU,CAAC,IAAI;AACtB;AAEA;;;;;CAKC,GACD,SAAS,cAAc,MAAM;IAC3B,IAAI,aAAa,CAAC,SAAS,CAAC,MAAM;IAClC,IAAI,UAAU,UAAU;IACxB,OAAO,aACH,CAAC,UACD;AACN;AAEA;;CAEC,GACD,QAAQ,MAAM,GAAG,SAAS,iBAAiB,MAAM;IAC/C,IAAI,UAAU;IACd,IAAI;IAEJ,IAAI,MAAM,YAAY;IAEtB,GAAG;QACD,QAAQ,MAAM;QACd,SAAS;QACT,IAAI,MAAM,GAAG;YACX,sEAAsE;YACtE,8BAA8B;YAC9B,SAAS;QACX;QACA,WAAW,OAAO,MAAM,CAAC;IAC3B,QAAS,MAAM,EAAG;IAElB,OAAO;AACT;AAEA;;;CAGC,GACD,QAAQ,MAAM,GAAG,SAAS,iBAAiB,IAAI,EAAE,MAAM,EAAE,SAAS;IAChE,IAAI,SAAS,KAAK,MAAM;IACxB,IAAI,SAAS;IACb,IAAI,QAAQ;IACZ,IAAI,cAAc;IAElB,GAAG;QACD,IAAI,UAAU,QAAQ;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,OAAO,MAAM,CAAC,KAAK,UAAU,CAAC;QACtC,IAAI,UAAU,CAAC,GAAG;YAChB,MAAM,IAAI,MAAM,2BAA2B,KAAK,MAAM,CAAC,SAAS;QAClE;QAEA,eAAe,CAAC,CAAC,CAAC,QAAQ,oBAAoB;QAC9C,SAAS;QACT,SAAS,SAAS,CAAC,SAAS,KAAK;QACjC,SAAS;IACX,QAAS,aAAc;IAEvB,UAAU,KAAK,GAAG,cAAc;IAChC,UAAU,IAAI,GAAG;AACnB", "ignoreList": [0]}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/util.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n/**\n * This is a helper function for getting values from parameter/options\n * objects.\n *\n * @param args The object we are extracting values from\n * @param name The name of the property we are getting.\n * @param defaultValue An optional value to return if the property is missing\n * from the object. If this is not specified and the property is missing, an\n * error will be thrown.\n */\nfunction getArg(aArgs, aName, aDefaultValue) {\n  if (aName in aArgs) {\n    return aArgs[aName];\n  } else if (arguments.length === 3) {\n    return aDefaultValue;\n  } else {\n    throw new Error('\"' + aName + '\" is a required argument.');\n  }\n}\nexports.getArg = getArg;\n\nvar urlRegexp = /^(?:([\\w+\\-.]+):)?\\/\\/(?:(\\w+:\\w+)@)?([\\w.-]*)(?::(\\d+))?(.*)$/;\nvar dataUrlRegexp = /^data:.+\\,.+$/;\n\nfunction urlParse(aUrl) {\n  var match = aUrl.match(urlRegexp);\n  if (!match) {\n    return null;\n  }\n  return {\n    scheme: match[1],\n    auth: match[2],\n    host: match[3],\n    port: match[4],\n    path: match[5]\n  };\n}\nexports.urlParse = urlParse;\n\nfunction urlGenerate(aParsedUrl) {\n  var url = '';\n  if (aParsedUrl.scheme) {\n    url += aParsedUrl.scheme + ':';\n  }\n  url += '//';\n  if (aParsedUrl.auth) {\n    url += aParsedUrl.auth + '@';\n  }\n  if (aParsedUrl.host) {\n    url += aParsedUrl.host;\n  }\n  if (aParsedUrl.port) {\n    url += \":\" + aParsedUrl.port\n  }\n  if (aParsedUrl.path) {\n    url += aParsedUrl.path;\n  }\n  return url;\n}\nexports.urlGenerate = urlGenerate;\n\nvar MAX_CACHED_INPUTS = 32;\n\n/**\n * Takes some function `f(input) -> result` and returns a memoized version of\n * `f`.\n *\n * We keep at most `MAX_CACHED_INPUTS` memoized results of `f` alive. The\n * memoization is a dumb-simple, linear least-recently-used cache.\n */\nfunction lruMemoize(f) {\n  var cache = [];\n\n  return function(input) {\n    for (var i = 0; i < cache.length; i++) {\n      if (cache[i].input === input) {\n        var temp = cache[0];\n        cache[0] = cache[i];\n        cache[i] = temp;\n        return cache[0].result;\n      }\n    }\n\n    var result = f(input);\n\n    cache.unshift({\n      input,\n      result,\n    });\n\n    if (cache.length > MAX_CACHED_INPUTS) {\n      cache.pop();\n    }\n\n    return result;\n  };\n}\n\n/**\n * Normalizes a path, or the path portion of a URL:\n *\n * - Replaces consecutive slashes with one slash.\n * - Removes unnecessary '.' parts.\n * - Removes unnecessary '<dir>/..' parts.\n *\n * Based on code in the Node.js 'path' core module.\n *\n * @param aPath The path or url to normalize.\n */\nvar normalize = lruMemoize(function normalize(aPath) {\n  var path = aPath;\n  var url = urlParse(aPath);\n  if (url) {\n    if (!url.path) {\n      return aPath;\n    }\n    path = url.path;\n  }\n  var isAbsolute = exports.isAbsolute(path);\n  // Split the path into parts between `/` characters. This is much faster than\n  // using `.split(/\\/+/g)`.\n  var parts = [];\n  var start = 0;\n  var i = 0;\n  while (true) {\n    start = i;\n    i = path.indexOf(\"/\", start);\n    if (i === -1) {\n      parts.push(path.slice(start));\n      break;\n    } else {\n      parts.push(path.slice(start, i));\n      while (i < path.length && path[i] === \"/\") {\n        i++;\n      }\n    }\n  }\n\n  for (var part, up = 0, i = parts.length - 1; i >= 0; i--) {\n    part = parts[i];\n    if (part === '.') {\n      parts.splice(i, 1);\n    } else if (part === '..') {\n      up++;\n    } else if (up > 0) {\n      if (part === '') {\n        // The first part is blank if the path is absolute. Trying to go\n        // above the root is a no-op. Therefore we can remove all '..' parts\n        // directly after the root.\n        parts.splice(i + 1, up);\n        up = 0;\n      } else {\n        parts.splice(i, 2);\n        up--;\n      }\n    }\n  }\n  path = parts.join('/');\n\n  if (path === '') {\n    path = isAbsolute ? '/' : '.';\n  }\n\n  if (url) {\n    url.path = path;\n    return urlGenerate(url);\n  }\n  return path;\n});\nexports.normalize = normalize;\n\n/**\n * Joins two paths/URLs.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be joined with the root.\n *\n * - If aPath is a URL or a data URI, aPath is returned, unless aPath is a\n *   scheme-relative URL: Then the scheme of aRoot, if any, is prepended\n *   first.\n * - Otherwise aPath is a path. If aRoot is a URL, then its path portion\n *   is updated with the result and aRoot is returned. Otherwise the result\n *   is returned.\n *   - If aPath is absolute, the result is aPath.\n *   - Otherwise the two paths are joined with a slash.\n * - Joining for example 'http://' and 'www.example.com' is also supported.\n */\nfunction join(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n  if (aPath === \"\") {\n    aPath = \".\";\n  }\n  var aPathUrl = urlParse(aPath);\n  var aRootUrl = urlParse(aRoot);\n  if (aRootUrl) {\n    aRoot = aRootUrl.path || '/';\n  }\n\n  // `join(foo, '//www.example.org')`\n  if (aPathUrl && !aPathUrl.scheme) {\n    if (aRootUrl) {\n      aPathUrl.scheme = aRootUrl.scheme;\n    }\n    return urlGenerate(aPathUrl);\n  }\n\n  if (aPathUrl || aPath.match(dataUrlRegexp)) {\n    return aPath;\n  }\n\n  // `join('http://', 'www.example.com')`\n  if (aRootUrl && !aRootUrl.host && !aRootUrl.path) {\n    aRootUrl.host = aPath;\n    return urlGenerate(aRootUrl);\n  }\n\n  var joined = aPath.charAt(0) === '/'\n    ? aPath\n    : normalize(aRoot.replace(/\\/+$/, '') + '/' + aPath);\n\n  if (aRootUrl) {\n    aRootUrl.path = joined;\n    return urlGenerate(aRootUrl);\n  }\n  return joined;\n}\nexports.join = join;\n\nexports.isAbsolute = function (aPath) {\n  return aPath.charAt(0) === '/' || urlRegexp.test(aPath);\n};\n\n/**\n * Make a path relative to a URL or another path.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be made relative to aRoot.\n */\nfunction relative(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n\n  aRoot = aRoot.replace(/\\/$/, '');\n\n  // It is possible for the path to be above the root. In this case, simply\n  // checking whether the root is a prefix of the path won't work. Instead, we\n  // need to remove components from the root one by one, until either we find\n  // a prefix that fits, or we run out of components to remove.\n  var level = 0;\n  while (aPath.indexOf(aRoot + '/') !== 0) {\n    var index = aRoot.lastIndexOf(\"/\");\n    if (index < 0) {\n      return aPath;\n    }\n\n    // If the only part of the root that is left is the scheme (i.e. http://,\n    // file:///, etc.), one or more slashes (/), or simply nothing at all, we\n    // have exhausted all components, so the path is not relative to the root.\n    aRoot = aRoot.slice(0, index);\n    if (aRoot.match(/^([^\\/]+:\\/)?\\/*$/)) {\n      return aPath;\n    }\n\n    ++level;\n  }\n\n  // Make sure we add a \"../\" for each component we removed from the root.\n  return Array(level + 1).join(\"../\") + aPath.substr(aRoot.length + 1);\n}\nexports.relative = relative;\n\nvar supportsNullProto = (function () {\n  var obj = Object.create(null);\n  return !('__proto__' in obj);\n}());\n\nfunction identity (s) {\n  return s;\n}\n\n/**\n * Because behavior goes wacky when you set `__proto__` on objects, we\n * have to prefix all the strings in our set with an arbitrary character.\n *\n * See https://github.com/mozilla/source-map/pull/31 and\n * https://github.com/mozilla/source-map/issues/30\n *\n * @param String aStr\n */\nfunction toSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return '$' + aStr;\n  }\n\n  return aStr;\n}\nexports.toSetString = supportsNullProto ? identity : toSetString;\n\nfunction fromSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return aStr.slice(1);\n  }\n\n  return aStr;\n}\nexports.fromSetString = supportsNullProto ? identity : fromSetString;\n\nfunction isProtoString(s) {\n  if (!s) {\n    return false;\n  }\n\n  var length = s.length;\n\n  if (length < 9 /* \"__proto__\".length */) {\n    return false;\n  }\n\n  if (s.charCodeAt(length - 1) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 2) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 3) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 4) !== 116 /* 't' */ ||\n      s.charCodeAt(length - 5) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 6) !== 114 /* 'r' */ ||\n      s.charCodeAt(length - 7) !== 112 /* 'p' */ ||\n      s.charCodeAt(length - 8) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 9) !== 95  /* '_' */) {\n    return false;\n  }\n\n  for (var i = length - 10; i >= 0; i--) {\n    if (s.charCodeAt(i) !== 36 /* '$' */) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Comparator between two mappings where the original positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same original source/line/column, but different generated\n * line and column the same. Useful when searching for a mapping with a\n * stubbed out mapping.\n */\nfunction compareByOriginalPositions(mappingA, mappingB, onlyCompareOriginal) {\n  var cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0 || onlyCompareOriginal) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByOriginalPositions = compareByOriginalPositions;\n\nfunction compareByOriginalPositionsNoSource(mappingA, mappingB, onlyCompareOriginal) {\n  var cmp\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0 || onlyCompareOriginal) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByOriginalPositionsNoSource = compareByOriginalPositionsNoSource;\n\n/**\n * Comparator between two mappings with deflated source and name indices where\n * the generated positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same generated line and column, but different\n * source/name/original line and column the same. Useful when searching for a\n * mapping with a stubbed out mapping.\n */\nfunction compareByGeneratedPositionsDeflated(mappingA, mappingB, onlyCompareGenerated) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0 || onlyCompareGenerated) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsDeflated = compareByGeneratedPositionsDeflated;\n\nfunction compareByGeneratedPositionsDeflatedNoLine(mappingA, mappingB, onlyCompareGenerated) {\n  var cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0 || onlyCompareGenerated) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsDeflatedNoLine = compareByGeneratedPositionsDeflatedNoLine;\n\nfunction strcmp(aStr1, aStr2) {\n  if (aStr1 === aStr2) {\n    return 0;\n  }\n\n  if (aStr1 === null) {\n    return 1; // aStr2 !== null\n  }\n\n  if (aStr2 === null) {\n    return -1; // aStr1 !== null\n  }\n\n  if (aStr1 > aStr2) {\n    return 1;\n  }\n\n  return -1;\n}\n\n/**\n * Comparator between two mappings with inflated source and name strings where\n * the generated positions are compared.\n */\nfunction compareByGeneratedPositionsInflated(mappingA, mappingB) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsInflated = compareByGeneratedPositionsInflated;\n\n/**\n * Strip any JSON XSSI avoidance prefix from the string (as documented\n * in the source maps specification), and then parse the string as\n * JSON.\n */\nfunction parseSourceMapInput(str) {\n  return JSON.parse(str.replace(/^\\)]}'[^\\n]*\\n/, ''));\n}\nexports.parseSourceMapInput = parseSourceMapInput;\n\n/**\n * Compute the URL of a source given the the source root, the source's\n * URL, and the source map's URL.\n */\nfunction computeSourceURL(sourceRoot, sourceURL, sourceMapURL) {\n  sourceURL = sourceURL || '';\n\n  if (sourceRoot) {\n    // This follows what Chrome does.\n    if (sourceRoot[sourceRoot.length - 1] !== '/' && sourceURL[0] !== '/') {\n      sourceRoot += '/';\n    }\n    // The spec says:\n    //   Line 4: An optional source root, useful for relocating source\n    //   files on a server or removing repeated values in the\n    //   “sources” entry.  This value is prepended to the individual\n    //   entries in the “source” field.\n    sourceURL = sourceRoot + sourceURL;\n  }\n\n  // Historically, SourceMapConsumer did not take the sourceMapURL as\n  // a parameter.  This mode is still somewhat supported, which is why\n  // this code block is conditional.  However, it's preferable to pass\n  // the source map URL to SourceMapConsumer, so that this function\n  // can implement the source URL resolution algorithm as outlined in\n  // the spec.  This block is basically the equivalent of:\n  //    new URL(sourceURL, sourceMapURL).toString()\n  // ... except it avoids using URL, which wasn't available in the\n  // older releases of node still supported by this library.\n  //\n  // The spec says:\n  //   If the sources are not absolute URLs after prepending of the\n  //   “sourceRoot”, the sources are resolved relative to the\n  //   SourceMap (like resolving script src in a html document).\n  if (sourceMapURL) {\n    var parsed = urlParse(sourceMapURL);\n    if (!parsed) {\n      throw new Error(\"sourceMapURL could not be parsed\");\n    }\n    if (parsed.path) {\n      // Strip the last path component, but keep the \"/\".\n      var index = parsed.path.lastIndexOf('/');\n      if (index >= 0) {\n        parsed.path = parsed.path.substring(0, index + 1);\n      }\n    }\n    sourceURL = join(urlGenerate(parsed), sourceURL);\n  }\n\n  return normalize(sourceURL);\n}\nexports.computeSourceURL = computeSourceURL;\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED;;;;;;;;;CASC,GACD,SAAS,OAAO,KAAK,EAAE,KAAK,EAAE,aAAa;IACzC,IAAI,SAAS,OAAO;QAClB,OAAO,KAAK,CAAC,MAAM;IACrB,OAAO,IAAI,UAAU,MAAM,KAAK,GAAG;QACjC,OAAO;IACT,OAAO;QACL,MAAM,IAAI,MAAM,MAAM,QAAQ;IAChC;AACF;AACA,QAAQ,MAAM,GAAG;AAEjB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AAEpB,SAAS,SAAS,IAAI;IACpB,IAAI,QAAQ,KAAK,KAAK,CAAC;IACvB,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,OAAO;QACL,QAAQ,KAAK,CAAC,EAAE;QAChB,MAAM,KAAK,CAAC,EAAE;QACd,MAAM,KAAK,CAAC,EAAE;QACd,MAAM,KAAK,CAAC,EAAE;QACd,MAAM,KAAK,CAAC,EAAE;IAChB;AACF;AACA,QAAQ,QAAQ,GAAG;AAEnB,SAAS,YAAY,UAAU;IAC7B,IAAI,MAAM;IACV,IAAI,WAAW,MAAM,EAAE;QACrB,OAAO,WAAW,MAAM,GAAG;IAC7B;IACA,OAAO;IACP,IAAI,WAAW,IAAI,EAAE;QACnB,OAAO,WAAW,IAAI,GAAG;IAC3B;IACA,IAAI,WAAW,IAAI,EAAE;QACnB,OAAO,WAAW,IAAI;IACxB;IACA,IAAI,WAAW,IAAI,EAAE;QACnB,OAAO,MAAM,WAAW,IAAI;IAC9B;IACA,IAAI,WAAW,IAAI,EAAE;QACnB,OAAO,WAAW,IAAI;IACxB;IACA,OAAO;AACT;AACA,QAAQ,WAAW,GAAG;AAEtB,IAAI,oBAAoB;AAExB;;;;;;CAMC,GACD,SAAS,WAAW,CAAC;IACnB,IAAI,QAAQ,EAAE;IAEd,OAAO,SAAS,KAAK;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,IAAI,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,OAAO;gBAC5B,IAAI,OAAO,KAAK,CAAC,EAAE;gBACnB,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;gBACnB,KAAK,CAAC,EAAE,GAAG;gBACX,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM;YACxB;QACF;QAEA,IAAI,SAAS,EAAE;QAEf,MAAM,OAAO,CAAC;YACZ;YACA;QACF;QAEA,IAAI,MAAM,MAAM,GAAG,mBAAmB;YACpC,MAAM,GAAG;QACX;QAEA,OAAO;IACT;AACF;AAEA;;;;;;;;;;CAUC,GACD,IAAI,YAAY,WAAW,SAAS,UAAU,KAAK;IACjD,IAAI,OAAO;IACX,IAAI,MAAM,SAAS;IACnB,IAAI,KAAK;QACP,IAAI,CAAC,IAAI,IAAI,EAAE;YACb,OAAO;QACT;QACA,OAAO,IAAI,IAAI;IACjB;IACA,IAAI,aAAa,QAAQ,UAAU,CAAC;IACpC,6EAA6E;IAC7E,0BAA0B;IAC1B,IAAI,QAAQ,EAAE;IACd,IAAI,QAAQ;IACZ,IAAI,IAAI;IACR,MAAO,KAAM;QACX,QAAQ;QACR,IAAI,KAAK,OAAO,CAAC,KAAK;QACtB,IAAI,MAAM,CAAC,GAAG;YACZ,MAAM,IAAI,CAAC,KAAK,KAAK,CAAC;YACtB;QACF,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,KAAK,CAAC,OAAO;YAC7B,MAAO,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,IAAK;gBACzC;YACF;QACF;IACF;IAEA,IAAK,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACxD,OAAO,KAAK,CAAC,EAAE;QACf,IAAI,SAAS,KAAK;YAChB,MAAM,MAAM,CAAC,GAAG;QAClB,OAAO,IAAI,SAAS,MAAM;YACxB;QACF,OAAO,IAAI,KAAK,GAAG;YACjB,IAAI,SAAS,IAAI;gBACf,gEAAgE;gBAChE,oEAAoE;gBACpE,2BAA2B;gBAC3B,MAAM,MAAM,CAAC,IAAI,GAAG;gBACpB,KAAK;YACP,OAAO;gBACL,MAAM,MAAM,CAAC,GAAG;gBAChB;YACF;QACF;IACF;IACA,OAAO,MAAM,IAAI,CAAC;IAElB,IAAI,SAAS,IAAI;QACf,OAAO,aAAa,MAAM;IAC5B;IAEA,IAAI,KAAK;QACP,IAAI,IAAI,GAAG;QACX,OAAO,YAAY;IACrB;IACA,OAAO;AACT;AACA,QAAQ,SAAS,GAAG;AAEpB;;;;;;;;;;;;;;;CAeC,GACD,SAAS,KAAK,KAAK,EAAE,KAAK;IACxB,IAAI,UAAU,IAAI;QAChB,QAAQ;IACV;IACA,IAAI,UAAU,IAAI;QAChB,QAAQ;IACV;IACA,IAAI,WAAW,SAAS;IACxB,IAAI,WAAW,SAAS;IACxB,IAAI,UAAU;QACZ,QAAQ,SAAS,IAAI,IAAI;IAC3B;IAEA,mCAAmC;IACnC,IAAI,YAAY,CAAC,SAAS,MAAM,EAAE;QAChC,IAAI,UAAU;YACZ,SAAS,MAAM,GAAG,SAAS,MAAM;QACnC;QACA,OAAO,YAAY;IACrB;IAEA,IAAI,YAAY,MAAM,KAAK,CAAC,gBAAgB;QAC1C,OAAO;IACT;IAEA,uCAAuC;IACvC,IAAI,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE;QAChD,SAAS,IAAI,GAAG;QAChB,OAAO,YAAY;IACrB;IAEA,IAAI,SAAS,MAAM,MAAM,CAAC,OAAO,MAC7B,QACA,UAAU,MAAM,OAAO,CAAC,QAAQ,MAAM,MAAM;IAEhD,IAAI,UAAU;QACZ,SAAS,IAAI,GAAG;QAChB,OAAO,YAAY;IACrB;IACA,OAAO;AACT;AACA,QAAQ,IAAI,GAAG;AAEf,QAAQ,UAAU,GAAG,SAAU,KAAK;IAClC,OAAO,MAAM,MAAM,CAAC,OAAO,OAAO,UAAU,IAAI,CAAC;AACnD;AAEA;;;;;CAKC,GACD,SAAS,SAAS,KAAK,EAAE,KAAK;IAC5B,IAAI,UAAU,IAAI;QAChB,QAAQ;IACV;IAEA,QAAQ,MAAM,OAAO,CAAC,OAAO;IAE7B,yEAAyE;IACzE,4EAA4E;IAC5E,2EAA2E;IAC3E,6DAA6D;IAC7D,IAAI,QAAQ;IACZ,MAAO,MAAM,OAAO,CAAC,QAAQ,SAAS,EAAG;QACvC,IAAI,QAAQ,MAAM,WAAW,CAAC;QAC9B,IAAI,QAAQ,GAAG;YACb,OAAO;QACT;QAEA,yEAAyE;QACzE,yEAAyE;QACzE,0EAA0E;QAC1E,QAAQ,MAAM,KAAK,CAAC,GAAG;QACvB,IAAI,MAAM,KAAK,CAAC,sBAAsB;YACpC,OAAO;QACT;QAEA,EAAE;IACJ;IAEA,wEAAwE;IACxE,OAAO,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,MAAM,MAAM,CAAC,MAAM,MAAM,GAAG;AACpE;AACA,QAAQ,QAAQ,GAAG;AAEnB,IAAI,oBAAqB;IACvB,IAAI,MAAM,OAAO,MAAM,CAAC;IACxB,OAAO,CAAC,CAAC,eAAe,GAAG;AAC7B;AAEA,SAAS,SAAU,CAAC;IAClB,OAAO;AACT;AAEA;;;;;;;;CAQC,GACD,SAAS,YAAY,IAAI;IACvB,IAAI,cAAc,OAAO;QACvB,OAAO,MAAM;IACf;IAEA,OAAO;AACT;AACA,QAAQ,WAAW,GAAG,oBAAoB,WAAW;AAErD,SAAS,cAAc,IAAI;IACzB,IAAI,cAAc,OAAO;QACvB,OAAO,KAAK,KAAK,CAAC;IACpB;IAEA,OAAO;AACT;AACA,QAAQ,aAAa,GAAG,oBAAoB,WAAW;AAEvD,SAAS,cAAc,CAAC;IACtB,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IAEA,IAAI,SAAS,EAAE,MAAM;IAErB,IAAI,SAAS,EAAE,sBAAsB,KAAI;QACvC,OAAO;IACT;IAEA,IAAI,EAAE,UAAU,CAAC,SAAS,OAAO,GAAI,OAAO,OACxC,EAAE,UAAU,CAAC,SAAS,OAAO,GAAI,OAAO,OACxC,EAAE,UAAU,CAAC,SAAS,OAAO,IAAI,OAAO,OACxC,EAAE,UAAU,CAAC,SAAS,OAAO,IAAI,OAAO,OACxC,EAAE,UAAU,CAAC,SAAS,OAAO,IAAI,OAAO,OACxC,EAAE,UAAU,CAAC,SAAS,OAAO,IAAI,OAAO,OACxC,EAAE,UAAU,CAAC,SAAS,OAAO,IAAI,OAAO,OACxC,EAAE,UAAU,CAAC,SAAS,OAAO,GAAI,OAAO,OACxC,EAAE,UAAU,CAAC,SAAS,OAAO,GAAI,OAAO,KAAI;QAC9C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,SAAS,IAAI,KAAK,GAAG,IAAK;QACrC,IAAI,EAAE,UAAU,CAAC,OAAO,GAAG,OAAO,KAAI;YACpC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,2BAA2B,QAAQ,EAAE,QAAQ,EAAE,mBAAmB;IACzE,IAAI,MAAM,OAAO,SAAS,MAAM,EAAE,SAAS,MAAM;IACjD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,YAAY,GAAG,SAAS,YAAY;IACnD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,cAAc,GAAG,SAAS,cAAc;IACvD,IAAI,QAAQ,KAAK,qBAAqB;QACpC,OAAO;IACT;IAEA,MAAM,SAAS,eAAe,GAAG,SAAS,eAAe;IACzD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,aAAa,GAAG,SAAS,aAAa;IACrD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,OAAO,OAAO,SAAS,IAAI,EAAE,SAAS,IAAI;AAC5C;AACA,QAAQ,0BAA0B,GAAG;AAErC,SAAS,mCAAmC,QAAQ,EAAE,QAAQ,EAAE,mBAAmB;IACjF,IAAI;IAEJ,MAAM,SAAS,YAAY,GAAG,SAAS,YAAY;IACnD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,cAAc,GAAG,SAAS,cAAc;IACvD,IAAI,QAAQ,KAAK,qBAAqB;QACpC,OAAO;IACT;IAEA,MAAM,SAAS,eAAe,GAAG,SAAS,eAAe;IACzD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,aAAa,GAAG,SAAS,aAAa;IACrD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,OAAO,OAAO,SAAS,IAAI,EAAE,SAAS,IAAI;AAC5C;AACA,QAAQ,kCAAkC,GAAG;AAE7C;;;;;;;;CAQC,GACD,SAAS,oCAAoC,QAAQ,EAAE,QAAQ,EAAE,oBAAoB;IACnF,IAAI,MAAM,SAAS,aAAa,GAAG,SAAS,aAAa;IACzD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,eAAe,GAAG,SAAS,eAAe;IACzD,IAAI,QAAQ,KAAK,sBAAsB;QACrC,OAAO;IACT;IAEA,MAAM,OAAO,SAAS,MAAM,EAAE,SAAS,MAAM;IAC7C,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,YAAY,GAAG,SAAS,YAAY;IACnD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,cAAc,GAAG,SAAS,cAAc;IACvD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,OAAO,OAAO,SAAS,IAAI,EAAE,SAAS,IAAI;AAC5C;AACA,QAAQ,mCAAmC,GAAG;AAE9C,SAAS,0CAA0C,QAAQ,EAAE,QAAQ,EAAE,oBAAoB;IACzF,IAAI,MAAM,SAAS,eAAe,GAAG,SAAS,eAAe;IAC7D,IAAI,QAAQ,KAAK,sBAAsB;QACrC,OAAO;IACT;IAEA,MAAM,OAAO,SAAS,MAAM,EAAE,SAAS,MAAM;IAC7C,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,YAAY,GAAG,SAAS,YAAY;IACnD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,cAAc,GAAG,SAAS,cAAc;IACvD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,OAAO,OAAO,SAAS,IAAI,EAAE,SAAS,IAAI;AAC5C;AACA,QAAQ,yCAAyC,GAAG;AAEpD,SAAS,OAAO,KAAK,EAAE,KAAK;IAC1B,IAAI,UAAU,OAAO;QACnB,OAAO;IACT;IAEA,IAAI,UAAU,MAAM;QAClB,OAAO,GAAG,iBAAiB;IAC7B;IAEA,IAAI,UAAU,MAAM;QAClB,OAAO,CAAC,GAAG,iBAAiB;IAC9B;IAEA,IAAI,QAAQ,OAAO;QACjB,OAAO;IACT;IAEA,OAAO,CAAC;AACV;AAEA;;;CAGC,GACD,SAAS,oCAAoC,QAAQ,EAAE,QAAQ;IAC7D,IAAI,MAAM,SAAS,aAAa,GAAG,SAAS,aAAa;IACzD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,eAAe,GAAG,SAAS,eAAe;IACzD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,OAAO,SAAS,MAAM,EAAE,SAAS,MAAM;IAC7C,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,YAAY,GAAG,SAAS,YAAY;IACnD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,MAAM,SAAS,cAAc,GAAG,SAAS,cAAc;IACvD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IAEA,OAAO,OAAO,SAAS,IAAI,EAAE,SAAS,IAAI;AAC5C;AACA,QAAQ,mCAAmC,GAAG;AAE9C;;;;CAIC,GACD,SAAS,oBAAoB,GAAG;IAC9B,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,kBAAkB;AAClD;AACA,QAAQ,mBAAmB,GAAG;AAE9B;;;CAGC,GACD,SAAS,iBAAiB,UAAU,EAAE,SAAS,EAAE,YAAY;IAC3D,YAAY,aAAa;IAEzB,IAAI,YAAY;QACd,iCAAiC;QACjC,IAAI,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE,KAAK,KAAK;YACrE,cAAc;QAChB;QACA,iBAAiB;QACjB,kEAAkE;QAClE,yDAAyD;QACzD,gEAAgE;QAChE,mCAAmC;QACnC,YAAY,aAAa;IAC3B;IAEA,mEAAmE;IACnE,oEAAoE;IACpE,oEAAoE;IACpE,iEAAiE;IACjE,mEAAmE;IACnE,wDAAwD;IACxD,iDAAiD;IACjD,gEAAgE;IAChE,0DAA0D;IAC1D,EAAE;IACF,iBAAiB;IACjB,iEAAiE;IACjE,2DAA2D;IAC3D,8DAA8D;IAC9D,IAAI,cAAc;QAChB,IAAI,SAAS,SAAS;QACtB,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,OAAO,IAAI,EAAE;YACf,mDAAmD;YACnD,IAAI,QAAQ,OAAO,IAAI,CAAC,WAAW,CAAC;YACpC,IAAI,SAAS,GAAG;gBACd,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,QAAQ;YACjD;QACF;QACA,YAAY,KAAK,YAAY,SAAS;IACxC;IAEA,OAAO,UAAU;AACnB;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0]}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/array-set.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar has = Object.prototype.hasOwnProperty;\nvar hasNativeMap = typeof Map !== \"undefined\";\n\n/**\n * A data structure which is a combination of an array and a set. Adding a new\n * member is O(1), testing for membership is O(1), and finding the index of an\n * element is O(1). Removing elements from the set is not supported. Only\n * strings are supported for membership.\n */\nfunction ArraySet() {\n  this._array = [];\n  this._set = hasNativeMap ? new Map() : Object.create(null);\n}\n\n/**\n * Static method for creating ArraySet instances from an existing array.\n */\nArraySet.fromArray = function ArraySet_fromArray(aArray, aAllowDuplicates) {\n  var set = new ArraySet();\n  for (var i = 0, len = aArray.length; i < len; i++) {\n    set.add(aArray[i], aAllowDuplicates);\n  }\n  return set;\n};\n\n/**\n * Return how many unique items are in this ArraySet. If duplicates have been\n * added, than those do not count towards the size.\n *\n * @returns Number\n */\nArraySet.prototype.size = function ArraySet_size() {\n  return hasNativeMap ? this._set.size : Object.getOwnPropertyNames(this._set).length;\n};\n\n/**\n * Add the given string to this set.\n *\n * @param String aStr\n */\nArraySet.prototype.add = function ArraySet_add(aStr, aAllowDuplicates) {\n  var sStr = hasNativeMap ? aStr : util.toSetString(aStr);\n  var isDuplicate = hasNativeMap ? this.has(aStr) : has.call(this._set, sStr);\n  var idx = this._array.length;\n  if (!isDuplicate || aAllowDuplicates) {\n    this._array.push(aStr);\n  }\n  if (!isDuplicate) {\n    if (hasNativeMap) {\n      this._set.set(aStr, idx);\n    } else {\n      this._set[sStr] = idx;\n    }\n  }\n};\n\n/**\n * Is the given string a member of this set?\n *\n * @param String aStr\n */\nArraySet.prototype.has = function ArraySet_has(aStr) {\n  if (hasNativeMap) {\n    return this._set.has(aStr);\n  } else {\n    var sStr = util.toSetString(aStr);\n    return has.call(this._set, sStr);\n  }\n};\n\n/**\n * What is the index of the given string in the array?\n *\n * @param String aStr\n */\nArraySet.prototype.indexOf = function ArraySet_indexOf(aStr) {\n  if (hasNativeMap) {\n    var idx = this._set.get(aStr);\n    if (idx >= 0) {\n        return idx;\n    }\n  } else {\n    var sStr = util.toSetString(aStr);\n    if (has.call(this._set, sStr)) {\n      return this._set[sStr];\n    }\n  }\n\n  throw new Error('\"' + aStr + '\" is not in the set.');\n};\n\n/**\n * What is the element at the given index?\n *\n * @param Number aIdx\n */\nArraySet.prototype.at = function ArraySet_at(aIdx) {\n  if (aIdx >= 0 && aIdx < this._array.length) {\n    return this._array[aIdx];\n  }\n  throw new Error('No element indexed by ' + aIdx);\n};\n\n/**\n * Returns the array representation of this set (which has the proper indices\n * indicated by indexOf). Note that this is a copy of the internal array used\n * for storing the members so that no one can mess with internal state.\n */\nArraySet.prototype.toArray = function ArraySet_toArray() {\n  return this._array.slice();\n};\n\nexports.ArraySet = ArraySet;\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED,IAAI;AACJ,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AACzC,IAAI,eAAe,OAAO,QAAQ;AAElC;;;;;CAKC,GACD,SAAS;IACP,IAAI,CAAC,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC,IAAI,GAAG,eAAe,IAAI,QAAQ,OAAO,MAAM,CAAC;AACvD;AAEA;;CAEC,GACD,SAAS,SAAS,GAAG,SAAS,mBAAmB,MAAM,EAAE,gBAAgB;IACvE,IAAI,MAAM,IAAI;IACd,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;IACrB;IACA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,SAAS,CAAC,IAAI,GAAG,SAAS;IACjC,OAAO,eAAe,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM;AACrF;AAEA;;;;CAIC,GACD,SAAS,SAAS,CAAC,GAAG,GAAG,SAAS,aAAa,IAAI,EAAE,gBAAgB;IACnE,IAAI,OAAO,eAAe,OAAO,KAAK,WAAW,CAAC;IAClD,IAAI,cAAc,eAAe,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;IACtE,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;IAC5B,IAAI,CAAC,eAAe,kBAAkB;QACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACnB;IACA,IAAI,CAAC,aAAa;QAChB,IAAI,cAAc;YAChB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM;QACtB,OAAO;YACL,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;QACpB;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,SAAS,CAAC,GAAG,GAAG,SAAS,aAAa,IAAI;IACjD,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;IACvB,OAAO;QACL,IAAI,OAAO,KAAK,WAAW,CAAC;QAC5B,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;IAC7B;AACF;AAEA;;;;CAIC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG,SAAS,iBAAiB,IAAI;IACzD,IAAI,cAAc;QAChB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACxB,IAAI,OAAO,GAAG;YACV,OAAO;QACX;IACF,OAAO;QACL,IAAI,OAAO,KAAK,WAAW,CAAC;QAC5B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;YAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;QACxB;IACF;IAEA,MAAM,IAAI,MAAM,MAAM,OAAO;AAC/B;AAEA;;;;CAIC,GACD,SAAS,SAAS,CAAC,EAAE,GAAG,SAAS,YAAY,IAAI;IAC/C,IAAI,QAAQ,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;IAC1B;IACA,MAAM,IAAI,MAAM,2BAA2B;AAC7C;AAEA;;;;CAIC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG,SAAS;IACpC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;AAC1B;AAEA,QAAQ,QAAQ,GAAG", "ignoreList": [0]}}, {"offset": {"line": 2491, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/mapping-list.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2014 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\n\n/**\n * Determine whether mappingB is after mappingA with respect to generated\n * position.\n */\nfunction generatedPositionAfter(mappingA, mappingB) {\n  // Optimized for most common case\n  var lineA = mappingA.generatedLine;\n  var lineB = mappingB.generatedLine;\n  var columnA = mappingA.generatedColumn;\n  var columnB = mappingB.generatedColumn;\n  return lineB > lineA || lineB == lineA && columnB >= columnA ||\n         util.compareByGeneratedPositionsInflated(mappingA, mappingB) <= 0;\n}\n\n/**\n * A data structure to provide a sorted view of accumulated mappings in a\n * performance conscious manner. It trades a neglibable overhead in general\n * case for a large speedup in case of mappings being added in order.\n */\nfunction MappingList() {\n  this._array = [];\n  this._sorted = true;\n  // Serves as infimum\n  this._last = {generatedLine: -1, generatedColumn: 0};\n}\n\n/**\n * Iterate through internal items. This method takes the same arguments that\n * `Array.prototype.forEach` takes.\n *\n * NOTE: The order of the mappings is NOT guaranteed.\n */\nMappingList.prototype.unsortedForEach =\n  function MappingList_forEach(aCallback, aThisArg) {\n    this._array.forEach(aCallback, aThisArg);\n  };\n\n/**\n * Add the given source mapping.\n *\n * @param Object aMapping\n */\nMappingList.prototype.add = function MappingList_add(aMapping) {\n  if (generatedPositionAfter(this._last, aMapping)) {\n    this._last = aMapping;\n    this._array.push(aMapping);\n  } else {\n    this._sorted = false;\n    this._array.push(aMapping);\n  }\n};\n\n/**\n * Returns the flat, sorted array of mappings. The mappings are sorted by\n * generated position.\n *\n * WARNING: This method returns internal data without copying, for\n * performance. The return value must NOT be mutated, and should be treated as\n * an immutable borrow. If you want to take ownership, you must make your own\n * copy.\n */\nMappingList.prototype.toArray = function MappingList_toArray() {\n  if (!this._sorted) {\n    this._array.sort(util.compareByGeneratedPositionsInflated);\n    this._sorted = true;\n  }\n  return this._array;\n};\n\nexports.MappingList = MappingList;\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED,IAAI;AAEJ;;;CAGC,GACD,SAAS,uBAAuB,QAAQ,EAAE,QAAQ;IAChD,iCAAiC;IACjC,IAAI,QAAQ,SAAS,aAAa;IAClC,IAAI,QAAQ,SAAS,aAAa;IAClC,IAAI,UAAU,SAAS,eAAe;IACtC,IAAI,UAAU,SAAS,eAAe;IACtC,OAAO,QAAQ,SAAS,SAAS,SAAS,WAAW,WAC9C,KAAK,mCAAmC,CAAC,UAAU,aAAa;AACzE;AAEA;;;;CAIC,GACD,SAAS;IACP,IAAI,CAAC,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC,OAAO,GAAG;IACf,oBAAoB;IACpB,IAAI,CAAC,KAAK,GAAG;QAAC,eAAe,CAAC;QAAG,iBAAiB;IAAC;AACrD;AAEA;;;;;CAKC,GACD,YAAY,SAAS,CAAC,eAAe,GACnC,SAAS,oBAAoB,SAAS,EAAE,QAAQ;IAC9C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW;AACjC;AAEF;;;;CAIC,GACD,YAAY,SAAS,CAAC,GAAG,GAAG,SAAS,gBAAgB,QAAQ;IAC3D,IAAI,uBAAuB,IAAI,CAAC,KAAK,EAAE,WAAW;QAChD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACnB,OAAO;QACL,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACnB;AACF;AAEA;;;;;;;;CAQC,GACD,YAAY,SAAS,CAAC,OAAO,GAAG,SAAS;IACvC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,mCAAmC;QACzD,IAAI,CAAC,OAAO,GAAG;IACjB;IACA,OAAO,IAAI,CAAC,MAAM;AACpB;AAEA,QAAQ,WAAW,GAAG", "ignoreList": [0]}}, {"offset": {"line": 2562, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/source-map-generator.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar base64VLQ = require('./base64-vlq');\nvar util = require('./util');\nvar ArraySet = require('./array-set').ArraySet;\nvar MappingList = require('./mapping-list').MappingList;\n\n/**\n * An instance of the SourceMapGenerator represents a source map which is\n * being built incrementally. You may pass an object with the following\n * properties:\n *\n *   - file: The filename of the generated source.\n *   - sourceRoot: A root for all relative URLs in this source map.\n */\nfunction SourceMapGenerator(aArgs) {\n  if (!aArgs) {\n    aArgs = {};\n  }\n  this._file = util.getArg(aArgs, 'file', null);\n  this._sourceRoot = util.getArg(aArgs, 'sourceRoot', null);\n  this._skipValidation = util.getArg(aArgs, 'skipValidation', false);\n  this._ignoreInvalidMapping = util.getArg(aArgs, 'ignoreInvalidMapping', false);\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n  this._mappings = new MappingList();\n  this._sourcesContents = null;\n}\n\nSourceMapGenerator.prototype._version = 3;\n\n/**\n * Creates a new SourceMapGenerator based on a SourceMapConsumer\n *\n * @param aSourceMapConsumer The SourceMap.\n */\nSourceMapGenerator.fromSourceMap =\n  function SourceMapGenerator_fromSourceMap(aSourceMapConsumer, generatorOps) {\n    var sourceRoot = aSourceMapConsumer.sourceRoot;\n    var generator = new SourceMapGenerator(Object.assign(generatorOps || {}, {\n      file: aSourceMapConsumer.file,\n      sourceRoot: sourceRoot\n    }));\n    aSourceMapConsumer.eachMapping(function (mapping) {\n      var newMapping = {\n        generated: {\n          line: mapping.generatedLine,\n          column: mapping.generatedColumn\n        }\n      };\n\n      if (mapping.source != null) {\n        newMapping.source = mapping.source;\n        if (sourceRoot != null) {\n          newMapping.source = util.relative(sourceRoot, newMapping.source);\n        }\n\n        newMapping.original = {\n          line: mapping.originalLine,\n          column: mapping.originalColumn\n        };\n\n        if (mapping.name != null) {\n          newMapping.name = mapping.name;\n        }\n      }\n\n      generator.addMapping(newMapping);\n    });\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var sourceRelative = sourceFile;\n      if (sourceRoot !== null) {\n        sourceRelative = util.relative(sourceRoot, sourceFile);\n      }\n\n      if (!generator._sources.has(sourceRelative)) {\n        generator._sources.add(sourceRelative);\n      }\n\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        generator.setSourceContent(sourceFile, content);\n      }\n    });\n    return generator;\n  };\n\n/**\n * Add a single mapping from original source line and column to the generated\n * source's line and column for this source map being created. The mapping\n * object should have the following properties:\n *\n *   - generated: An object with the generated line and column positions.\n *   - original: An object with the original line and column positions.\n *   - source: The original source file (relative to the sourceRoot).\n *   - name: An optional original token name for this mapping.\n */\nSourceMapGenerator.prototype.addMapping =\n  function SourceMapGenerator_addMapping(aArgs) {\n    var generated = util.getArg(aArgs, 'generated');\n    var original = util.getArg(aArgs, 'original', null);\n    var source = util.getArg(aArgs, 'source', null);\n    var name = util.getArg(aArgs, 'name', null);\n\n    if (!this._skipValidation) {\n      if (this._validateMapping(generated, original, source, name) === false) {\n        return;\n      }\n    }\n\n    if (source != null) {\n      source = String(source);\n      if (!this._sources.has(source)) {\n        this._sources.add(source);\n      }\n    }\n\n    if (name != null) {\n      name = String(name);\n      if (!this._names.has(name)) {\n        this._names.add(name);\n      }\n    }\n\n    this._mappings.add({\n      generatedLine: generated.line,\n      generatedColumn: generated.column,\n      originalLine: original != null && original.line,\n      originalColumn: original != null && original.column,\n      source: source,\n      name: name\n    });\n  };\n\n/**\n * Set the source content for a source file.\n */\nSourceMapGenerator.prototype.setSourceContent =\n  function SourceMapGenerator_setSourceContent(aSourceFile, aSourceContent) {\n    var source = aSourceFile;\n    if (this._sourceRoot != null) {\n      source = util.relative(this._sourceRoot, source);\n    }\n\n    if (aSourceContent != null) {\n      // Add the source content to the _sourcesContents map.\n      // Create a new _sourcesContents map if the property is null.\n      if (!this._sourcesContents) {\n        this._sourcesContents = Object.create(null);\n      }\n      this._sourcesContents[util.toSetString(source)] = aSourceContent;\n    } else if (this._sourcesContents) {\n      // Remove the source file from the _sourcesContents map.\n      // If the _sourcesContents map is empty, set the property to null.\n      delete this._sourcesContents[util.toSetString(source)];\n      if (Object.keys(this._sourcesContents).length === 0) {\n        this._sourcesContents = null;\n      }\n    }\n  };\n\n/**\n * Applies the mappings of a sub-source-map for a specific source file to the\n * source map being generated. Each mapping to the supplied source file is\n * rewritten using the supplied source map. Note: The resolution for the\n * resulting mappings is the minimium of this map and the supplied map.\n *\n * @param aSourceMapConsumer The source map to be applied.\n * @param aSourceFile Optional. The filename of the source file.\n *        If omitted, SourceMapConsumer's file property will be used.\n * @param aSourceMapPath Optional. The dirname of the path to the source map\n *        to be applied. If relative, it is relative to the SourceMapConsumer.\n *        This parameter is needed when the two source maps aren't in the same\n *        directory, and the source map to be applied contains relative source\n *        paths. If so, those relative source paths need to be rewritten\n *        relative to the SourceMapGenerator.\n */\nSourceMapGenerator.prototype.applySourceMap =\n  function SourceMapGenerator_applySourceMap(aSourceMapConsumer, aSourceFile, aSourceMapPath) {\n    var sourceFile = aSourceFile;\n    // If aSourceFile is omitted, we will use the file property of the SourceMap\n    if (aSourceFile == null) {\n      if (aSourceMapConsumer.file == null) {\n        throw new Error(\n          'SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, ' +\n          'or the source map\\'s \"file\" property. Both were omitted.'\n        );\n      }\n      sourceFile = aSourceMapConsumer.file;\n    }\n    var sourceRoot = this._sourceRoot;\n    // Make \"sourceFile\" relative if an absolute Url is passed.\n    if (sourceRoot != null) {\n      sourceFile = util.relative(sourceRoot, sourceFile);\n    }\n    // Applying the SourceMap can add and remove items from the sources and\n    // the names array.\n    var newSources = new ArraySet();\n    var newNames = new ArraySet();\n\n    // Find mappings for the \"sourceFile\"\n    this._mappings.unsortedForEach(function (mapping) {\n      if (mapping.source === sourceFile && mapping.originalLine != null) {\n        // Check if it can be mapped by the source map, then update the mapping.\n        var original = aSourceMapConsumer.originalPositionFor({\n          line: mapping.originalLine,\n          column: mapping.originalColumn\n        });\n        if (original.source != null) {\n          // Copy mapping\n          mapping.source = original.source;\n          if (aSourceMapPath != null) {\n            mapping.source = util.join(aSourceMapPath, mapping.source)\n          }\n          if (sourceRoot != null) {\n            mapping.source = util.relative(sourceRoot, mapping.source);\n          }\n          mapping.originalLine = original.line;\n          mapping.originalColumn = original.column;\n          if (original.name != null) {\n            mapping.name = original.name;\n          }\n        }\n      }\n\n      var source = mapping.source;\n      if (source != null && !newSources.has(source)) {\n        newSources.add(source);\n      }\n\n      var name = mapping.name;\n      if (name != null && !newNames.has(name)) {\n        newNames.add(name);\n      }\n\n    }, this);\n    this._sources = newSources;\n    this._names = newNames;\n\n    // Copy sourcesContents of applied map.\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        if (aSourceMapPath != null) {\n          sourceFile = util.join(aSourceMapPath, sourceFile);\n        }\n        if (sourceRoot != null) {\n          sourceFile = util.relative(sourceRoot, sourceFile);\n        }\n        this.setSourceContent(sourceFile, content);\n      }\n    }, this);\n  };\n\n/**\n * A mapping can have one of the three levels of data:\n *\n *   1. Just the generated position.\n *   2. The Generated position, original position, and original source.\n *   3. Generated and original position, original source, as well as a name\n *      token.\n *\n * To maintain consistency, we validate that any new mapping being added falls\n * in to one of these categories.\n */\nSourceMapGenerator.prototype._validateMapping =\n  function SourceMapGenerator_validateMapping(aGenerated, aOriginal, aSource,\n                                              aName) {\n    // When aOriginal is truthy but has empty values for .line and .column,\n    // it is most likely a programmer error. In this case we throw a very\n    // specific error message to try to guide them the right way.\n    // For example: https://github.com/Polymer/polymer-bundler/pull/519\n    if (aOriginal && typeof aOriginal.line !== 'number' && typeof aOriginal.column !== 'number') {\n      var message = 'original.line and original.column are not numbers -- you probably meant to omit ' +\n      'the original mapping entirely and only map the generated position. If so, pass ' +\n      'null for the original mapping instead of an object with empty or null values.'\n\n      if (this._ignoreInvalidMapping) {\n        if (typeof console !== 'undefined' && console.warn) {\n          console.warn(message);\n        }\n        return false;\n      } else {\n        throw new Error(message);\n      }\n    }\n\n    if (aGenerated && 'line' in aGenerated && 'column' in aGenerated\n        && aGenerated.line > 0 && aGenerated.column >= 0\n        && !aOriginal && !aSource && !aName) {\n      // Case 1.\n      return;\n    }\n    else if (aGenerated && 'line' in aGenerated && 'column' in aGenerated\n             && aOriginal && 'line' in aOriginal && 'column' in aOriginal\n             && aGenerated.line > 0 && aGenerated.column >= 0\n             && aOriginal.line > 0 && aOriginal.column >= 0\n             && aSource) {\n      // Cases 2 and 3.\n      return;\n    }\n    else {\n      var message = 'Invalid mapping: ' + JSON.stringify({\n        generated: aGenerated,\n        source: aSource,\n        original: aOriginal,\n        name: aName\n      });\n\n      if (this._ignoreInvalidMapping) {\n        if (typeof console !== 'undefined' && console.warn) {\n          console.warn(message);\n        }\n        return false;\n      } else {\n        throw new Error(message)\n      }\n    }\n  };\n\n/**\n * Serialize the accumulated mappings in to the stream of base 64 VLQs\n * specified by the source map format.\n */\nSourceMapGenerator.prototype._serializeMappings =\n  function SourceMapGenerator_serializeMappings() {\n    var previousGeneratedColumn = 0;\n    var previousGeneratedLine = 1;\n    var previousOriginalColumn = 0;\n    var previousOriginalLine = 0;\n    var previousName = 0;\n    var previousSource = 0;\n    var result = '';\n    var next;\n    var mapping;\n    var nameIdx;\n    var sourceIdx;\n\n    var mappings = this._mappings.toArray();\n    for (var i = 0, len = mappings.length; i < len; i++) {\n      mapping = mappings[i];\n      next = ''\n\n      if (mapping.generatedLine !== previousGeneratedLine) {\n        previousGeneratedColumn = 0;\n        while (mapping.generatedLine !== previousGeneratedLine) {\n          next += ';';\n          previousGeneratedLine++;\n        }\n      }\n      else {\n        if (i > 0) {\n          if (!util.compareByGeneratedPositionsInflated(mapping, mappings[i - 1])) {\n            continue;\n          }\n          next += ',';\n        }\n      }\n\n      next += base64VLQ.encode(mapping.generatedColumn\n                                 - previousGeneratedColumn);\n      previousGeneratedColumn = mapping.generatedColumn;\n\n      if (mapping.source != null) {\n        sourceIdx = this._sources.indexOf(mapping.source);\n        next += base64VLQ.encode(sourceIdx - previousSource);\n        previousSource = sourceIdx;\n\n        // lines are stored 0-based in SourceMap spec version 3\n        next += base64VLQ.encode(mapping.originalLine - 1\n                                   - previousOriginalLine);\n        previousOriginalLine = mapping.originalLine - 1;\n\n        next += base64VLQ.encode(mapping.originalColumn\n                                   - previousOriginalColumn);\n        previousOriginalColumn = mapping.originalColumn;\n\n        if (mapping.name != null) {\n          nameIdx = this._names.indexOf(mapping.name);\n          next += base64VLQ.encode(nameIdx - previousName);\n          previousName = nameIdx;\n        }\n      }\n\n      result += next;\n    }\n\n    return result;\n  };\n\nSourceMapGenerator.prototype._generateSourcesContent =\n  function SourceMapGenerator_generateSourcesContent(aSources, aSourceRoot) {\n    return aSources.map(function (source) {\n      if (!this._sourcesContents) {\n        return null;\n      }\n      if (aSourceRoot != null) {\n        source = util.relative(aSourceRoot, source);\n      }\n      var key = util.toSetString(source);\n      return Object.prototype.hasOwnProperty.call(this._sourcesContents, key)\n        ? this._sourcesContents[key]\n        : null;\n    }, this);\n  };\n\n/**\n * Externalize the source map.\n */\nSourceMapGenerator.prototype.toJSON =\n  function SourceMapGenerator_toJSON() {\n    var map = {\n      version: this._version,\n      sources: this._sources.toArray(),\n      names: this._names.toArray(),\n      mappings: this._serializeMappings()\n    };\n    if (this._file != null) {\n      map.file = this._file;\n    }\n    if (this._sourceRoot != null) {\n      map.sourceRoot = this._sourceRoot;\n    }\n    if (this._sourcesContents) {\n      map.sourcesContent = this._generateSourcesContent(map.sources, map.sourceRoot);\n    }\n\n    return map;\n  };\n\n/**\n * Render the source map being generated to a string.\n */\nSourceMapGenerator.prototype.toString =\n  function SourceMapGenerator_toString() {\n    return JSON.stringify(this.toJSON());\n  };\n\nexports.SourceMapGenerator = SourceMapGenerator;\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED,IAAI;AACJ,IAAI;AACJ,IAAI,WAAW,wGAAuB,QAAQ;AAC9C,IAAI,cAAc,2GAA0B,WAAW;AAEvD;;;;;;;CAOC,GACD,SAAS,mBAAmB,KAAK;IAC/B,IAAI,CAAC,OAAO;QACV,QAAQ,CAAC;IACX;IACA,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,OAAO,QAAQ;IACxC,IAAI,CAAC,WAAW,GAAG,KAAK,MAAM,CAAC,OAAO,cAAc;IACpD,IAAI,CAAC,eAAe,GAAG,KAAK,MAAM,CAAC,OAAO,kBAAkB;IAC5D,IAAI,CAAC,qBAAqB,GAAG,KAAK,MAAM,CAAC,OAAO,wBAAwB;IACxE,IAAI,CAAC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC,gBAAgB,GAAG;AAC1B;AAEA,mBAAmB,SAAS,CAAC,QAAQ,GAAG;AAExC;;;;CAIC,GACD,mBAAmB,aAAa,GAC9B,SAAS,iCAAiC,kBAAkB,EAAE,YAAY;IACxE,IAAI,aAAa,mBAAmB,UAAU;IAC9C,IAAI,YAAY,IAAI,mBAAmB,OAAO,MAAM,CAAC,gBAAgB,CAAC,GAAG;QACvE,MAAM,mBAAmB,IAAI;QAC7B,YAAY;IACd;IACA,mBAAmB,WAAW,CAAC,SAAU,OAAO;QAC9C,IAAI,aAAa;YACf,WAAW;gBACT,MAAM,QAAQ,aAAa;gBAC3B,QAAQ,QAAQ,eAAe;YACjC;QACF;QAEA,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,WAAW,MAAM,GAAG,QAAQ,MAAM;YAClC,IAAI,cAAc,MAAM;gBACtB,WAAW,MAAM,GAAG,KAAK,QAAQ,CAAC,YAAY,WAAW,MAAM;YACjE;YAEA,WAAW,QAAQ,GAAG;gBACpB,MAAM,QAAQ,YAAY;gBAC1B,QAAQ,QAAQ,cAAc;YAChC;YAEA,IAAI,QAAQ,IAAI,IAAI,MAAM;gBACxB,WAAW,IAAI,GAAG,QAAQ,IAAI;YAChC;QACF;QAEA,UAAU,UAAU,CAAC;IACvB;IACA,mBAAmB,OAAO,CAAC,OAAO,CAAC,SAAU,UAAU;QACrD,IAAI,iBAAiB;QACrB,IAAI,eAAe,MAAM;YACvB,iBAAiB,KAAK,QAAQ,CAAC,YAAY;QAC7C;QAEA,IAAI,CAAC,UAAU,QAAQ,CAAC,GAAG,CAAC,iBAAiB;YAC3C,UAAU,QAAQ,CAAC,GAAG,CAAC;QACzB;QAEA,IAAI,UAAU,mBAAmB,gBAAgB,CAAC;QAClD,IAAI,WAAW,MAAM;YACnB,UAAU,gBAAgB,CAAC,YAAY;QACzC;IACF;IACA,OAAO;AACT;AAEF;;;;;;;;;CASC,GACD,mBAAmB,SAAS,CAAC,UAAU,GACrC,SAAS,8BAA8B,KAAK;IAC1C,IAAI,YAAY,KAAK,MAAM,CAAC,OAAO;IACnC,IAAI,WAAW,KAAK,MAAM,CAAC,OAAO,YAAY;IAC9C,IAAI,SAAS,KAAK,MAAM,CAAC,OAAO,UAAU;IAC1C,IAAI,OAAO,KAAK,MAAM,CAAC,OAAO,QAAQ;IAEtC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;QACzB,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,UAAU,QAAQ,UAAU,OAAO;YACtE;QACF;IACF;IAEA,IAAI,UAAU,MAAM;QAClB,SAAS,OAAO;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS;YAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QACpB;IACF;IAEA,IAAI,QAAQ,MAAM;QAChB,OAAO,OAAO;QACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAClB;IACF;IAEA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACjB,eAAe,UAAU,IAAI;QAC7B,iBAAiB,UAAU,MAAM;QACjC,cAAc,YAAY,QAAQ,SAAS,IAAI;QAC/C,gBAAgB,YAAY,QAAQ,SAAS,MAAM;QACnD,QAAQ;QACR,MAAM;IACR;AACF;AAEF;;CAEC,GACD,mBAAmB,SAAS,CAAC,gBAAgB,GAC3C,SAAS,oCAAoC,WAAW,EAAE,cAAc;IACtE,IAAI,SAAS;IACb,IAAI,IAAI,CAAC,WAAW,IAAI,MAAM;QAC5B,SAAS,KAAK,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE;IAC3C;IAEA,IAAI,kBAAkB,MAAM;QAC1B,sDAAsD;QACtD,6DAA6D;QAC7D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,gBAAgB,GAAG,OAAO,MAAM,CAAC;QACxC;QACA,IAAI,CAAC,gBAAgB,CAAC,KAAK,WAAW,CAAC,QAAQ,GAAG;IACpD,OAAO,IAAI,IAAI,CAAC,gBAAgB,EAAE;QAChC,wDAAwD;QACxD,kEAAkE;QAClE,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,WAAW,CAAC,QAAQ;QACtD,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,KAAK,GAAG;YACnD,IAAI,CAAC,gBAAgB,GAAG;QAC1B;IACF;AACF;AAEF;;;;;;;;;;;;;;;CAeC,GACD,mBAAmB,SAAS,CAAC,cAAc,GACzC,SAAS,kCAAkC,kBAAkB,EAAE,WAAW,EAAE,cAAc;IACxF,IAAI,aAAa;IACjB,4EAA4E;IAC5E,IAAI,eAAe,MAAM;QACvB,IAAI,mBAAmB,IAAI,IAAI,MAAM;YACnC,MAAM,IAAI,MACR,0FACA;QAEJ;QACA,aAAa,mBAAmB,IAAI;IACtC;IACA,IAAI,aAAa,IAAI,CAAC,WAAW;IACjC,2DAA2D;IAC3D,IAAI,cAAc,MAAM;QACtB,aAAa,KAAK,QAAQ,CAAC,YAAY;IACzC;IACA,uEAAuE;IACvE,mBAAmB;IACnB,IAAI,aAAa,IAAI;IACrB,IAAI,WAAW,IAAI;IAEnB,qCAAqC;IACrC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAU,OAAO;QAC9C,IAAI,QAAQ,MAAM,KAAK,cAAc,QAAQ,YAAY,IAAI,MAAM;YACjE,wEAAwE;YACxE,IAAI,WAAW,mBAAmB,mBAAmB,CAAC;gBACpD,MAAM,QAAQ,YAAY;gBAC1B,QAAQ,QAAQ,cAAc;YAChC;YACA,IAAI,SAAS,MAAM,IAAI,MAAM;gBAC3B,eAAe;gBACf,QAAQ,MAAM,GAAG,SAAS,MAAM;gBAChC,IAAI,kBAAkB,MAAM;oBAC1B,QAAQ,MAAM,GAAG,KAAK,IAAI,CAAC,gBAAgB,QAAQ,MAAM;gBAC3D;gBACA,IAAI,cAAc,MAAM;oBACtB,QAAQ,MAAM,GAAG,KAAK,QAAQ,CAAC,YAAY,QAAQ,MAAM;gBAC3D;gBACA,QAAQ,YAAY,GAAG,SAAS,IAAI;gBACpC,QAAQ,cAAc,GAAG,SAAS,MAAM;gBACxC,IAAI,SAAS,IAAI,IAAI,MAAM;oBACzB,QAAQ,IAAI,GAAG,SAAS,IAAI;gBAC9B;YACF;QACF;QAEA,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,UAAU,QAAQ,CAAC,WAAW,GAAG,CAAC,SAAS;YAC7C,WAAW,GAAG,CAAC;QACjB;QAEA,IAAI,OAAO,QAAQ,IAAI;QACvB,IAAI,QAAQ,QAAQ,CAAC,SAAS,GAAG,CAAC,OAAO;YACvC,SAAS,GAAG,CAAC;QACf;IAEF,GAAG,IAAI;IACP,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG;IAEd,uCAAuC;IACvC,mBAAmB,OAAO,CAAC,OAAO,CAAC,SAAU,UAAU;QACrD,IAAI,UAAU,mBAAmB,gBAAgB,CAAC;QAClD,IAAI,WAAW,MAAM;YACnB,IAAI,kBAAkB,MAAM;gBAC1B,aAAa,KAAK,IAAI,CAAC,gBAAgB;YACzC;YACA,IAAI,cAAc,MAAM;gBACtB,aAAa,KAAK,QAAQ,CAAC,YAAY;YACzC;YACA,IAAI,CAAC,gBAAgB,CAAC,YAAY;QACpC;IACF,GAAG,IAAI;AACT;AAEF;;;;;;;;;;CAUC,GACD,mBAAmB,SAAS,CAAC,gBAAgB,GAC3C,SAAS,mCAAmC,UAAU,EAAE,SAAS,EAAE,OAAO,EAC9B,KAAK;IAC/C,uEAAuE;IACvE,qEAAqE;IACrE,6DAA6D;IAC7D,mEAAmE;IACnE,IAAI,aAAa,OAAO,UAAU,IAAI,KAAK,YAAY,OAAO,UAAU,MAAM,KAAK,UAAU;QAC3F,IAAI,UAAU,qFACd,oFACA;QAEA,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,IAAI,OAAO,YAAY,eAAe,QAAQ,IAAI,EAAE;gBAClD,QAAQ,IAAI,CAAC;YACf;YACA,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,IAAI,cAAc,UAAU,cAAc,YAAY,cAC/C,WAAW,IAAI,GAAG,KAAK,WAAW,MAAM,IAAI,KAC5C,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO;QACvC,UAAU;QACV;IACF,OACK,IAAI,cAAc,UAAU,cAAc,YAAY,cAC/C,aAAa,UAAU,aAAa,YAAY,aAChD,WAAW,IAAI,GAAG,KAAK,WAAW,MAAM,IAAI,KAC5C,UAAU,IAAI,GAAG,KAAK,UAAU,MAAM,IAAI,KAC1C,SAAS;QACnB,iBAAiB;QACjB;IACF,OACK;QACH,IAAI,UAAU,sBAAsB,KAAK,SAAS,CAAC;YACjD,WAAW;YACX,QAAQ;YACR,UAAU;YACV,MAAM;QACR;QAEA,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,IAAI,OAAO,YAAY,eAAe,QAAQ,IAAI,EAAE;gBAClD,QAAQ,IAAI,CAAC;YACf;YACA,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAEF;;;CAGC,GACD,mBAAmB,SAAS,CAAC,kBAAkB,GAC7C,SAAS;IACP,IAAI,0BAA0B;IAC9B,IAAI,wBAAwB;IAC5B,IAAI,yBAAyB;IAC7B,IAAI,uBAAuB;IAC3B,IAAI,eAAe;IACnB,IAAI,iBAAiB;IACrB,IAAI,SAAS;IACb,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IAEJ,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,OAAO;IACrC,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAAK;QACnD,UAAU,QAAQ,CAAC,EAAE;QACrB,OAAO;QAEP,IAAI,QAAQ,aAAa,KAAK,uBAAuB;YACnD,0BAA0B;YAC1B,MAAO,QAAQ,aAAa,KAAK,sBAAuB;gBACtD,QAAQ;gBACR;YACF;QACF,OACK;YACH,IAAI,IAAI,GAAG;gBACT,IAAI,CAAC,KAAK,mCAAmC,CAAC,SAAS,QAAQ,CAAC,IAAI,EAAE,GAAG;oBACvE;gBACF;gBACA,QAAQ;YACV;QACF;QAEA,QAAQ,UAAU,MAAM,CAAC,QAAQ,eAAe,GACnB;QAC7B,0BAA0B,QAAQ,eAAe;QAEjD,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,YAAY,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,MAAM;YAChD,QAAQ,UAAU,MAAM,CAAC,YAAY;YACrC,iBAAiB;YAEjB,uDAAuD;YACvD,QAAQ,UAAU,MAAM,CAAC,QAAQ,YAAY,GAAG,IACnB;YAC7B,uBAAuB,QAAQ,YAAY,GAAG;YAE9C,QAAQ,UAAU,MAAM,CAAC,QAAQ,cAAc,GAClB;YAC7B,yBAAyB,QAAQ,cAAc;YAE/C,IAAI,QAAQ,IAAI,IAAI,MAAM;gBACxB,UAAU,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,IAAI;gBAC1C,QAAQ,UAAU,MAAM,CAAC,UAAU;gBACnC,eAAe;YACjB;QACF;QAEA,UAAU;IACZ;IAEA,OAAO;AACT;AAEF,mBAAmB,SAAS,CAAC,uBAAuB,GAClD,SAAS,0CAA0C,QAAQ,EAAE,WAAW;IACtE,OAAO,SAAS,GAAG,CAAC,SAAU,MAAM;QAClC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAC1B,OAAO;QACT;QACA,IAAI,eAAe,MAAM;YACvB,SAAS,KAAK,QAAQ,CAAC,aAAa;QACtC;QACA,IAAI,MAAM,KAAK,WAAW,CAAC;QAC3B,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAC/D,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAC1B;IACN,GAAG,IAAI;AACT;AAEF;;CAEC,GACD,mBAAmB,SAAS,CAAC,MAAM,GACjC,SAAS;IACP,IAAI,MAAM;QACR,SAAS,IAAI,CAAC,QAAQ;QACtB,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;QAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO;QAC1B,UAAU,IAAI,CAAC,kBAAkB;IACnC;IACA,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM;QACtB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK;IACvB;IACA,IAAI,IAAI,CAAC,WAAW,IAAI,MAAM;QAC5B,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW;IACnC;IACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;QACzB,IAAI,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,OAAO,EAAE,IAAI,UAAU;IAC/E;IAEA,OAAO;AACT;AAEF;;CAEC,GACD,mBAAmB,SAAS,CAAC,QAAQ,GACnC,SAAS;IACP,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM;AACnC;AAEF,QAAQ,kBAAkB,GAAG", "ignoreList": [0]}}, {"offset": {"line": 2929, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/binary-search.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nexports.GREATEST_LOWER_BOUND = 1;\nexports.LEAST_UPPER_BOUND = 2;\n\n/**\n * Recursive implementation of binary search.\n *\n * @param aLow Indices here and lower do not contain the needle.\n * @param aHigh Indices here and higher do not contain the needle.\n * @param aNeedle The element being searched for.\n * @param aHaystack The non-empty array being searched.\n * @param aCompare Function which takes two elements and returns -1, 0, or 1.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n */\nfunction recursiveSearch(aLow, aHigh, a<PERSON>eed<PERSON>, aHaystack, aCompare, a<PERSON><PERSON>) {\n  // This function terminates when one of the following is true:\n  //\n  //   1. We find the exact element we are looking for.\n  //\n  //   2. We did not find the exact element, but we can return the index of\n  //      the next-closest element.\n  //\n  //   3. We did not find the exact element, and there is no next-closest\n  //      element than the one we are searching for, so we return -1.\n  var mid = Math.floor((aHigh - aLow) / 2) + aLow;\n  var cmp = aCompare(aNeedle, aHaystack[mid], true);\n  if (cmp === 0) {\n    // Found the element we are looking for.\n    return mid;\n  }\n  else if (cmp > 0) {\n    // Our needle is greater than aHaystack[mid].\n    if (aHigh - mid > 1) {\n      // The element is in the upper half.\n      return recursiveSearch(mid, aHigh, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // The exact needle element was not found in this haystack. Determine if\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return aHigh < aHaystack.length ? aHigh : -1;\n    } else {\n      return mid;\n    }\n  }\n  else {\n    // Our needle is less than aHaystack[mid].\n    if (mid - aLow > 1) {\n      // The element is in the lower half.\n      return recursiveSearch(aLow, mid, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return mid;\n    } else {\n      return aLow < 0 ? -1 : aLow;\n    }\n  }\n}\n\n/**\n * This is an implementation of binary search which will always try and return\n * the index of the closest element if there is no exact hit. This is because\n * mappings between original and generated line/col pairs are single points,\n * and there is an implicit region between each of them, so a miss just means\n * that you aren't on the very start of a region.\n *\n * @param aNeedle The element you are looking for.\n * @param aHaystack The array that is being searched.\n * @param aCompare A function which takes the needle and an element in the\n *     array and returns -1, 0, or 1 depending on whether the needle is less\n *     than, equal to, or greater than the element, respectively.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'binarySearch.GREATEST_LOWER_BOUND'.\n */\nexports.search = function search(aNeedle, aHaystack, aCompare, aBias) {\n  if (aHaystack.length === 0) {\n    return -1;\n  }\n\n  var index = recursiveSearch(-1, aHaystack.length, aNeedle, aHaystack,\n                              aCompare, aBias || exports.GREATEST_LOWER_BOUND);\n  if (index < 0) {\n    return -1;\n  }\n\n  // We have found either the exact element, or the next-closest element than\n  // the one we are searching for. However, there may be more than one such\n  // element. Make sure we always return the smallest of these.\n  while (index - 1 >= 0) {\n    if (aCompare(aHaystack[index], aHaystack[index - 1], true) !== 0) {\n      break;\n    }\n    --index;\n  }\n\n  return index;\n};\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED,QAAQ,oBAAoB,GAAG;AAC/B,QAAQ,iBAAiB,GAAG;AAE5B;;;;;;;;;;;;CAYC,GACD,SAAS,gBAAgB,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK;IACvE,8DAA8D;IAC9D,EAAE;IACF,qDAAqD;IACrD,EAAE;IACF,yEAAyE;IACzE,iCAAiC;IACjC,EAAE;IACF,uEAAuE;IACvE,mEAAmE;IACnE,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC,QAAQ,IAAI,IAAI,KAAK;IAC3C,IAAI,MAAM,SAAS,SAAS,SAAS,CAAC,IAAI,EAAE;IAC5C,IAAI,QAAQ,GAAG;QACb,wCAAwC;QACxC,OAAO;IACT,OACK,IAAI,MAAM,GAAG;QAChB,6CAA6C;QAC7C,IAAI,QAAQ,MAAM,GAAG;YACnB,oCAAoC;YACpC,OAAO,gBAAgB,KAAK,OAAO,SAAS,WAAW,UAAU;QACnE;QAEA,wEAAwE;QACxE,0EAA0E;QAC1E,IAAI,SAAS,QAAQ,iBAAiB,EAAE;YACtC,OAAO,QAAQ,UAAU,MAAM,GAAG,QAAQ,CAAC;QAC7C,OAAO;YACL,OAAO;QACT;IACF,OACK;QACH,0CAA0C;QAC1C,IAAI,MAAM,OAAO,GAAG;YAClB,oCAAoC;YACpC,OAAO,gBAAgB,MAAM,KAAK,SAAS,WAAW,UAAU;QAClE;QAEA,0EAA0E;QAC1E,IAAI,SAAS,QAAQ,iBAAiB,EAAE;YACtC,OAAO;QACT,OAAO;YACL,OAAO,OAAO,IAAI,CAAC,IAAI;QACzB;IACF;AACF;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,QAAQ,MAAM,GAAG,SAAS,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK;IAClE,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI,QAAQ,gBAAgB,CAAC,GAAG,UAAU,MAAM,EAAE,SAAS,WAC/B,UAAU,SAAS,QAAQ,oBAAoB;IAC3E,IAAI,QAAQ,GAAG;QACb,OAAO,CAAC;IACV;IAEA,2EAA2E;IAC3E,yEAAyE;IACzE,6DAA6D;IAC7D,MAAO,QAAQ,KAAK,EAAG;QACrB,IAAI,SAAS,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,QAAQ,EAAE,EAAE,UAAU,GAAG;YAChE;QACF;QACA,EAAE;IACJ;IAEA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 3030, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/quick-sort.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n// It turns out that some (most?) JavaScript engines don't self-host\n// `Array.prototype.sort`. This makes sense because C++ will likely remain\n// faster than JS when doing raw CPU-intensive sorting. However, when using a\n// custom comparator function, calling back and forth between the VM's C++ and\n// JIT'd JS is rather slow *and* loses JIT type information, resulting in\n// worse generated code for the comparator function than would be optimal. In\n// fact, when sorting with a comparator, these costs outweigh the benefits of\n// sorting in C++. By using our own JS-implemented Quick Sort (below), we get\n// a ~3500ms mean speed-up in `bench/bench.html`.\n\nfunction SortTemplate(comparator) {\n\n/**\n * Swap the elements indexed by `x` and `y` in the array `ary`.\n *\n * @param {Array} ary\n *        The array.\n * @param {Number} x\n *        The index of the first item.\n * @param {Number} y\n *        The index of the second item.\n */\nfunction swap(ary, x, y) {\n  var temp = ary[x];\n  ary[x] = ary[y];\n  ary[y] = temp;\n}\n\n/**\n * Returns a random integer within the range `low .. high` inclusive.\n *\n * @param {Number} low\n *        The lower bound on the range.\n * @param {Number} high\n *        The upper bound on the range.\n */\nfunction randomIntInRange(low, high) {\n  return Math.round(low + (Math.random() * (high - low)));\n}\n\n/**\n * The Quick Sort algorithm.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n * @param {Number} p\n *        Start index of the array\n * @param {Number} r\n *        End index of the array\n */\nfunction doQuickSort(ary, comparator, p, r) {\n  // If our lower bound is less than our upper bound, we (1) partition the\n  // array into two pieces and (2) recurse on each half. If it is not, this is\n  // the empty array and our base case.\n\n  if (p < r) {\n    // (1) Partitioning.\n    //\n    // The partitioning chooses a pivot between `p` and `r` and moves all\n    // elements that are less than or equal to the pivot to the before it, and\n    // all the elements that are greater than it after it. The effect is that\n    // once partition is done, the pivot is in the exact place it will be when\n    // the array is put in sorted order, and it will not need to be moved\n    // again. This runs in O(n) time.\n\n    // Always choose a random pivot so that an input array which is reverse\n    // sorted does not cause O(n^2) running time.\n    var pivotIndex = randomIntInRange(p, r);\n    var i = p - 1;\n\n    swap(ary, pivotIndex, r);\n    var pivot = ary[r];\n\n    // Immediately after `j` is incremented in this loop, the following hold\n    // true:\n    //\n    //   * Every element in `ary[p .. i]` is less than or equal to the pivot.\n    //\n    //   * Every element in `ary[i+1 .. j-1]` is greater than the pivot.\n    for (var j = p; j < r; j++) {\n      if (comparator(ary[j], pivot, false) <= 0) {\n        i += 1;\n        swap(ary, i, j);\n      }\n    }\n\n    swap(ary, i + 1, j);\n    var q = i + 1;\n\n    // (2) Recurse on each half.\n\n    doQuickSort(ary, comparator, p, q - 1);\n    doQuickSort(ary, comparator, q + 1, r);\n  }\n}\n\n  return doQuickSort;\n}\n\nfunction cloneSort(comparator) {\n  let template = SortTemplate.toString();\n  let templateFn = new Function(`return ${template}`)();\n  return templateFn(comparator);\n}\n\n/**\n * Sort the given array in-place with the given comparator function.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n */\n\nlet sortCache = new WeakMap();\nexports.quickSort = function (ary, comparator, start = 0) {\n  let doQuickSort = sortCache.get(comparator);\n  if (doQuickSort === void 0) {\n    doQuickSort = cloneSort(comparator);\n    sortCache.set(comparator, doQuickSort);\n  }\n  doQuickSort(ary, comparator, start, ary.length - 1);\n};\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED,oEAAoE;AACpE,0EAA0E;AAC1E,6EAA6E;AAC7E,8EAA8E;AAC9E,yEAAyE;AACzE,6EAA6E;AAC7E,6EAA6E;AAC7E,6EAA6E;AAC7E,iDAAiD;AAEjD,SAAS,aAAa,UAAU;IAEhC;;;;;;;;;CASC,GACD,SAAS,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC;QACrB,IAAI,OAAO,GAAG,CAAC,EAAE;QACjB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QACf,GAAG,CAAC,EAAE,GAAG;IACX;IAEA;;;;;;;CAOC,GACD,SAAS,iBAAiB,GAAG,EAAE,IAAI;QACjC,OAAO,KAAK,KAAK,CAAC,MAAO,KAAK,MAAM,KAAK,CAAC,OAAO,GAAG;IACtD;IAEA;;;;;;;;;;;CAWC,GACD,SAAS,YAAY,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;QACxC,wEAAwE;QACxE,4EAA4E;QAC5E,qCAAqC;QAErC,IAAI,IAAI,GAAG;YACT,oBAAoB;YACpB,EAAE;YACF,qEAAqE;YACrE,0EAA0E;YAC1E,yEAAyE;YACzE,0EAA0E;YAC1E,qEAAqE;YACrE,iCAAiC;YAEjC,uEAAuE;YACvE,6CAA6C;YAC7C,IAAI,aAAa,iBAAiB,GAAG;YACrC,IAAI,IAAI,IAAI;YAEZ,KAAK,KAAK,YAAY;YACtB,IAAI,QAAQ,GAAG,CAAC,EAAE;YAElB,wEAAwE;YACxE,QAAQ;YACR,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,oEAAoE;YACpE,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAI,WAAW,GAAG,CAAC,EAAE,EAAE,OAAO,UAAU,GAAG;oBACzC,KAAK;oBACL,KAAK,KAAK,GAAG;gBACf;YACF;YAEA,KAAK,KAAK,IAAI,GAAG;YACjB,IAAI,IAAI,IAAI;YAEZ,4BAA4B;YAE5B,YAAY,KAAK,YAAY,GAAG,IAAI;YACpC,YAAY,KAAK,YAAY,IAAI,GAAG;QACtC;IACF;IAEE,OAAO;AACT;AAEA,SAAS,UAAU,UAAU;IAC3B,IAAI,WAAW,aAAa,QAAQ;IACpC,IAAI,aAAa,IAAI,SAAS,CAAC,OAAO,EAAE,UAAU;IAClD,OAAO,WAAW;AACpB;AAEA;;;;;;;CAOC,GAED,IAAI,YAAY,IAAI;AACpB,QAAQ,SAAS,GAAG,SAAU,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC;IACtD,IAAI,cAAc,UAAU,GAAG,CAAC;IAChC,IAAI,gBAAgB,KAAK,GAAG;QAC1B,cAAc,UAAU;QACxB,UAAU,GAAG,CAAC,YAAY;IAC5B;IACA,YAAY,KAAK,YAAY,OAAO,IAAI,MAAM,GAAG;AACnD", "ignoreList": [0]}}, {"offset": {"line": 3145, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/source-map-consumer.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar binarySearch = require('./binary-search');\nvar ArraySet = require('./array-set').ArraySet;\nvar base64VLQ = require('./base64-vlq');\nvar quickSort = require('./quick-sort').quickSort;\n\nfunction SourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  return sourceMap.sections != null\n    ? new IndexedSourceMapConsumer(sourceMap, aSourceMapURL)\n    : new BasicSourceMapConsumer(sourceMap, aSourceMapURL);\n}\n\nSourceMapConsumer.fromSourceMap = function(aSourceMap, aSourceMapURL) {\n  return BasicSourceMapConsumer.fromSourceMap(aSourceMap, aSourceMapURL);\n}\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nSourceMapConsumer.prototype._version = 3;\n\n// `__generatedMappings` and `__originalMappings` are arrays that hold the\n// parsed mapping coordinates from the source map's \"mappings\" attribute. They\n// are lazily instantiated, accessed via the `_generatedMappings` and\n// `_originalMappings` getters respectively, and we only parse the mappings\n// and create these arrays once queried for a source location. We jump through\n// these hoops because there can be many thousands of mappings, and parsing\n// them is expensive, so we only want to do it if we must.\n//\n// Each object in the arrays is of the form:\n//\n//     {\n//       generatedLine: The line number in the generated code,\n//       generatedColumn: The column number in the generated code,\n//       source: The path to the original source file that generated this\n//               chunk of code,\n//       originalLine: The line number in the original source that\n//                     corresponds to this chunk of generated code,\n//       originalColumn: The column number in the original source that\n//                       corresponds to this chunk of generated code,\n//       name: The name of the original symbol which generated this chunk of\n//             code.\n//     }\n//\n// All properties except for `generatedLine` and `generatedColumn` can be\n// `null`.\n//\n// `_generatedMappings` is ordered by the generated positions.\n//\n// `_originalMappings` is ordered by the original positions.\n\nSourceMapConsumer.prototype.__generatedMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_generatedMappings', {\n  configurable: true,\n  enumerable: true,\n  get: function () {\n    if (!this.__generatedMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__generatedMappings;\n  }\n});\n\nSourceMapConsumer.prototype.__originalMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_originalMappings', {\n  configurable: true,\n  enumerable: true,\n  get: function () {\n    if (!this.__originalMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__originalMappings;\n  }\n});\n\nSourceMapConsumer.prototype._charIsMappingSeparator =\n  function SourceMapConsumer_charIsMappingSeparator(aStr, index) {\n    var c = aStr.charAt(index);\n    return c === \";\" || c === \",\";\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    throw new Error(\"Subclasses must implement _parseMappings\");\n  };\n\nSourceMapConsumer.GENERATED_ORDER = 1;\nSourceMapConsumer.ORIGINAL_ORDER = 2;\n\nSourceMapConsumer.GREATEST_LOWER_BOUND = 1;\nSourceMapConsumer.LEAST_UPPER_BOUND = 2;\n\n/**\n * Iterate over each mapping between an original source/line/column and a\n * generated line/column in this source map.\n *\n * @param Function aCallback\n *        The function that is called with each mapping.\n * @param Object aContext\n *        Optional. If specified, this object will be the value of `this` every\n *        time that `aCallback` is called.\n * @param aOrder\n *        Either `SourceMapConsumer.GENERATED_ORDER` or\n *        `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to\n *        iterate over the mappings sorted by the generated file's line/column\n *        order or the original's source/line/column order, respectively. Defaults to\n *        `SourceMapConsumer.GENERATED_ORDER`.\n */\nSourceMapConsumer.prototype.eachMapping =\n  function SourceMapConsumer_eachMapping(aCallback, aContext, aOrder) {\n    var context = aContext || null;\n    var order = aOrder || SourceMapConsumer.GENERATED_ORDER;\n\n    var mappings;\n    switch (order) {\n    case SourceMapConsumer.GENERATED_ORDER:\n      mappings = this._generatedMappings;\n      break;\n    case SourceMapConsumer.ORIGINAL_ORDER:\n      mappings = this._originalMappings;\n      break;\n    default:\n      throw new Error(\"Unknown order of iteration.\");\n    }\n\n    var sourceRoot = this.sourceRoot;\n    var boundCallback = aCallback.bind(context);\n    var names = this._names;\n    var sources = this._sources;\n    var sourceMapURL = this._sourceMapURL;\n\n    for (var i = 0, n = mappings.length; i < n; i++) {\n      var mapping = mappings[i];\n      var source = mapping.source === null ? null : sources.at(mapping.source);\n      if(source !== null) {\n        source = util.computeSourceURL(sourceRoot, source, sourceMapURL);\n      }\n      boundCallback({\n        source: source,\n        generatedLine: mapping.generatedLine,\n        generatedColumn: mapping.generatedColumn,\n        originalLine: mapping.originalLine,\n        originalColumn: mapping.originalColumn,\n        name: mapping.name === null ? null : names.at(mapping.name)\n      });\n    }\n  };\n\n/**\n * Returns all generated line and column information for the original source,\n * line, and column provided. If no column is provided, returns all mappings\n * corresponding to a either the line we are searching for or the next\n * closest line that has any mappings. Otherwise, returns all mappings\n * corresponding to the given line and either the column we are searching for\n * or the next closest column that has any offsets.\n *\n * The only argument is an object with the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number is 1-based.\n *   - column: Optional. the column number in the original source.\n *    The column number is 0-based.\n *\n * and an array of objects is returned, each with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *    line number is 1-based.\n *   - column: The column number in the generated source, or null.\n *    The column number is 0-based.\n */\nSourceMapConsumer.prototype.allGeneratedPositionsFor =\n  function SourceMapConsumer_allGeneratedPositionsFor(aArgs) {\n    var line = util.getArg(aArgs, 'line');\n\n    // When there is no exact match, BasicSourceMapConsumer.prototype._findMapping\n    // returns the index of the closest mapping less than the needle. By\n    // setting needle.originalColumn to 0, we thus find the last mapping for\n    // the given line, provided such a mapping exists.\n    var needle = {\n      source: util.getArg(aArgs, 'source'),\n      originalLine: line,\n      originalColumn: util.getArg(aArgs, 'column', 0)\n    };\n\n    needle.source = this._findSourceIndex(needle.source);\n    if (needle.source < 0) {\n      return [];\n    }\n\n    var mappings = [];\n\n    var index = this._findMapping(needle,\n                                  this._originalMappings,\n                                  \"originalLine\",\n                                  \"originalColumn\",\n                                  util.compareByOriginalPositions,\n                                  binarySearch.LEAST_UPPER_BOUND);\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (aArgs.column === undefined) {\n        var originalLine = mapping.originalLine;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we found. Since\n        // mappings are sorted, this is guaranteed to find all mappings for\n        // the line we found.\n        while (mapping && mapping.originalLine === originalLine) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      } else {\n        var originalColumn = mapping.originalColumn;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we were searching for.\n        // Since mappings are sorted, this is guaranteed to find all mappings for\n        // the line we are searching for.\n        while (mapping &&\n               mapping.originalLine === line &&\n               mapping.originalColumn == originalColumn) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      }\n    }\n\n    return mappings;\n  };\n\nexports.SourceMapConsumer = SourceMapConsumer;\n\n/**\n * A BasicSourceMapConsumer instance represents a parsed source map which we can\n * query for information about the original file positions by giving it a file\n * position in the generated source.\n *\n * The first parameter is the raw source map (either as a JSON string, or\n * already parsed to an object). According to the spec, source maps have the\n * following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - sources: An array of URLs to the original source files.\n *   - names: An array of identifiers which can be referrenced by individual mappings.\n *   - sourceRoot: Optional. The URL root from which all sources are relative.\n *   - sourcesContent: Optional. An array of contents of the original source files.\n *   - mappings: A string of base64 VLQs which contain the actual mappings.\n *   - file: Optional. The generated file this source map is associated with.\n *\n * Here is an example source map, taken from the source map spec[0]:\n *\n *     {\n *       version : 3,\n *       file: \"out.js\",\n *       sourceRoot : \"\",\n *       sources: [\"foo.js\", \"bar.js\"],\n *       names: [\"src\", \"maps\", \"are\", \"fun\"],\n *       mappings: \"AA,AB;;ABCDE;\"\n *     }\n *\n * The second parameter, if given, is a string whose value is the URL\n * at which the source map was found.  This URL is used to compute the\n * sources array.\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit?pli=1#\n */\nfunction BasicSourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sources = util.getArg(sourceMap, 'sources');\n  // Sass 3.3 leaves out the 'names' array, so we deviate from the spec (which\n  // requires the array) to play nice here.\n  var names = util.getArg(sourceMap, 'names', []);\n  var sourceRoot = util.getArg(sourceMap, 'sourceRoot', null);\n  var sourcesContent = util.getArg(sourceMap, 'sourcesContent', null);\n  var mappings = util.getArg(sourceMap, 'mappings');\n  var file = util.getArg(sourceMap, 'file', null);\n\n  // Once again, Sass deviates from the spec and supplies the version as a\n  // string rather than a number, so we use loose equality checking here.\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  if (sourceRoot) {\n    sourceRoot = util.normalize(sourceRoot);\n  }\n\n  sources = sources\n    .map(String)\n    // Some source maps produce relative source paths like \"./foo.js\" instead of\n    // \"foo.js\".  Normalize these first so that future comparisons will succeed.\n    // See bugzil.la/1090768.\n    .map(util.normalize)\n    // Always ensure that absolute sources are internally stored relative to\n    // the source root, if the source root is absolute. Not doing this would\n    // be particularly problematic when the source root is a prefix of the\n    // source (valid, but why??). See github issue #199 and bugzil.la/1188982.\n    .map(function (source) {\n      return sourceRoot && util.isAbsolute(sourceRoot) && util.isAbsolute(source)\n        ? util.relative(sourceRoot, source)\n        : source;\n    });\n\n  // Pass `true` below to allow duplicate names and sources. While source maps\n  // are intended to be compressed and deduplicated, the TypeScript compiler\n  // sometimes generates source maps with duplicates in them. See Github issue\n  // #72 and bugzil.la/889492.\n  this._names = ArraySet.fromArray(names.map(String), true);\n  this._sources = ArraySet.fromArray(sources, true);\n\n  this._absoluteSources = this._sources.toArray().map(function (s) {\n    return util.computeSourceURL(sourceRoot, s, aSourceMapURL);\n  });\n\n  this.sourceRoot = sourceRoot;\n  this.sourcesContent = sourcesContent;\n  this._mappings = mappings;\n  this._sourceMapURL = aSourceMapURL;\n  this.file = file;\n}\n\nBasicSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nBasicSourceMapConsumer.prototype.consumer = SourceMapConsumer;\n\n/**\n * Utility function to find the index of a source.  Returns -1 if not\n * found.\n */\nBasicSourceMapConsumer.prototype._findSourceIndex = function(aSource) {\n  var relativeSource = aSource;\n  if (this.sourceRoot != null) {\n    relativeSource = util.relative(this.sourceRoot, relativeSource);\n  }\n\n  if (this._sources.has(relativeSource)) {\n    return this._sources.indexOf(relativeSource);\n  }\n\n  // Maybe aSource is an absolute URL as returned by |sources|.  In\n  // this case we can't simply undo the transform.\n  var i;\n  for (i = 0; i < this._absoluteSources.length; ++i) {\n    if (this._absoluteSources[i] == aSource) {\n      return i;\n    }\n  }\n\n  return -1;\n};\n\n/**\n * Create a BasicSourceMapConsumer from a SourceMapGenerator.\n *\n * @param SourceMapGenerator aSourceMap\n *        The source map that will be consumed.\n * @param String aSourceMapURL\n *        The URL at which the source map can be found (optional)\n * @returns BasicSourceMapConsumer\n */\nBasicSourceMapConsumer.fromSourceMap =\n  function SourceMapConsumer_fromSourceMap(aSourceMap, aSourceMapURL) {\n    var smc = Object.create(BasicSourceMapConsumer.prototype);\n\n    var names = smc._names = ArraySet.fromArray(aSourceMap._names.toArray(), true);\n    var sources = smc._sources = ArraySet.fromArray(aSourceMap._sources.toArray(), true);\n    smc.sourceRoot = aSourceMap._sourceRoot;\n    smc.sourcesContent = aSourceMap._generateSourcesContent(smc._sources.toArray(),\n                                                            smc.sourceRoot);\n    smc.file = aSourceMap._file;\n    smc._sourceMapURL = aSourceMapURL;\n    smc._absoluteSources = smc._sources.toArray().map(function (s) {\n      return util.computeSourceURL(smc.sourceRoot, s, aSourceMapURL);\n    });\n\n    // Because we are modifying the entries (by converting string sources and\n    // names to indices into the sources and names ArraySets), we have to make\n    // a copy of the entry or else bad things happen. Shared mutable state\n    // strikes again! See github issue #191.\n\n    var generatedMappings = aSourceMap._mappings.toArray().slice();\n    var destGeneratedMappings = smc.__generatedMappings = [];\n    var destOriginalMappings = smc.__originalMappings = [];\n\n    for (var i = 0, length = generatedMappings.length; i < length; i++) {\n      var srcMapping = generatedMappings[i];\n      var destMapping = new Mapping;\n      destMapping.generatedLine = srcMapping.generatedLine;\n      destMapping.generatedColumn = srcMapping.generatedColumn;\n\n      if (srcMapping.source) {\n        destMapping.source = sources.indexOf(srcMapping.source);\n        destMapping.originalLine = srcMapping.originalLine;\n        destMapping.originalColumn = srcMapping.originalColumn;\n\n        if (srcMapping.name) {\n          destMapping.name = names.indexOf(srcMapping.name);\n        }\n\n        destOriginalMappings.push(destMapping);\n      }\n\n      destGeneratedMappings.push(destMapping);\n    }\n\n    quickSort(smc.__originalMappings, util.compareByOriginalPositions);\n\n    return smc;\n  };\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nBasicSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(BasicSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    return this._absoluteSources.slice();\n  }\n});\n\n/**\n * Provide the JIT with a nice shape / hidden class.\n */\nfunction Mapping() {\n  this.generatedLine = 0;\n  this.generatedColumn = 0;\n  this.source = null;\n  this.originalLine = null;\n  this.originalColumn = null;\n  this.name = null;\n}\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\n\nconst compareGenerated = util.compareByGeneratedPositionsDeflatedNoLine;\nfunction sortGenerated(array, start) {\n  let l = array.length;\n  let n = array.length - start;\n  if (n <= 1) {\n    return;\n  } else if (n == 2) {\n    let a = array[start];\n    let b = array[start + 1];\n    if (compareGenerated(a, b) > 0) {\n      array[start] = b;\n      array[start + 1] = a;\n    }\n  } else if (n < 20) {\n    for (let i = start; i < l; i++) {\n      for (let j = i; j > start; j--) {\n        let a = array[j - 1];\n        let b = array[j];\n        if (compareGenerated(a, b) <= 0) {\n          break;\n        }\n        array[j - 1] = b;\n        array[j] = a;\n      }\n    }\n  } else {\n    quickSort(array, compareGenerated, start);\n  }\n}\nBasicSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    var generatedLine = 1;\n    var previousGeneratedColumn = 0;\n    var previousOriginalLine = 0;\n    var previousOriginalColumn = 0;\n    var previousSource = 0;\n    var previousName = 0;\n    var length = aStr.length;\n    var index = 0;\n    var cachedSegments = {};\n    var temp = {};\n    var originalMappings = [];\n    var generatedMappings = [];\n    var mapping, str, segment, end, value;\n\n    let subarrayStart = 0;\n    while (index < length) {\n      if (aStr.charAt(index) === ';') {\n        generatedLine++;\n        index++;\n        previousGeneratedColumn = 0;\n\n        sortGenerated(generatedMappings, subarrayStart);\n        subarrayStart = generatedMappings.length;\n      }\n      else if (aStr.charAt(index) === ',') {\n        index++;\n      }\n      else {\n        mapping = new Mapping();\n        mapping.generatedLine = generatedLine;\n\n        for (end = index; end < length; end++) {\n          if (this._charIsMappingSeparator(aStr, end)) {\n            break;\n          }\n        }\n        str = aStr.slice(index, end);\n\n        segment = [];\n        while (index < end) {\n          base64VLQ.decode(aStr, index, temp);\n          value = temp.value;\n          index = temp.rest;\n          segment.push(value);\n        }\n\n        if (segment.length === 2) {\n          throw new Error('Found a source, but no line and column');\n        }\n\n        if (segment.length === 3) {\n          throw new Error('Found a source and line, but no column');\n        }\n\n        // Generated column.\n        mapping.generatedColumn = previousGeneratedColumn + segment[0];\n        previousGeneratedColumn = mapping.generatedColumn;\n\n        if (segment.length > 1) {\n          // Original source.\n          mapping.source = previousSource + segment[1];\n          previousSource += segment[1];\n\n          // Original line.\n          mapping.originalLine = previousOriginalLine + segment[2];\n          previousOriginalLine = mapping.originalLine;\n          // Lines are stored 0-based\n          mapping.originalLine += 1;\n\n          // Original column.\n          mapping.originalColumn = previousOriginalColumn + segment[3];\n          previousOriginalColumn = mapping.originalColumn;\n\n          if (segment.length > 4) {\n            // Original name.\n            mapping.name = previousName + segment[4];\n            previousName += segment[4];\n          }\n        }\n\n        generatedMappings.push(mapping);\n        if (typeof mapping.originalLine === 'number') {\n          let currentSource = mapping.source;\n          while (originalMappings.length <= currentSource) {\n            originalMappings.push(null);\n          }\n          if (originalMappings[currentSource] === null) {\n            originalMappings[currentSource] = [];\n          }\n          originalMappings[currentSource].push(mapping);\n        }\n      }\n    }\n\n    sortGenerated(generatedMappings, subarrayStart);\n    this.__generatedMappings = generatedMappings;\n\n    for (var i = 0; i < originalMappings.length; i++) {\n      if (originalMappings[i] != null) {\n        quickSort(originalMappings[i], util.compareByOriginalPositionsNoSource);\n      }\n    }\n    this.__originalMappings = [].concat(...originalMappings);\n  };\n\n/**\n * Find the mapping that best matches the hypothetical \"needle\" mapping that\n * we are searching for in the given \"haystack\" of mappings.\n */\nBasicSourceMapConsumer.prototype._findMapping =\n  function SourceMapConsumer_findMapping(aNeedle, aMappings, aLineName,\n                                         aColumnName, aComparator, aBias) {\n    // To return the position we are searching for, we must first find the\n    // mapping for the given position and then return the opposite position it\n    // points to. Because the mappings are sorted, we can use binary search to\n    // find the best mapping.\n\n    if (aNeedle[aLineName] <= 0) {\n      throw new TypeError('Line must be greater than or equal to 1, got '\n                          + aNeedle[aLineName]);\n    }\n    if (aNeedle[aColumnName] < 0) {\n      throw new TypeError('Column must be greater than or equal to 0, got '\n                          + aNeedle[aColumnName]);\n    }\n\n    return binarySearch.search(aNeedle, aMappings, aComparator, aBias);\n  };\n\n/**\n * Compute the last column for each generated mapping. The last column is\n * inclusive.\n */\nBasicSourceMapConsumer.prototype.computeColumnSpans =\n  function SourceMapConsumer_computeColumnSpans() {\n    for (var index = 0; index < this._generatedMappings.length; ++index) {\n      var mapping = this._generatedMappings[index];\n\n      // Mappings do not contain a field for the last generated columnt. We\n      // can come up with an optimistic estimate, however, by assuming that\n      // mappings are contiguous (i.e. given two consecutive mappings, the\n      // first mapping ends where the second one starts).\n      if (index + 1 < this._generatedMappings.length) {\n        var nextMapping = this._generatedMappings[index + 1];\n\n        if (mapping.generatedLine === nextMapping.generatedLine) {\n          mapping.lastGeneratedColumn = nextMapping.generatedColumn - 1;\n          continue;\n        }\n      }\n\n      // The last mapping for each line spans the entire line.\n      mapping.lastGeneratedColumn = Infinity;\n    }\n  };\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.  The line number\n *     is 1-based.\n *   - column: The column number in the generated source.  The column\n *     number is 0-based.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the original source, or null.  The\n *     column number is 0-based.\n *   - name: The original identifier, or null.\n */\nBasicSourceMapConsumer.prototype.originalPositionFor =\n  function SourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._generatedMappings,\n      \"generatedLine\",\n      \"generatedColumn\",\n      util.compareByGeneratedPositionsDeflated,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._generatedMappings[index];\n\n      if (mapping.generatedLine === needle.generatedLine) {\n        var source = util.getArg(mapping, 'source', null);\n        if (source !== null) {\n          source = this._sources.at(source);\n          source = util.computeSourceURL(this.sourceRoot, source, this._sourceMapURL);\n        }\n        var name = util.getArg(mapping, 'name', null);\n        if (name !== null) {\n          name = this._names.at(name);\n        }\n        return {\n          source: source,\n          line: util.getArg(mapping, 'originalLine', null),\n          column: util.getArg(mapping, 'originalColumn', null),\n          name: name\n        };\n      }\n    }\n\n    return {\n      source: null,\n      line: null,\n      column: null,\n      name: null\n    };\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nBasicSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function BasicSourceMapConsumer_hasContentsOfAllSources() {\n    if (!this.sourcesContent) {\n      return false;\n    }\n    return this.sourcesContent.length >= this._sources.size() &&\n      !this.sourcesContent.some(function (sc) { return sc == null; });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nBasicSourceMapConsumer.prototype.sourceContentFor =\n  function SourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    if (!this.sourcesContent) {\n      return null;\n    }\n\n    var index = this._findSourceIndex(aSource);\n    if (index >= 0) {\n      return this.sourcesContent[index];\n    }\n\n    var relativeSource = aSource;\n    if (this.sourceRoot != null) {\n      relativeSource = util.relative(this.sourceRoot, relativeSource);\n    }\n\n    var url;\n    if (this.sourceRoot != null\n        && (url = util.urlParse(this.sourceRoot))) {\n      // XXX: file:// URIs and absolute paths lead to unexpected behavior for\n      // many users. We can help them out when they expect file:// URIs to\n      // behave like it would if they were running a local HTTP server. See\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=885597.\n      var fileUriAbsPath = relativeSource.replace(/^file:\\/\\//, \"\");\n      if (url.scheme == \"file\"\n          && this._sources.has(fileUriAbsPath)) {\n        return this.sourcesContent[this._sources.indexOf(fileUriAbsPath)]\n      }\n\n      if ((!url.path || url.path == \"/\")\n          && this._sources.has(\"/\" + relativeSource)) {\n        return this.sourcesContent[this._sources.indexOf(\"/\" + relativeSource)];\n      }\n    }\n\n    // This function is used recursively from\n    // IndexedSourceMapConsumer.prototype.sourceContentFor. In that case, we\n    // don't want to throw if we can't find the source - we just want to\n    // return null, so we provide a flag to exit gracefully.\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + relativeSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number\n *     is 1-based.\n *   - column: The column number in the original source.  The column\n *     number is 0-based.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the generated source, or null.\n *     The column number is 0-based.\n */\nBasicSourceMapConsumer.prototype.generatedPositionFor =\n  function SourceMapConsumer_generatedPositionFor(aArgs) {\n    var source = util.getArg(aArgs, 'source');\n    source = this._findSourceIndex(source);\n    if (source < 0) {\n      return {\n        line: null,\n        column: null,\n        lastColumn: null\n      };\n    }\n\n    var needle = {\n      source: source,\n      originalLine: util.getArg(aArgs, 'line'),\n      originalColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._originalMappings,\n      \"originalLine\",\n      \"originalColumn\",\n      util.compareByOriginalPositions,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (mapping.source === needle.source) {\n        return {\n          line: util.getArg(mapping, 'generatedLine', null),\n          column: util.getArg(mapping, 'generatedColumn', null),\n          lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n        };\n      }\n    }\n\n    return {\n      line: null,\n      column: null,\n      lastColumn: null\n    };\n  };\n\nexports.BasicSourceMapConsumer = BasicSourceMapConsumer;\n\n/**\n * An IndexedSourceMapConsumer instance represents a parsed source map which\n * we can query for information. It differs from BasicSourceMapConsumer in\n * that it takes \"indexed\" source maps (i.e. ones with a \"sections\" field) as\n * input.\n *\n * The first parameter is a raw source map (either as a JSON string, or already\n * parsed to an object). According to the spec for indexed source maps, they\n * have the following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - file: Optional. The generated file this source map is associated with.\n *   - sections: A list of section definitions.\n *\n * Each value under the \"sections\" field has two fields:\n *   - offset: The offset into the original specified at which this section\n *       begins to apply, defined as an object with a \"line\" and \"column\"\n *       field.\n *   - map: A source map definition. This source map could also be indexed,\n *       but doesn't have to be.\n *\n * Instead of the \"map\" field, it's also possible to have a \"url\" field\n * specifying a URL to retrieve a source map from, but that's currently\n * unsupported.\n *\n * Here's an example source map, taken from the source map spec[0], but\n * modified to omit a section which uses the \"url\" field.\n *\n *  {\n *    version : 3,\n *    file: \"app.js\",\n *    sections: [{\n *      offset: {line:100, column:10},\n *      map: {\n *        version : 3,\n *        file: \"section.js\",\n *        sources: [\"foo.js\", \"bar.js\"],\n *        names: [\"src\", \"maps\", \"are\", \"fun\"],\n *        mappings: \"AAAA,E;;ABCDE;\"\n *      }\n *    }],\n *  }\n *\n * The second parameter, if given, is a string whose value is the URL\n * at which the source map was found.  This URL is used to compute the\n * sources array.\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit#heading=h.535es3xeprgt\n */\nfunction IndexedSourceMapConsumer(aSourceMap, aSourceMapURL) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = util.parseSourceMapInput(aSourceMap);\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sections = util.getArg(sourceMap, 'sections');\n\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n\n  var lastOffset = {\n    line: -1,\n    column: 0\n  };\n  this._sections = sections.map(function (s) {\n    if (s.url) {\n      // The url field will require support for asynchronicity.\n      // See https://github.com/mozilla/source-map/issues/16\n      throw new Error('Support for url field in sections not implemented.');\n    }\n    var offset = util.getArg(s, 'offset');\n    var offsetLine = util.getArg(offset, 'line');\n    var offsetColumn = util.getArg(offset, 'column');\n\n    if (offsetLine < lastOffset.line ||\n        (offsetLine === lastOffset.line && offsetColumn < lastOffset.column)) {\n      throw new Error('Section offsets must be ordered and non-overlapping.');\n    }\n    lastOffset = offset;\n\n    return {\n      generatedOffset: {\n        // The offset fields are 0-based, but we use 1-based indices when\n        // encoding/decoding from VLQ.\n        generatedLine: offsetLine + 1,\n        generatedColumn: offsetColumn + 1\n      },\n      consumer: new SourceMapConsumer(util.getArg(s, 'map'), aSourceMapURL)\n    }\n  });\n}\n\nIndexedSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nIndexedSourceMapConsumer.prototype.constructor = SourceMapConsumer;\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nIndexedSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(IndexedSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    var sources = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      for (var j = 0; j < this._sections[i].consumer.sources.length; j++) {\n        sources.push(this._sections[i].consumer.sources[j]);\n      }\n    }\n    return sources;\n  }\n});\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.  The line number\n *     is 1-based.\n *   - column: The column number in the generated source.  The column\n *     number is 0-based.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.  The\n *     line number is 1-based.\n *   - column: The column number in the original source, or null.  The\n *     column number is 0-based.\n *   - name: The original identifier, or null.\n */\nIndexedSourceMapConsumer.prototype.originalPositionFor =\n  function IndexedSourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    // Find the section containing the generated position we're trying to map\n    // to an original position.\n    var sectionIndex = binarySearch.search(needle, this._sections,\n      function(needle, section) {\n        var cmp = needle.generatedLine - section.generatedOffset.generatedLine;\n        if (cmp) {\n          return cmp;\n        }\n\n        return (needle.generatedColumn -\n                section.generatedOffset.generatedColumn);\n      });\n    var section = this._sections[sectionIndex];\n\n    if (!section) {\n      return {\n        source: null,\n        line: null,\n        column: null,\n        name: null\n      };\n    }\n\n    return section.consumer.originalPositionFor({\n      line: needle.generatedLine -\n        (section.generatedOffset.generatedLine - 1),\n      column: needle.generatedColumn -\n        (section.generatedOffset.generatedLine === needle.generatedLine\n         ? section.generatedOffset.generatedColumn - 1\n         : 0),\n      bias: aArgs.bias\n    });\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nIndexedSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function IndexedSourceMapConsumer_hasContentsOfAllSources() {\n    return this._sections.every(function (s) {\n      return s.consumer.hasContentsOfAllSources();\n    });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nIndexedSourceMapConsumer.prototype.sourceContentFor =\n  function IndexedSourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      var content = section.consumer.sourceContentFor(aSource, true);\n      if (content || content === '') {\n        return content;\n      }\n    }\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.  The line number\n *     is 1-based.\n *   - column: The column number in the original source.  The column\n *     number is 0-based.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.  The\n *     line number is 1-based. \n *   - column: The column number in the generated source, or null.\n *     The column number is 0-based.\n */\nIndexedSourceMapConsumer.prototype.generatedPositionFor =\n  function IndexedSourceMapConsumer_generatedPositionFor(aArgs) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      // Only consider this section if the requested source is in the list of\n      // sources of the consumer.\n      if (section.consumer._findSourceIndex(util.getArg(aArgs, 'source')) === -1) {\n        continue;\n      }\n      var generatedPosition = section.consumer.generatedPositionFor(aArgs);\n      if (generatedPosition) {\n        var ret = {\n          line: generatedPosition.line +\n            (section.generatedOffset.generatedLine - 1),\n          column: generatedPosition.column +\n            (section.generatedOffset.generatedLine === generatedPosition.line\n             ? section.generatedOffset.generatedColumn - 1\n             : 0)\n        };\n        return ret;\n      }\n    }\n\n    return {\n      line: null,\n      column: null\n    };\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nIndexedSourceMapConsumer.prototype._parseMappings =\n  function IndexedSourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    this.__generatedMappings = [];\n    this.__originalMappings = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n      var sectionMappings = section.consumer._generatedMappings;\n      for (var j = 0; j < sectionMappings.length; j++) {\n        var mapping = sectionMappings[j];\n\n        var source = section.consumer._sources.at(mapping.source);\n        if(source !== null) {\n          source = util.computeSourceURL(section.consumer.sourceRoot, source, this._sourceMapURL);\n        }\n        this._sources.add(source);\n        source = this._sources.indexOf(source);\n\n        var name = null;\n        if (mapping.name) {\n          name = section.consumer._names.at(mapping.name);\n          this._names.add(name);\n          name = this._names.indexOf(name);\n        }\n\n        // The mappings coming from the consumer for the section have\n        // generated positions relative to the start of the section, so we\n        // need to offset them to be relative to the start of the concatenated\n        // generated file.\n        var adjustedMapping = {\n          source: source,\n          generatedLine: mapping.generatedLine +\n            (section.generatedOffset.generatedLine - 1),\n          generatedColumn: mapping.generatedColumn +\n            (section.generatedOffset.generatedLine === mapping.generatedLine\n            ? section.generatedOffset.generatedColumn - 1\n            : 0),\n          originalLine: mapping.originalLine,\n          originalColumn: mapping.originalColumn,\n          name: name\n        };\n\n        this.__generatedMappings.push(adjustedMapping);\n        if (typeof adjustedMapping.originalLine === 'number') {\n          this.__originalMappings.push(adjustedMapping);\n        }\n      }\n    }\n\n    quickSort(this.__generatedMappings, util.compareByGeneratedPositionsDeflated);\n    quickSort(this.__originalMappings, util.compareByOriginalPositions);\n  };\n\nexports.IndexedSourceMapConsumer = IndexedSourceMapConsumer;\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED,IAAI;AACJ,IAAI;AACJ,IAAI,WAAW,wGAAuB,QAAQ;AAC9C,IAAI;AACJ,IAAI,YAAY,yGAAwB,SAAS;AAEjD,SAAS,kBAAkB,UAAU,EAAE,aAAa;IAClD,IAAI,YAAY;IAChB,IAAI,OAAO,eAAe,UAAU;QAClC,YAAY,KAAK,mBAAmB,CAAC;IACvC;IAEA,OAAO,UAAU,QAAQ,IAAI,OACzB,IAAI,yBAAyB,WAAW,iBACxC,IAAI,uBAAuB,WAAW;AAC5C;AAEA,kBAAkB,aAAa,GAAG,SAAS,UAAU,EAAE,aAAa;IAClE,OAAO,uBAAuB,aAAa,CAAC,YAAY;AAC1D;AAEA;;CAEC,GACD,kBAAkB,SAAS,CAAC,QAAQ,GAAG;AAEvC,0EAA0E;AAC1E,8EAA8E;AAC9E,qEAAqE;AACrE,2EAA2E;AAC3E,8EAA8E;AAC9E,2EAA2E;AAC3E,0DAA0D;AAC1D,EAAE;AACF,4CAA4C;AAC5C,EAAE;AACF,QAAQ;AACR,8DAA8D;AAC9D,kEAAkE;AAClE,yEAAyE;AACzE,+BAA+B;AAC/B,kEAAkE;AAClE,mEAAmE;AACnE,sEAAsE;AACtE,qEAAqE;AACrE,4EAA4E;AAC5E,oBAAoB;AACpB,QAAQ;AACR,EAAE;AACF,yEAAyE;AACzE,UAAU;AACV,EAAE;AACF,8DAA8D;AAC9D,EAAE;AACF,4DAA4D;AAE5D,kBAAkB,SAAS,CAAC,mBAAmB,GAAG;AAClD,OAAO,cAAc,CAAC,kBAAkB,SAAS,EAAE,sBAAsB;IACvE,cAAc;IACd,YAAY;IACZ,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU;QACrD;QAEA,OAAO,IAAI,CAAC,mBAAmB;IACjC;AACF;AAEA,kBAAkB,SAAS,CAAC,kBAAkB,GAAG;AACjD,OAAO,cAAc,CAAC,kBAAkB,SAAS,EAAE,qBAAqB;IACtE,cAAc;IACd,YAAY;IACZ,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU;QACrD;QAEA,OAAO,IAAI,CAAC,kBAAkB;IAChC;AACF;AAEA,kBAAkB,SAAS,CAAC,uBAAuB,GACjD,SAAS,yCAAyC,IAAI,EAAE,KAAK;IAC3D,IAAI,IAAI,KAAK,MAAM,CAAC;IACpB,OAAO,MAAM,OAAO,MAAM;AAC5B;AAEF;;;;CAIC,GACD,kBAAkB,SAAS,CAAC,cAAc,GACxC,SAAS,gCAAgC,IAAI,EAAE,WAAW;IACxD,MAAM,IAAI,MAAM;AAClB;AAEF,kBAAkB,eAAe,GAAG;AACpC,kBAAkB,cAAc,GAAG;AAEnC,kBAAkB,oBAAoB,GAAG;AACzC,kBAAkB,iBAAiB,GAAG;AAEtC;;;;;;;;;;;;;;;CAeC,GACD,kBAAkB,SAAS,CAAC,WAAW,GACrC,SAAS,8BAA8B,SAAS,EAAE,QAAQ,EAAE,MAAM;IAChE,IAAI,UAAU,YAAY;IAC1B,IAAI,QAAQ,UAAU,kBAAkB,eAAe;IAEvD,IAAI;IACJ,OAAQ;QACR,KAAK,kBAAkB,eAAe;YACpC,WAAW,IAAI,CAAC,kBAAkB;YAClC;QACF,KAAK,kBAAkB,cAAc;YACnC,WAAW,IAAI,CAAC,iBAAiB;YACjC;QACF;YACE,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,aAAa,IAAI,CAAC,UAAU;IAChC,IAAI,gBAAgB,UAAU,IAAI,CAAC;IACnC,IAAI,QAAQ,IAAI,CAAC,MAAM;IACvB,IAAI,UAAU,IAAI,CAAC,QAAQ;IAC3B,IAAI,eAAe,IAAI,CAAC,aAAa;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAI,GAAG,IAAK;QAC/C,IAAI,UAAU,QAAQ,CAAC,EAAE;QACzB,IAAI,SAAS,QAAQ,MAAM,KAAK,OAAO,OAAO,QAAQ,EAAE,CAAC,QAAQ,MAAM;QACvE,IAAG,WAAW,MAAM;YAClB,SAAS,KAAK,gBAAgB,CAAC,YAAY,QAAQ;QACrD;QACA,cAAc;YACZ,QAAQ;YACR,eAAe,QAAQ,aAAa;YACpC,iBAAiB,QAAQ,eAAe;YACxC,cAAc,QAAQ,YAAY;YAClC,gBAAgB,QAAQ,cAAc;YACtC,MAAM,QAAQ,IAAI,KAAK,OAAO,OAAO,MAAM,EAAE,CAAC,QAAQ,IAAI;QAC5D;IACF;AACF;AAEF;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,kBAAkB,SAAS,CAAC,wBAAwB,GAClD,SAAS,2CAA2C,KAAK;IACvD,IAAI,OAAO,KAAK,MAAM,CAAC,OAAO;IAE9B,8EAA8E;IAC9E,oEAAoE;IACpE,wEAAwE;IACxE,kDAAkD;IAClD,IAAI,SAAS;QACX,QAAQ,KAAK,MAAM,CAAC,OAAO;QAC3B,cAAc;QACd,gBAAgB,KAAK,MAAM,CAAC,OAAO,UAAU;IAC/C;IAEA,OAAO,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,MAAM;IACnD,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,OAAO,EAAE;IACX;IAEA,IAAI,WAAW,EAAE;IAEjB,IAAI,QAAQ,IAAI,CAAC,YAAY,CAAC,QACA,IAAI,CAAC,iBAAiB,EACtB,gBACA,kBACA,KAAK,0BAA0B,EAC/B,aAAa,iBAAiB;IAC5D,IAAI,SAAS,GAAG;QACd,IAAI,UAAU,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAE3C,IAAI,MAAM,MAAM,KAAK,WAAW;YAC9B,IAAI,eAAe,QAAQ,YAAY;YAEvC,8DAA8D;YAC9D,8DAA8D;YAC9D,mEAAmE;YACnE,qBAAqB;YACrB,MAAO,WAAW,QAAQ,YAAY,KAAK,aAAc;gBACvD,SAAS,IAAI,CAAC;oBACZ,MAAM,KAAK,MAAM,CAAC,SAAS,iBAAiB;oBAC5C,QAAQ,KAAK,MAAM,CAAC,SAAS,mBAAmB;oBAChD,YAAY,KAAK,MAAM,CAAC,SAAS,uBAAuB;gBAC1D;gBAEA,UAAU,IAAI,CAAC,iBAAiB,CAAC,EAAE,MAAM;YAC3C;QACF,OAAO;YACL,IAAI,iBAAiB,QAAQ,cAAc;YAE3C,8DAA8D;YAC9D,qEAAqE;YACrE,yEAAyE;YACzE,iCAAiC;YACjC,MAAO,WACA,QAAQ,YAAY,KAAK,QACzB,QAAQ,cAAc,IAAI,eAAgB;gBAC/C,SAAS,IAAI,CAAC;oBACZ,MAAM,KAAK,MAAM,CAAC,SAAS,iBAAiB;oBAC5C,QAAQ,KAAK,MAAM,CAAC,SAAS,mBAAmB;oBAChD,YAAY,KAAK,MAAM,CAAC,SAAS,uBAAuB;gBAC1D;gBAEA,UAAU,IAAI,CAAC,iBAAiB,CAAC,EAAE,MAAM;YAC3C;QACF;IACF;IAEA,OAAO;AACT;AAEF,QAAQ,iBAAiB,GAAG;AAE5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC,GACD,SAAS,uBAAuB,UAAU,EAAE,aAAa;IACvD,IAAI,YAAY;IAChB,IAAI,OAAO,eAAe,UAAU;QAClC,YAAY,KAAK,mBAAmB,CAAC;IACvC;IAEA,IAAI,UAAU,KAAK,MAAM,CAAC,WAAW;IACrC,IAAI,UAAU,KAAK,MAAM,CAAC,WAAW;IACrC,4EAA4E;IAC5E,yCAAyC;IACzC,IAAI,QAAQ,KAAK,MAAM,CAAC,WAAW,SAAS,EAAE;IAC9C,IAAI,aAAa,KAAK,MAAM,CAAC,WAAW,cAAc;IACtD,IAAI,iBAAiB,KAAK,MAAM,CAAC,WAAW,kBAAkB;IAC9D,IAAI,WAAW,KAAK,MAAM,CAAC,WAAW;IACtC,IAAI,OAAO,KAAK,MAAM,CAAC,WAAW,QAAQ;IAE1C,wEAAwE;IACxE,uEAAuE;IACvE,IAAI,WAAW,IAAI,CAAC,QAAQ,EAAE;QAC5B,MAAM,IAAI,MAAM,0BAA0B;IAC5C;IAEA,IAAI,YAAY;QACd,aAAa,KAAK,SAAS,CAAC;IAC9B;IAEA,UAAU,QACP,GAAG,CAAC,OACL,4EAA4E;IAC5E,4EAA4E;IAC5E,yBAAyB;KACxB,GAAG,CAAC,KAAK,SAAS,CACnB,wEAAwE;IACxE,wEAAwE;IACxE,sEAAsE;IACtE,0EAA0E;KACzE,GAAG,CAAC,SAAU,MAAM;QACnB,OAAO,cAAc,KAAK,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC,UAChE,KAAK,QAAQ,CAAC,YAAY,UAC1B;IACN;IAEF,4EAA4E;IAC5E,0EAA0E;IAC1E,4EAA4E;IAC5E,4BAA4B;IAC5B,IAAI,CAAC,MAAM,GAAG,SAAS,SAAS,CAAC,MAAM,GAAG,CAAC,SAAS;IACpD,IAAI,CAAC,QAAQ,GAAG,SAAS,SAAS,CAAC,SAAS;IAE5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,SAAU,CAAC;QAC7D,OAAO,KAAK,gBAAgB,CAAC,YAAY,GAAG;IAC9C;IAEA,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,aAAa,GAAG;IACrB,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,uBAAuB,SAAS,GAAG,OAAO,MAAM,CAAC,kBAAkB,SAAS;AAC5E,uBAAuB,SAAS,CAAC,QAAQ,GAAG;AAE5C;;;CAGC,GACD,uBAAuB,SAAS,CAAC,gBAAgB,GAAG,SAAS,OAAO;IAClE,IAAI,iBAAiB;IACrB,IAAI,IAAI,CAAC,UAAU,IAAI,MAAM;QAC3B,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;IAClD;IAEA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB;QACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC/B;IAEA,iEAAiE;IACjE,gDAAgD;IAChD,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,EAAG;QACjD,IAAI,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,SAAS;YACvC,OAAO;QACT;IACF;IAEA,OAAO,CAAC;AACV;AAEA;;;;;;;;CAQC,GACD,uBAAuB,aAAa,GAClC,SAAS,gCAAgC,UAAU,EAAE,aAAa;IAChE,IAAI,MAAM,OAAO,MAAM,CAAC,uBAAuB,SAAS;IAExD,IAAI,QAAQ,IAAI,MAAM,GAAG,SAAS,SAAS,CAAC,WAAW,MAAM,CAAC,OAAO,IAAI;IACzE,IAAI,UAAU,IAAI,QAAQ,GAAG,SAAS,SAAS,CAAC,WAAW,QAAQ,CAAC,OAAO,IAAI;IAC/E,IAAI,UAAU,GAAG,WAAW,WAAW;IACvC,IAAI,cAAc,GAAG,WAAW,uBAAuB,CAAC,IAAI,QAAQ,CAAC,OAAO,IACpB,IAAI,UAAU;IACtE,IAAI,IAAI,GAAG,WAAW,KAAK;IAC3B,IAAI,aAAa,GAAG;IACpB,IAAI,gBAAgB,GAAG,IAAI,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,SAAU,CAAC;QAC3D,OAAO,KAAK,gBAAgB,CAAC,IAAI,UAAU,EAAE,GAAG;IAClD;IAEA,yEAAyE;IACzE,0EAA0E;IAC1E,sEAAsE;IACtE,wCAAwC;IAExC,IAAI,oBAAoB,WAAW,SAAS,CAAC,OAAO,GAAG,KAAK;IAC5D,IAAI,wBAAwB,IAAI,mBAAmB,GAAG,EAAE;IACxD,IAAI,uBAAuB,IAAI,kBAAkB,GAAG,EAAE;IAEtD,IAAK,IAAI,IAAI,GAAG,SAAS,kBAAkB,MAAM,EAAE,IAAI,QAAQ,IAAK;QAClE,IAAI,aAAa,iBAAiB,CAAC,EAAE;QACrC,IAAI,cAAc,IAAI;QACtB,YAAY,aAAa,GAAG,WAAW,aAAa;QACpD,YAAY,eAAe,GAAG,WAAW,eAAe;QAExD,IAAI,WAAW,MAAM,EAAE;YACrB,YAAY,MAAM,GAAG,QAAQ,OAAO,CAAC,WAAW,MAAM;YACtD,YAAY,YAAY,GAAG,WAAW,YAAY;YAClD,YAAY,cAAc,GAAG,WAAW,cAAc;YAEtD,IAAI,WAAW,IAAI,EAAE;gBACnB,YAAY,IAAI,GAAG,MAAM,OAAO,CAAC,WAAW,IAAI;YAClD;YAEA,qBAAqB,IAAI,CAAC;QAC5B;QAEA,sBAAsB,IAAI,CAAC;IAC7B;IAEA,UAAU,IAAI,kBAAkB,EAAE,KAAK,0BAA0B;IAEjE,OAAO;AACT;AAEF;;CAEC,GACD,uBAAuB,SAAS,CAAC,QAAQ,GAAG;AAE5C;;CAEC,GACD,OAAO,cAAc,CAAC,uBAAuB,SAAS,EAAE,WAAW;IACjE,KAAK;QACH,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK;IACpC;AACF;AAEA;;CAEC,GACD,SAAS;IACP,IAAI,CAAC,aAAa,GAAG;IACrB,IAAI,CAAC,eAAe,GAAG;IACvB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,IAAI,GAAG;AACd;AAEA;;;;CAIC,GAED,MAAM,mBAAmB,KAAK,yCAAyC;AACvE,SAAS,cAAc,KAAK,EAAE,KAAK;IACjC,IAAI,IAAI,MAAM,MAAM;IACpB,IAAI,IAAI,MAAM,MAAM,GAAG;IACvB,IAAI,KAAK,GAAG;QACV;IACF,OAAO,IAAI,KAAK,GAAG;QACjB,IAAI,IAAI,KAAK,CAAC,MAAM;QACpB,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;QACxB,IAAI,iBAAiB,GAAG,KAAK,GAAG;YAC9B,KAAK,CAAC,MAAM,GAAG;YACf,KAAK,CAAC,QAAQ,EAAE,GAAG;QACrB;IACF,OAAO,IAAI,IAAI,IAAI;QACjB,IAAK,IAAI,IAAI,OAAO,IAAI,GAAG,IAAK;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE;gBACpB,IAAI,IAAI,KAAK,CAAC,EAAE;gBAChB,IAAI,iBAAiB,GAAG,MAAM,GAAG;oBAC/B;gBACF;gBACA,KAAK,CAAC,IAAI,EAAE,GAAG;gBACf,KAAK,CAAC,EAAE,GAAG;YACb;QACF;IACF,OAAO;QACL,UAAU,OAAO,kBAAkB;IACrC;AACF;AACA,uBAAuB,SAAS,CAAC,cAAc,GAC7C,SAAS,gCAAgC,IAAI,EAAE,WAAW;IACxD,IAAI,gBAAgB;IACpB,IAAI,0BAA0B;IAC9B,IAAI,uBAAuB;IAC3B,IAAI,yBAAyB;IAC7B,IAAI,iBAAiB;IACrB,IAAI,eAAe;IACnB,IAAI,SAAS,KAAK,MAAM;IACxB,IAAI,QAAQ;IACZ,IAAI,iBAAiB,CAAC;IACtB,IAAI,OAAO,CAAC;IACZ,IAAI,mBAAmB,EAAE;IACzB,IAAI,oBAAoB,EAAE;IAC1B,IAAI,SAAS,KAAK,SAAS,KAAK;IAEhC,IAAI,gBAAgB;IACpB,MAAO,QAAQ,OAAQ;QACrB,IAAI,KAAK,MAAM,CAAC,WAAW,KAAK;YAC9B;YACA;YACA,0BAA0B;YAE1B,cAAc,mBAAmB;YACjC,gBAAgB,kBAAkB,MAAM;QAC1C,OACK,IAAI,KAAK,MAAM,CAAC,WAAW,KAAK;YACnC;QACF,OACK;YACH,UAAU,IAAI;YACd,QAAQ,aAAa,GAAG;YAExB,IAAK,MAAM,OAAO,MAAM,QAAQ,MAAO;gBACrC,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,MAAM;oBAC3C;gBACF;YACF;YACA,MAAM,KAAK,KAAK,CAAC,OAAO;YAExB,UAAU,EAAE;YACZ,MAAO,QAAQ,IAAK;gBAClB,UAAU,MAAM,CAAC,MAAM,OAAO;gBAC9B,QAAQ,KAAK,KAAK;gBAClB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,IAAI,CAAC;YACf;YAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,oBAAoB;YACpB,QAAQ,eAAe,GAAG,0BAA0B,OAAO,CAAC,EAAE;YAC9D,0BAA0B,QAAQ,eAAe;YAEjD,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,mBAAmB;gBACnB,QAAQ,MAAM,GAAG,iBAAiB,OAAO,CAAC,EAAE;gBAC5C,kBAAkB,OAAO,CAAC,EAAE;gBAE5B,iBAAiB;gBACjB,QAAQ,YAAY,GAAG,uBAAuB,OAAO,CAAC,EAAE;gBACxD,uBAAuB,QAAQ,YAAY;gBAC3C,2BAA2B;gBAC3B,QAAQ,YAAY,IAAI;gBAExB,mBAAmB;gBACnB,QAAQ,cAAc,GAAG,yBAAyB,OAAO,CAAC,EAAE;gBAC5D,yBAAyB,QAAQ,cAAc;gBAE/C,IAAI,QAAQ,MAAM,GAAG,GAAG;oBACtB,iBAAiB;oBACjB,QAAQ,IAAI,GAAG,eAAe,OAAO,CAAC,EAAE;oBACxC,gBAAgB,OAAO,CAAC,EAAE;gBAC5B;YACF;YAEA,kBAAkB,IAAI,CAAC;YACvB,IAAI,OAAO,QAAQ,YAAY,KAAK,UAAU;gBAC5C,IAAI,gBAAgB,QAAQ,MAAM;gBAClC,MAAO,iBAAiB,MAAM,IAAI,cAAe;oBAC/C,iBAAiB,IAAI,CAAC;gBACxB;gBACA,IAAI,gBAAgB,CAAC,cAAc,KAAK,MAAM;oBAC5C,gBAAgB,CAAC,cAAc,GAAG,EAAE;gBACtC;gBACA,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC;YACvC;QACF;IACF;IAEA,cAAc,mBAAmB;IACjC,IAAI,CAAC,mBAAmB,GAAG;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;QAChD,IAAI,gBAAgB,CAAC,EAAE,IAAI,MAAM;YAC/B,UAAU,gBAAgB,CAAC,EAAE,EAAE,KAAK,kCAAkC;QACxE;IACF;IACA,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,MAAM,IAAI;AACzC;AAEF;;;CAGC,GACD,uBAAuB,SAAS,CAAC,YAAY,GAC3C,SAAS,8BAA8B,OAAO,EAAE,SAAS,EAAE,SAAS,EAC7B,WAAW,EAAE,WAAW,EAAE,KAAK;IACpE,sEAAsE;IACtE,0EAA0E;IAC1E,0EAA0E;IAC1E,yBAAyB;IAEzB,IAAI,OAAO,CAAC,UAAU,IAAI,GAAG;QAC3B,MAAM,IAAI,UAAU,kDACE,OAAO,CAAC,UAAU;IAC1C;IACA,IAAI,OAAO,CAAC,YAAY,GAAG,GAAG;QAC5B,MAAM,IAAI,UAAU,oDACE,OAAO,CAAC,YAAY;IAC5C;IAEA,OAAO,aAAa,MAAM,CAAC,SAAS,WAAW,aAAa;AAC9D;AAEF;;;CAGC,GACD,uBAAuB,SAAS,CAAC,kBAAkB,GACjD,SAAS;IACP,IAAK,IAAI,QAAQ,GAAG,QAAQ,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE,MAAO;QACnE,IAAI,UAAU,IAAI,CAAC,kBAAkB,CAAC,MAAM;QAE5C,qEAAqE;QACrE,qEAAqE;QACrE,oEAAoE;QACpE,mDAAmD;QACnD,IAAI,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;YAC9C,IAAI,cAAc,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE;YAEpD,IAAI,QAAQ,aAAa,KAAK,YAAY,aAAa,EAAE;gBACvD,QAAQ,mBAAmB,GAAG,YAAY,eAAe,GAAG;gBAC5D;YACF;QACF;QAEA,wDAAwD;QACxD,QAAQ,mBAAmB,GAAG;IAChC;AACF;AAEF;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,uBAAuB,SAAS,CAAC,mBAAmB,GAClD,SAAS,sCAAsC,KAAK;IAClD,IAAI,SAAS;QACX,eAAe,KAAK,MAAM,CAAC,OAAO;QAClC,iBAAiB,KAAK,MAAM,CAAC,OAAO;IACtC;IAEA,IAAI,QAAQ,IAAI,CAAC,YAAY,CAC3B,QACA,IAAI,CAAC,kBAAkB,EACvB,iBACA,mBACA,KAAK,mCAAmC,EACxC,KAAK,MAAM,CAAC,OAAO,QAAQ,kBAAkB,oBAAoB;IAGnE,IAAI,SAAS,GAAG;QACd,IAAI,UAAU,IAAI,CAAC,kBAAkB,CAAC,MAAM;QAE5C,IAAI,QAAQ,aAAa,KAAK,OAAO,aAAa,EAAE;YAClD,IAAI,SAAS,KAAK,MAAM,CAAC,SAAS,UAAU;YAC5C,IAAI,WAAW,MAAM;gBACnB,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1B,SAAS,KAAK,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,aAAa;YAC5E;YACA,IAAI,OAAO,KAAK,MAAM,CAAC,SAAS,QAAQ;YACxC,IAAI,SAAS,MAAM;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACxB;YACA,OAAO;gBACL,QAAQ;gBACR,MAAM,KAAK,MAAM,CAAC,SAAS,gBAAgB;gBAC3C,QAAQ,KAAK,MAAM,CAAC,SAAS,kBAAkB;gBAC/C,MAAM;YACR;QACF;IACF;IAEA,OAAO;QACL,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,MAAM;IACR;AACF;AAEF;;;CAGC,GACD,uBAAuB,SAAS,CAAC,uBAAuB,GACtD,SAAS;IACP,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;QACxB,OAAO;IACT;IACA,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,MACrD,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAU,EAAE;QAAI,OAAO,MAAM;IAAM;AACjE;AAEF;;;;CAIC,GACD,uBAAuB,SAAS,CAAC,gBAAgB,GAC/C,SAAS,mCAAmC,OAAO,EAAE,aAAa;IAChE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;QACxB,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,CAAC,gBAAgB,CAAC;IAClC,IAAI,SAAS,GAAG;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM;IACnC;IAEA,IAAI,iBAAiB;IACrB,IAAI,IAAI,CAAC,UAAU,IAAI,MAAM;QAC3B,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;IAClD;IAEA,IAAI;IACJ,IAAI,IAAI,CAAC,UAAU,IAAI,QAChB,CAAC,MAAM,KAAK,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG;QAC7C,uEAAuE;QACvE,oEAAoE;QACpE,qEAAqE;QACrE,uDAAuD;QACvD,IAAI,iBAAiB,eAAe,OAAO,CAAC,cAAc;QAC1D,IAAI,IAAI,MAAM,IAAI,UACX,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB;YACxC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB;QACnE;QAEA,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,KAC1B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,iBAAiB;YAC9C,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,gBAAgB;QACzE;IACF;IAEA,yCAAyC;IACzC,wEAAwE;IACxE,oEAAoE;IACpE,wDAAwD;IACxD,IAAI,eAAe;QACjB,OAAO;IACT,OACK;QACH,MAAM,IAAI,MAAM,MAAM,iBAAiB;IACzC;AACF;AAEF;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,uBAAuB,SAAS,CAAC,oBAAoB,GACnD,SAAS,uCAAuC,KAAK;IACnD,IAAI,SAAS,KAAK,MAAM,CAAC,OAAO;IAChC,SAAS,IAAI,CAAC,gBAAgB,CAAC;IAC/B,IAAI,SAAS,GAAG;QACd,OAAO;YACL,MAAM;YACN,QAAQ;YACR,YAAY;QACd;IACF;IAEA,IAAI,SAAS;QACX,QAAQ;QACR,cAAc,KAAK,MAAM,CAAC,OAAO;QACjC,gBAAgB,KAAK,MAAM,CAAC,OAAO;IACrC;IAEA,IAAI,QAAQ,IAAI,CAAC,YAAY,CAC3B,QACA,IAAI,CAAC,iBAAiB,EACtB,gBACA,kBACA,KAAK,0BAA0B,EAC/B,KAAK,MAAM,CAAC,OAAO,QAAQ,kBAAkB,oBAAoB;IAGnE,IAAI,SAAS,GAAG;QACd,IAAI,UAAU,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAE3C,IAAI,QAAQ,MAAM,KAAK,OAAO,MAAM,EAAE;YACpC,OAAO;gBACL,MAAM,KAAK,MAAM,CAAC,SAAS,iBAAiB;gBAC5C,QAAQ,KAAK,MAAM,CAAC,SAAS,mBAAmB;gBAChD,YAAY,KAAK,MAAM,CAAC,SAAS,uBAAuB;YAC1D;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN,QAAQ;QACR,YAAY;IACd;AACF;AAEF,QAAQ,sBAAsB,GAAG;AAEjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgDC,GACD,SAAS,yBAAyB,UAAU,EAAE,aAAa;IACzD,IAAI,YAAY;IAChB,IAAI,OAAO,eAAe,UAAU;QAClC,YAAY,KAAK,mBAAmB,CAAC;IACvC;IAEA,IAAI,UAAU,KAAK,MAAM,CAAC,WAAW;IACrC,IAAI,WAAW,KAAK,MAAM,CAAC,WAAW;IAEtC,IAAI,WAAW,IAAI,CAAC,QAAQ,EAAE;QAC5B,MAAM,IAAI,MAAM,0BAA0B;IAC5C;IAEA,IAAI,CAAC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC,MAAM,GAAG,IAAI;IAElB,IAAI,aAAa;QACf,MAAM,CAAC;QACP,QAAQ;IACV;IACA,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG,CAAC,SAAU,CAAC;QACvC,IAAI,EAAE,GAAG,EAAE;YACT,yDAAyD;YACzD,sDAAsD;YACtD,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,SAAS,KAAK,MAAM,CAAC,GAAG;QAC5B,IAAI,aAAa,KAAK,MAAM,CAAC,QAAQ;QACrC,IAAI,eAAe,KAAK,MAAM,CAAC,QAAQ;QAEvC,IAAI,aAAa,WAAW,IAAI,IAC3B,eAAe,WAAW,IAAI,IAAI,eAAe,WAAW,MAAM,EAAG;YACxE,MAAM,IAAI,MAAM;QAClB;QACA,aAAa;QAEb,OAAO;YACL,iBAAiB;gBACf,iEAAiE;gBACjE,8BAA8B;gBAC9B,eAAe,aAAa;gBAC5B,iBAAiB,eAAe;YAClC;YACA,UAAU,IAAI,kBAAkB,KAAK,MAAM,CAAC,GAAG,QAAQ;QACzD;IACF;AACF;AAEA,yBAAyB,SAAS,GAAG,OAAO,MAAM,CAAC,kBAAkB,SAAS;AAC9E,yBAAyB,SAAS,CAAC,WAAW,GAAG;AAEjD;;CAEC,GACD,yBAAyB,SAAS,CAAC,QAAQ,GAAG;AAE9C;;CAEC,GACD,OAAO,cAAc,CAAC,yBAAyB,SAAS,EAAE,WAAW;IACnE,KAAK;QACH,IAAI,UAAU,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;YAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;gBAClE,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YACpD;QACF;QACA,OAAO;IACT;AACF;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GACD,yBAAyB,SAAS,CAAC,mBAAmB,GACpD,SAAS,6CAA6C,KAAK;IACzD,IAAI,SAAS;QACX,eAAe,KAAK,MAAM,CAAC,OAAO;QAClC,iBAAiB,KAAK,MAAM,CAAC,OAAO;IACtC;IAEA,yEAAyE;IACzE,2BAA2B;IAC3B,IAAI,eAAe,aAAa,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,EAC3D,SAAS,MAAM,EAAE,OAAO;QACtB,IAAI,MAAM,OAAO,aAAa,GAAG,QAAQ,eAAe,CAAC,aAAa;QACtE,IAAI,KAAK;YACP,OAAO;QACT;QAEA,OAAQ,OAAO,eAAe,GACtB,QAAQ,eAAe,CAAC,eAAe;IACjD;IACF,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,aAAa;IAE1C,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,MAAM;QACR;IACF;IAEA,OAAO,QAAQ,QAAQ,CAAC,mBAAmB,CAAC;QAC1C,MAAM,OAAO,aAAa,GACxB,CAAC,QAAQ,eAAe,CAAC,aAAa,GAAG,CAAC;QAC5C,QAAQ,OAAO,eAAe,GAC5B,CAAC,QAAQ,eAAe,CAAC,aAAa,KAAK,OAAO,aAAa,GAC5D,QAAQ,eAAe,CAAC,eAAe,GAAG,IAC1C,CAAC;QACN,MAAM,MAAM,IAAI;IAClB;AACF;AAEF;;;CAGC,GACD,yBAAyB,SAAS,CAAC,uBAAuB,GACxD,SAAS;IACP,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAU,CAAC;QACrC,OAAO,EAAE,QAAQ,CAAC,uBAAuB;IAC3C;AACF;AAEF;;;;CAIC,GACD,yBAAyB,SAAS,CAAC,gBAAgB,GACjD,SAAS,0CAA0C,OAAO,EAAE,aAAa;IACvE,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;QAC9C,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,EAAE;QAE/B,IAAI,UAAU,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,SAAS;QACzD,IAAI,WAAW,YAAY,IAAI;YAC7B,OAAO;QACT;IACF;IACA,IAAI,eAAe;QACjB,OAAO;IACT,OACK;QACH,MAAM,IAAI,MAAM,MAAM,UAAU;IAClC;AACF;AAEF;;;;;;;;;;;;;;;;;CAiBC,GACD,yBAAyB,SAAS,CAAC,oBAAoB,GACrD,SAAS,8CAA8C,KAAK;IAC1D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;QAC9C,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,EAAE;QAE/B,uEAAuE;QACvE,2BAA2B;QAC3B,IAAI,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,KAAK,MAAM,CAAC,OAAO,eAAe,CAAC,GAAG;YAC1E;QACF;QACA,IAAI,oBAAoB,QAAQ,QAAQ,CAAC,oBAAoB,CAAC;QAC9D,IAAI,mBAAmB;YACrB,IAAI,MAAM;gBACR,MAAM,kBAAkB,IAAI,GAC1B,CAAC,QAAQ,eAAe,CAAC,aAAa,GAAG,CAAC;gBAC5C,QAAQ,kBAAkB,MAAM,GAC9B,CAAC,QAAQ,eAAe,CAAC,aAAa,KAAK,kBAAkB,IAAI,GAC9D,QAAQ,eAAe,CAAC,eAAe,GAAG,IAC1C,CAAC;YACR;YACA,OAAO;QACT;IACF;IAEA,OAAO;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEF;;;;CAIC,GACD,yBAAyB,SAAS,CAAC,cAAc,GAC/C,SAAS,uCAAuC,IAAI,EAAE,WAAW;IAC/D,IAAI,CAAC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC,kBAAkB,GAAG,EAAE;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;QAC9C,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,EAAE;QAC/B,IAAI,kBAAkB,QAAQ,QAAQ,CAAC,kBAAkB;QACzD,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,IAAI,UAAU,eAAe,CAAC,EAAE;YAEhC,IAAI,SAAS,QAAQ,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,MAAM;YACxD,IAAG,WAAW,MAAM;gBAClB,SAAS,KAAK,gBAAgB,CAAC,QAAQ,QAAQ,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,aAAa;YACxF;YACA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;YAClB,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAE/B,IAAI,OAAO;YACX,IAAI,QAAQ,IAAI,EAAE;gBAChB,OAAO,QAAQ,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,IAAI;gBAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;gBAChB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAC7B;YAEA,6DAA6D;YAC7D,kEAAkE;YAClE,sEAAsE;YACtE,kBAAkB;YAClB,IAAI,kBAAkB;gBACpB,QAAQ;gBACR,eAAe,QAAQ,aAAa,GAClC,CAAC,QAAQ,eAAe,CAAC,aAAa,GAAG,CAAC;gBAC5C,iBAAiB,QAAQ,eAAe,GACtC,CAAC,QAAQ,eAAe,CAAC,aAAa,KAAK,QAAQ,aAAa,GAC9D,QAAQ,eAAe,CAAC,eAAe,GAAG,IAC1C,CAAC;gBACL,cAAc,QAAQ,YAAY;gBAClC,gBAAgB,QAAQ,cAAc;gBACtC,MAAM;YACR;YAEA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC9B,IAAI,OAAO,gBAAgB,YAAY,KAAK,UAAU;gBACpD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAC/B;QACF;IACF;IAEA,UAAU,IAAI,CAAC,mBAAmB,EAAE,KAAK,mCAAmC;IAC5E,UAAU,IAAI,CAAC,kBAAkB,EAAE,KAAK,0BAA0B;AACpE;AAEF,QAAQ,wBAAwB,GAAG", "ignoreList": [0]}}, {"offset": {"line": 4114, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/lib/source-node.js"], "sourcesContent": ["/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar SourceMapGenerator = require('./source-map-generator').SourceMapGenerator;\nvar util = require('./util');\n\n// Matches a Windows-style `\\r\\n` newline or a `\\n` newline used by all other\n// operating systems these days (capturing the result).\nvar REGEX_NEWLINE = /(\\r?\\n)/;\n\n// Newline character code for charCodeAt() comparisons\nvar NEWLINE_CODE = 10;\n\n// Private symbol for identifying `SourceNode`s when multiple versions of\n// the source-map library are loaded. This MUST NOT CHANGE across\n// versions!\nvar isSourceNode = \"$$$isSourceNode$$$\";\n\n/**\n * SourceNodes provide a way to abstract over interpolating/concatenating\n * snippets of generated JavaScript source code while maintaining the line and\n * column information associated with the original source code.\n *\n * @param aLine The original line number.\n * @param aColumn The original column number.\n * @param aSource The original source's filename.\n * @param aChunks Optional. An array of strings which are snippets of\n *        generated JS, or other SourceNodes.\n * @param aName The original identifier.\n */\nfunction SourceNode(aLine, aColumn, aSource, aChunks, aName) {\n  this.children = [];\n  this.sourceContents = {};\n  this.line = aLine == null ? null : aLine;\n  this.column = aColumn == null ? null : aColumn;\n  this.source = aSource == null ? null : aSource;\n  this.name = aName == null ? null : aName;\n  this[isSourceNode] = true;\n  if (aChunks != null) this.add(aChunks);\n}\n\n/**\n * Creates a SourceNode from generated code and a SourceMapConsumer.\n *\n * @param aGeneratedCode The generated code\n * @param aSourceMapConsumer The SourceMap for the generated code\n * @param aRelativePath Optional. The path that relative sources in the\n *        SourceMapConsumer should be relative to.\n */\nSourceNode.fromStringWithSourceMap =\n  function SourceNode_fromStringWithSourceMap(aGeneratedCode, aSourceMapConsumer, aRelativePath) {\n    // The SourceNode we want to fill with the generated code\n    // and the SourceMap\n    var node = new SourceNode();\n\n    // All even indices of this array are one line of the generated code,\n    // while all odd indices are the newlines between two adjacent lines\n    // (since `REGEX_NEWLINE` captures its match).\n    // Processed fragments are accessed by calling `shiftNextLine`.\n    var remainingLines = aGeneratedCode.split(REGEX_NEWLINE);\n    var remainingLinesIndex = 0;\n    var shiftNextLine = function() {\n      var lineContents = getNextLine();\n      // The last line of a file might not have a newline.\n      var newLine = getNextLine() || \"\";\n      return lineContents + newLine;\n\n      function getNextLine() {\n        return remainingLinesIndex < remainingLines.length ?\n            remainingLines[remainingLinesIndex++] : undefined;\n      }\n    };\n\n    // We need to remember the position of \"remainingLines\"\n    var lastGeneratedLine = 1, lastGeneratedColumn = 0;\n\n    // The generate SourceNodes we need a code range.\n    // To extract it current and last mapping is used.\n    // Here we store the last mapping.\n    var lastMapping = null;\n\n    aSourceMapConsumer.eachMapping(function (mapping) {\n      if (lastMapping !== null) {\n        // We add the code from \"lastMapping\" to \"mapping\":\n        // First check if there is a new line in between.\n        if (lastGeneratedLine < mapping.generatedLine) {\n          // Associate first line with \"lastMapping\"\n          addMappingWithCode(lastMapping, shiftNextLine());\n          lastGeneratedLine++;\n          lastGeneratedColumn = 0;\n          // The remaining code is added without mapping\n        } else {\n          // There is no new line in between.\n          // Associate the code between \"lastGeneratedColumn\" and\n          // \"mapping.generatedColumn\" with \"lastMapping\"\n          var nextLine = remainingLines[remainingLinesIndex] || '';\n          var code = nextLine.substr(0, mapping.generatedColumn -\n                                        lastGeneratedColumn);\n          remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn -\n                                              lastGeneratedColumn);\n          lastGeneratedColumn = mapping.generatedColumn;\n          addMappingWithCode(lastMapping, code);\n          // No more remaining code, continue\n          lastMapping = mapping;\n          return;\n        }\n      }\n      // We add the generated code until the first mapping\n      // to the SourceNode without any mapping.\n      // Each line is added as separate string.\n      while (lastGeneratedLine < mapping.generatedLine) {\n        node.add(shiftNextLine());\n        lastGeneratedLine++;\n      }\n      if (lastGeneratedColumn < mapping.generatedColumn) {\n        var nextLine = remainingLines[remainingLinesIndex] || '';\n        node.add(nextLine.substr(0, mapping.generatedColumn));\n        remainingLines[remainingLinesIndex] = nextLine.substr(mapping.generatedColumn);\n        lastGeneratedColumn = mapping.generatedColumn;\n      }\n      lastMapping = mapping;\n    }, this);\n    // We have processed all mappings.\n    if (remainingLinesIndex < remainingLines.length) {\n      if (lastMapping) {\n        // Associate the remaining code in the current line with \"lastMapping\"\n        addMappingWithCode(lastMapping, shiftNextLine());\n      }\n      // and add the remaining lines without any mapping\n      node.add(remainingLines.splice(remainingLinesIndex).join(\"\"));\n    }\n\n    // Copy sourcesContent into SourceNode\n    aSourceMapConsumer.sources.forEach(function (sourceFile) {\n      var content = aSourceMapConsumer.sourceContentFor(sourceFile);\n      if (content != null) {\n        if (aRelativePath != null) {\n          sourceFile = util.join(aRelativePath, sourceFile);\n        }\n        node.setSourceContent(sourceFile, content);\n      }\n    });\n\n    return node;\n\n    function addMappingWithCode(mapping, code) {\n      if (mapping === null || mapping.source === undefined) {\n        node.add(code);\n      } else {\n        var source = aRelativePath\n          ? util.join(aRelativePath, mapping.source)\n          : mapping.source;\n        node.add(new SourceNode(mapping.originalLine,\n                                mapping.originalColumn,\n                                source,\n                                code,\n                                mapping.name));\n      }\n    }\n  };\n\n/**\n * Add a chunk of generated JS to this source node.\n *\n * @param aChunk A string snippet of generated JS code, another instance of\n *        SourceNode, or an array where each member is one of those things.\n */\nSourceNode.prototype.add = function SourceNode_add(aChunk) {\n  if (Array.isArray(aChunk)) {\n    aChunk.forEach(function (chunk) {\n      this.add(chunk);\n    }, this);\n  }\n  else if (aChunk[isSourceNode] || typeof aChunk === \"string\") {\n    if (aChunk) {\n      this.children.push(aChunk);\n    }\n  }\n  else {\n    throw new TypeError(\n      \"Expected a SourceNode, string, or an array of SourceNodes and strings. Got \" + aChunk\n    );\n  }\n  return this;\n};\n\n/**\n * Add a chunk of generated JS to the beginning of this source node.\n *\n * @param aChunk A string snippet of generated JS code, another instance of\n *        SourceNode, or an array where each member is one of those things.\n */\nSourceNode.prototype.prepend = function SourceNode_prepend(aChunk) {\n  if (Array.isArray(aChunk)) {\n    for (var i = aChunk.length-1; i >= 0; i--) {\n      this.prepend(aChunk[i]);\n    }\n  }\n  else if (aChunk[isSourceNode] || typeof aChunk === \"string\") {\n    this.children.unshift(aChunk);\n  }\n  else {\n    throw new TypeError(\n      \"Expected a SourceNode, string, or an array of SourceNodes and strings. Got \" + aChunk\n    );\n  }\n  return this;\n};\n\n/**\n * Walk over the tree of JS snippets in this node and its children. The\n * walking function is called once for each snippet of JS and is passed that\n * snippet and the its original associated source's line/column location.\n *\n * @param aFn The traversal function.\n */\nSourceNode.prototype.walk = function SourceNode_walk(aFn) {\n  var chunk;\n  for (var i = 0, len = this.children.length; i < len; i++) {\n    chunk = this.children[i];\n    if (chunk[isSourceNode]) {\n      chunk.walk(aFn);\n    }\n    else {\n      if (chunk !== '') {\n        aFn(chunk, { source: this.source,\n                     line: this.line,\n                     column: this.column,\n                     name: this.name });\n      }\n    }\n  }\n};\n\n/**\n * Like `String.prototype.join` except for SourceNodes. Inserts `aStr` between\n * each of `this.children`.\n *\n * @param aSep The separator.\n */\nSourceNode.prototype.join = function SourceNode_join(aSep) {\n  var newChildren;\n  var i;\n  var len = this.children.length;\n  if (len > 0) {\n    newChildren = [];\n    for (i = 0; i < len-1; i++) {\n      newChildren.push(this.children[i]);\n      newChildren.push(aSep);\n    }\n    newChildren.push(this.children[i]);\n    this.children = newChildren;\n  }\n  return this;\n};\n\n/**\n * Call String.prototype.replace on the very right-most source snippet. Useful\n * for trimming whitespace from the end of a source node, etc.\n *\n * @param aPattern The pattern to replace.\n * @param aReplacement The thing to replace the pattern with.\n */\nSourceNode.prototype.replaceRight = function SourceNode_replaceRight(aPattern, aReplacement) {\n  var lastChild = this.children[this.children.length - 1];\n  if (lastChild[isSourceNode]) {\n    lastChild.replaceRight(aPattern, aReplacement);\n  }\n  else if (typeof lastChild === 'string') {\n    this.children[this.children.length - 1] = lastChild.replace(aPattern, aReplacement);\n  }\n  else {\n    this.children.push(''.replace(aPattern, aReplacement));\n  }\n  return this;\n};\n\n/**\n * Set the source content for a source file. This will be added to the SourceMapGenerator\n * in the sourcesContent field.\n *\n * @param aSourceFile The filename of the source file\n * @param aSourceContent The content of the source file\n */\nSourceNode.prototype.setSourceContent =\n  function SourceNode_setSourceContent(aSourceFile, aSourceContent) {\n    this.sourceContents[util.toSetString(aSourceFile)] = aSourceContent;\n  };\n\n/**\n * Walk over the tree of SourceNodes. The walking function is called for each\n * source file content and is passed the filename and source content.\n *\n * @param aFn The traversal function.\n */\nSourceNode.prototype.walkSourceContents =\n  function SourceNode_walkSourceContents(aFn) {\n    for (var i = 0, len = this.children.length; i < len; i++) {\n      if (this.children[i][isSourceNode]) {\n        this.children[i].walkSourceContents(aFn);\n      }\n    }\n\n    var sources = Object.keys(this.sourceContents);\n    for (var i = 0, len = sources.length; i < len; i++) {\n      aFn(util.fromSetString(sources[i]), this.sourceContents[sources[i]]);\n    }\n  };\n\n/**\n * Return the string representation of this source node. Walks over the tree\n * and concatenates all the various snippets together to one string.\n */\nSourceNode.prototype.toString = function SourceNode_toString() {\n  var str = \"\";\n  this.walk(function (chunk) {\n    str += chunk;\n  });\n  return str;\n};\n\n/**\n * Returns the string representation of this source node along with a source\n * map.\n */\nSourceNode.prototype.toStringWithSourceMap = function SourceNode_toStringWithSourceMap(aArgs) {\n  var generated = {\n    code: \"\",\n    line: 1,\n    column: 0\n  };\n  var map = new SourceMapGenerator(aArgs);\n  var sourceMappingActive = false;\n  var lastOriginalSource = null;\n  var lastOriginalLine = null;\n  var lastOriginalColumn = null;\n  var lastOriginalName = null;\n  this.walk(function (chunk, original) {\n    generated.code += chunk;\n    if (original.source !== null\n        && original.line !== null\n        && original.column !== null) {\n      if(lastOriginalSource !== original.source\n         || lastOriginalLine !== original.line\n         || lastOriginalColumn !== original.column\n         || lastOriginalName !== original.name) {\n        map.addMapping({\n          source: original.source,\n          original: {\n            line: original.line,\n            column: original.column\n          },\n          generated: {\n            line: generated.line,\n            column: generated.column\n          },\n          name: original.name\n        });\n      }\n      lastOriginalSource = original.source;\n      lastOriginalLine = original.line;\n      lastOriginalColumn = original.column;\n      lastOriginalName = original.name;\n      sourceMappingActive = true;\n    } else if (sourceMappingActive) {\n      map.addMapping({\n        generated: {\n          line: generated.line,\n          column: generated.column\n        }\n      });\n      lastOriginalSource = null;\n      sourceMappingActive = false;\n    }\n    for (var idx = 0, length = chunk.length; idx < length; idx++) {\n      if (chunk.charCodeAt(idx) === NEWLINE_CODE) {\n        generated.line++;\n        generated.column = 0;\n        // Mappings end at eol\n        if (idx + 1 === length) {\n          lastOriginalSource = null;\n          sourceMappingActive = false;\n        } else if (sourceMappingActive) {\n          map.addMapping({\n            source: original.source,\n            original: {\n              line: original.line,\n              column: original.column\n            },\n            generated: {\n              line: generated.line,\n              column: generated.column\n            },\n            name: original.name\n          });\n        }\n      } else {\n        generated.column++;\n      }\n    }\n  });\n  this.walkSourceContents(function (sourceFile, sourceContent) {\n    map.setSourceContent(sourceFile, sourceContent);\n  });\n\n  return { code: generated.code, map: map };\n};\n\nexports.SourceNode = SourceNode;\n"], "names": [], "mappings": "AAAA,yCAAyC,GACzC;;;;CAIC,GAED,IAAI,qBAAqB,mHAAkC,kBAAkB;AAC7E,IAAI;AAEJ,6EAA6E;AAC7E,uDAAuD;AACvD,IAAI,gBAAgB;AAEpB,sDAAsD;AACtD,IAAI,eAAe;AAEnB,yEAAyE;AACzE,iEAAiE;AACjE,YAAY;AACZ,IAAI,eAAe;AAEnB;;;;;;;;;;;CAWC,GACD,SAAS,WAAW,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;IACzD,IAAI,CAAC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC,cAAc,GAAG,CAAC;IACvB,IAAI,CAAC,IAAI,GAAG,SAAS,OAAO,OAAO;IACnC,IAAI,CAAC,MAAM,GAAG,WAAW,OAAO,OAAO;IACvC,IAAI,CAAC,MAAM,GAAG,WAAW,OAAO,OAAO;IACvC,IAAI,CAAC,IAAI,GAAG,SAAS,OAAO,OAAO;IACnC,IAAI,CAAC,aAAa,GAAG;IACrB,IAAI,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC;AAChC;AAEA;;;;;;;CAOC,GACD,WAAW,uBAAuB,GAChC,SAAS,mCAAmC,cAAc,EAAE,kBAAkB,EAAE,aAAa;IAC3F,yDAAyD;IACzD,oBAAoB;IACpB,IAAI,OAAO,IAAI;IAEf,qEAAqE;IACrE,oEAAoE;IACpE,8CAA8C;IAC9C,+DAA+D;IAC/D,IAAI,iBAAiB,eAAe,KAAK,CAAC;IAC1C,IAAI,sBAAsB;IAC1B,IAAI,gBAAgB;QAClB,IAAI,eAAe;QACnB,oDAAoD;QACpD,IAAI,UAAU,iBAAiB;QAC/B,OAAO,eAAe;;QAEtB,SAAS;YACP,OAAO,sBAAsB,eAAe,MAAM,GAC9C,cAAc,CAAC,sBAAsB,GAAG;QAC9C;IACF;IAEA,uDAAuD;IACvD,IAAI,oBAAoB,GAAG,sBAAsB;IAEjD,iDAAiD;IACjD,kDAAkD;IAClD,kCAAkC;IAClC,IAAI,cAAc;IAElB,mBAAmB,WAAW,CAAC,SAAU,OAAO;QAC9C,IAAI,gBAAgB,MAAM;YACxB,mDAAmD;YACnD,iDAAiD;YACjD,IAAI,oBAAoB,QAAQ,aAAa,EAAE;gBAC7C,0CAA0C;gBAC1C,mBAAmB,aAAa;gBAChC;gBACA,sBAAsB;YACtB,8CAA8C;YAChD,OAAO;gBACL,mCAAmC;gBACnC,uDAAuD;gBACvD,+CAA+C;gBAC/C,IAAI,WAAW,cAAc,CAAC,oBAAoB,IAAI;gBACtD,IAAI,OAAO,SAAS,MAAM,CAAC,GAAG,QAAQ,eAAe,GACvB;gBAC9B,cAAc,CAAC,oBAAoB,GAAG,SAAS,MAAM,CAAC,QAAQ,eAAe,GACzC;gBACpC,sBAAsB,QAAQ,eAAe;gBAC7C,mBAAmB,aAAa;gBAChC,mCAAmC;gBACnC,cAAc;gBACd;YACF;QACF;QACA,oDAAoD;QACpD,yCAAyC;QACzC,yCAAyC;QACzC,MAAO,oBAAoB,QAAQ,aAAa,CAAE;YAChD,KAAK,GAAG,CAAC;YACT;QACF;QACA,IAAI,sBAAsB,QAAQ,eAAe,EAAE;YACjD,IAAI,WAAW,cAAc,CAAC,oBAAoB,IAAI;YACtD,KAAK,GAAG,CAAC,SAAS,MAAM,CAAC,GAAG,QAAQ,eAAe;YACnD,cAAc,CAAC,oBAAoB,GAAG,SAAS,MAAM,CAAC,QAAQ,eAAe;YAC7E,sBAAsB,QAAQ,eAAe;QAC/C;QACA,cAAc;IAChB,GAAG,IAAI;IACP,kCAAkC;IAClC,IAAI,sBAAsB,eAAe,MAAM,EAAE;QAC/C,IAAI,aAAa;YACf,sEAAsE;YACtE,mBAAmB,aAAa;QAClC;QACA,kDAAkD;QAClD,KAAK,GAAG,CAAC,eAAe,MAAM,CAAC,qBAAqB,IAAI,CAAC;IAC3D;IAEA,sCAAsC;IACtC,mBAAmB,OAAO,CAAC,OAAO,CAAC,SAAU,UAAU;QACrD,IAAI,UAAU,mBAAmB,gBAAgB,CAAC;QAClD,IAAI,WAAW,MAAM;YACnB,IAAI,iBAAiB,MAAM;gBACzB,aAAa,KAAK,IAAI,CAAC,eAAe;YACxC;YACA,KAAK,gBAAgB,CAAC,YAAY;QACpC;IACF;IAEA,OAAO;;IAEP,SAAS,mBAAmB,OAAO,EAAE,IAAI;QACvC,IAAI,YAAY,QAAQ,QAAQ,MAAM,KAAK,WAAW;YACpD,KAAK,GAAG,CAAC;QACX,OAAO;YACL,IAAI,SAAS,gBACT,KAAK,IAAI,CAAC,eAAe,QAAQ,MAAM,IACvC,QAAQ,MAAM;YAClB,KAAK,GAAG,CAAC,IAAI,WAAW,QAAQ,YAAY,EACpB,QAAQ,cAAc,EACtB,QACA,MACA,QAAQ,IAAI;QACtC;IACF;AACF;AAEF;;;;;CAKC,GACD,WAAW,SAAS,CAAC,GAAG,GAAG,SAAS,eAAe,MAAM;IACvD,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,OAAO,OAAO,CAAC,SAAU,KAAK;YAC5B,IAAI,CAAC,GAAG,CAAC;QACX,GAAG,IAAI;IACT,OACK,IAAI,MAAM,CAAC,aAAa,IAAI,OAAO,WAAW,UAAU;QAC3D,IAAI,QAAQ;YACV,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACrB;IACF,OACK;QACH,MAAM,IAAI,UACR,gFAAgF;IAEpF;IACA,OAAO,IAAI;AACb;AAEA;;;;;CAKC,GACD,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,mBAAmB,MAAM;IAC/D,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,IAAK,IAAI,IAAI,OAAO,MAAM,GAAC,GAAG,KAAK,GAAG,IAAK;YACzC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACxB;IACF,OACK,IAAI,MAAM,CAAC,aAAa,IAAI,OAAO,WAAW,UAAU;QAC3D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;IACxB,OACK;QACH,MAAM,IAAI,UACR,gFAAgF;IAEpF;IACA,OAAO,IAAI;AACb;AAEA;;;;;;CAMC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAS,gBAAgB,GAAG;IACtD,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;QACxD,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;QACxB,IAAI,KAAK,CAAC,aAAa,EAAE;YACvB,MAAM,IAAI,CAAC;QACb,OACK;YACH,IAAI,UAAU,IAAI;gBAChB,IAAI,OAAO;oBAAE,QAAQ,IAAI,CAAC,MAAM;oBACnB,MAAM,IAAI,CAAC,IAAI;oBACf,QAAQ,IAAI,CAAC,MAAM;oBACnB,MAAM,IAAI,CAAC,IAAI;gBAAC;YAC/B;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAS,gBAAgB,IAAI;IACvD,IAAI;IACJ,IAAI;IACJ,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM;IAC9B,IAAI,MAAM,GAAG;QACX,cAAc,EAAE;QAChB,IAAK,IAAI,GAAG,IAAI,MAAI,GAAG,IAAK;YAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACjC,YAAY,IAAI,CAAC;QACnB;QACA,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;QACjC,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA,OAAO,IAAI;AACb;AAEA;;;;;;CAMC,GACD,WAAW,SAAS,CAAC,YAAY,GAAG,SAAS,wBAAwB,QAAQ,EAAE,YAAY;IACzF,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE;IACvD,IAAI,SAAS,CAAC,aAAa,EAAE;QAC3B,UAAU,YAAY,CAAC,UAAU;IACnC,OACK,IAAI,OAAO,cAAc,UAAU;QACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,GAAG,UAAU,OAAO,CAAC,UAAU;IACxE,OACK;QACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,UAAU;IAC1C;IACA,OAAO,IAAI;AACb;AAEA;;;;;;CAMC,GACD,WAAW,SAAS,CAAC,gBAAgB,GACnC,SAAS,4BAA4B,WAAW,EAAE,cAAc;IAC9D,IAAI,CAAC,cAAc,CAAC,KAAK,WAAW,CAAC,aAAa,GAAG;AACvD;AAEF;;;;;CAKC,GACD,WAAW,SAAS,CAAC,kBAAkB,GACrC,SAAS,8BAA8B,GAAG;IACxC,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;QACxD,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,kBAAkB,CAAC;QACtC;IACF;IAEA,IAAI,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc;IAC7C,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,MAAM,EAAE,IAAI,KAAK,IAAK;QAClD,IAAI,KAAK,aAAa,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;IACrE;AACF;AAEF;;;CAGC,GACD,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS;IACvC,IAAI,MAAM;IACV,IAAI,CAAC,IAAI,CAAC,SAAU,KAAK;QACvB,OAAO;IACT;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,WAAW,SAAS,CAAC,qBAAqB,GAAG,SAAS,iCAAiC,KAAK;IAC1F,IAAI,YAAY;QACd,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA,IAAI,MAAM,IAAI,mBAAmB;IACjC,IAAI,sBAAsB;IAC1B,IAAI,qBAAqB;IACzB,IAAI,mBAAmB;IACvB,IAAI,qBAAqB;IACzB,IAAI,mBAAmB;IACvB,IAAI,CAAC,IAAI,CAAC,SAAU,KAAK,EAAE,QAAQ;QACjC,UAAU,IAAI,IAAI;QAClB,IAAI,SAAS,MAAM,KAAK,QACjB,SAAS,IAAI,KAAK,QAClB,SAAS,MAAM,KAAK,MAAM;YAC/B,IAAG,uBAAuB,SAAS,MAAM,IACnC,qBAAqB,SAAS,IAAI,IAClC,uBAAuB,SAAS,MAAM,IACtC,qBAAqB,SAAS,IAAI,EAAE;gBACxC,IAAI,UAAU,CAAC;oBACb,QAAQ,SAAS,MAAM;oBACvB,UAAU;wBACR,MAAM,SAAS,IAAI;wBACnB,QAAQ,SAAS,MAAM;oBACzB;oBACA,WAAW;wBACT,MAAM,UAAU,IAAI;wBACpB,QAAQ,UAAU,MAAM;oBAC1B;oBACA,MAAM,SAAS,IAAI;gBACrB;YACF;YACA,qBAAqB,SAAS,MAAM;YACpC,mBAAmB,SAAS,IAAI;YAChC,qBAAqB,SAAS,MAAM;YACpC,mBAAmB,SAAS,IAAI;YAChC,sBAAsB;QACxB,OAAO,IAAI,qBAAqB;YAC9B,IAAI,UAAU,CAAC;gBACb,WAAW;oBACT,MAAM,UAAU,IAAI;oBACpB,QAAQ,UAAU,MAAM;gBAC1B;YACF;YACA,qBAAqB;YACrB,sBAAsB;QACxB;QACA,IAAK,IAAI,MAAM,GAAG,SAAS,MAAM,MAAM,EAAE,MAAM,QAAQ,MAAO;YAC5D,IAAI,MAAM,UAAU,CAAC,SAAS,cAAc;gBAC1C,UAAU,IAAI;gBACd,UAAU,MAAM,GAAG;gBACnB,sBAAsB;gBACtB,IAAI,MAAM,MAAM,QAAQ;oBACtB,qBAAqB;oBACrB,sBAAsB;gBACxB,OAAO,IAAI,qBAAqB;oBAC9B,IAAI,UAAU,CAAC;wBACb,QAAQ,SAAS,MAAM;wBACvB,UAAU;4BACR,MAAM,SAAS,IAAI;4BACnB,QAAQ,SAAS,MAAM;wBACzB;wBACA,WAAW;4BACT,MAAM,UAAU,IAAI;4BACpB,QAAQ,UAAU,MAAM;wBAC1B;wBACA,MAAM,SAAS,IAAI;oBACrB;gBACF;YACF,OAAO;gBACL,UAAU,MAAM;YAClB;QACF;IACF;IACA,IAAI,CAAC,kBAAkB,CAAC,SAAU,UAAU,EAAE,aAAa;QACzD,IAAI,gBAAgB,CAAC,YAAY;IACnC;IAEA,OAAO;QAAE,MAAM,UAAU,IAAI;QAAE,KAAK;IAAI;AAC1C;AAEA,QAAQ,UAAU,GAAG", "ignoreList": [0]}}, {"offset": {"line": 4472, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/source-map-js/source-map.js"], "sourcesContent": ["/*\n * Copyright 2009-2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE.txt or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\nexports.SourceMapGenerator = require('./lib/source-map-generator').SourceMapGenerator;\nexports.SourceMapConsumer = require('./lib/source-map-consumer').SourceMapConsumer;\nexports.SourceNode = require('./lib/source-node').SourceNode;\n"], "names": [], "mappings": "AAAA;;;;CAIC,GACD,QAAQ,kBAAkB,GAAG,mHAAsC,kBAAkB;AACrF,QAAQ,iBAAiB,GAAG,kHAAqC,iBAAiB;AAClF,QAAQ,UAAU,GAAG,0GAA6B,UAAU", "ignoreList": [0]}}, {"offset": {"line": 4484, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/previous-map.js"], "sourcesContent": ["'use strict'\n\nlet { existsSync, readFileSync } = require('fs')\nlet { dirname, join } = require('path')\nlet { SourceMapConsumer, SourceMapGenerator } = require('source-map-js')\n\nfunction fromBase64(str) {\n  if (Buffer) {\n    return Buffer.from(str, 'base64').toString()\n  } else {\n    /* c8 ignore next 2 */\n    return window.atob(str)\n  }\n}\n\nclass PreviousMap {\n  constructor(css, opts) {\n    if (opts.map === false) return\n    this.loadAnnotation(css)\n    this.inline = this.startWith(this.annotation, 'data:')\n\n    let prev = opts.map ? opts.map.prev : undefined\n    let text = this.loadMap(opts.from, prev)\n    if (!this.mapFile && opts.from) {\n      this.mapFile = opts.from\n    }\n    if (this.mapFile) this.root = dirname(this.mapFile)\n    if (text) this.text = text\n  }\n\n  consumer() {\n    if (!this.consumerCache) {\n      this.consumerCache = new SourceMapConsumer(this.text)\n    }\n    return this.consumerCache\n  }\n\n  decodeInline(text) {\n    let baseCharsetUri = /^data:application\\/json;charset=utf-?8;base64,/\n    let baseUri = /^data:application\\/json;base64,/\n    let charsetUri = /^data:application\\/json;charset=utf-?8,/\n    let uri = /^data:application\\/json,/\n\n    let uriMatch = text.match(charsetUri) || text.match(uri)\n    if (uriMatch) {\n      return decodeURIComponent(text.substr(uriMatch[0].length))\n    }\n\n    let baseUriMatch = text.match(baseCharsetUri) || text.match(baseUri)\n    if (baseUriMatch) {\n      return fromBase64(text.substr(baseUriMatch[0].length))\n    }\n\n    let encoding = text.match(/data:application\\/json;([^,]+),/)[1]\n    throw new Error('Unsupported source map encoding ' + encoding)\n  }\n\n  getAnnotationURL(sourceMapString) {\n    return sourceMapString.replace(/^\\/\\*\\s*# sourceMappingURL=/, '').trim()\n  }\n\n  isMap(map) {\n    if (typeof map !== 'object') return false\n    return (\n      typeof map.mappings === 'string' ||\n      typeof map._mappings === 'string' ||\n      Array.isArray(map.sections)\n    )\n  }\n\n  loadAnnotation(css) {\n    let comments = css.match(/\\/\\*\\s*# sourceMappingURL=/g)\n    if (!comments) return\n\n    // sourceMappingURLs from comments, strings, etc.\n    let start = css.lastIndexOf(comments.pop())\n    let end = css.indexOf('*/', start)\n\n    if (start > -1 && end > -1) {\n      // Locate the last sourceMappingURL to avoid pickin\n      this.annotation = this.getAnnotationURL(css.substring(start, end))\n    }\n  }\n\n  loadFile(path) {\n    this.root = dirname(path)\n    if (existsSync(path)) {\n      this.mapFile = path\n      return readFileSync(path, 'utf-8').toString().trim()\n    }\n  }\n\n  loadMap(file, prev) {\n    if (prev === false) return false\n\n    if (prev) {\n      if (typeof prev === 'string') {\n        return prev\n      } else if (typeof prev === 'function') {\n        let prevPath = prev(file)\n        if (prevPath) {\n          let map = this.loadFile(prevPath)\n          if (!map) {\n            throw new Error(\n              'Unable to load previous source map: ' + prevPath.toString()\n            )\n          }\n          return map\n        }\n      } else if (prev instanceof SourceMapConsumer) {\n        return SourceMapGenerator.fromSourceMap(prev).toString()\n      } else if (prev instanceof SourceMapGenerator) {\n        return prev.toString()\n      } else if (this.isMap(prev)) {\n        return JSON.stringify(prev)\n      } else {\n        throw new Error(\n          'Unsupported previous source map format: ' + prev.toString()\n        )\n      }\n    } else if (this.inline) {\n      return this.decodeInline(this.annotation)\n    } else if (this.annotation) {\n      let map = this.annotation\n      if (file) map = join(dirname(file), map)\n      return this.loadFile(map)\n    }\n  }\n\n  startWith(string, start) {\n    if (!string) return false\n    return string.substr(0, start.length) === start\n  }\n\n  withContent() {\n    return !!(\n      this.consumer().sourcesContent &&\n      this.consumer().sourcesContent.length > 0\n    )\n  }\n}\n\nmodule.exports = PreviousMap\nPreviousMap.default = PreviousMap\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE;AAChC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;AACrB,IAAI,EAAE,iBAAiB,EAAE,kBAAkB,EAAE;AAE7C,SAAS,WAAW,GAAG;IACrB,wCAAY;QACV,OAAO,OAAO,IAAI,CAAC,KAAK,UAAU,QAAQ;IAC5C,OAAO;;IAGP;AACF;AAEA,MAAM;IACJ,YAAY,GAAG,EAAE,IAAI,CAAE;QACrB,IAAI,KAAK,GAAG,KAAK,OAAO;QACxB,IAAI,CAAC,cAAc,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE;QAE9C,IAAI,OAAO,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG;QACtC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI;QAC1B;QACA,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,OAAO;QAClD,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG;IACxB;IAEA,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,aAAa,GAAG,IAAI,kBAAkB,IAAI,CAAC,IAAI;QACtD;QACA,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,aAAa,IAAI,EAAE;QACjB,IAAI,iBAAiB;QACrB,IAAI,UAAU;QACd,IAAI,aAAa;QACjB,IAAI,MAAM;QAEV,IAAI,WAAW,KAAK,KAAK,CAAC,eAAe,KAAK,KAAK,CAAC;QACpD,IAAI,UAAU;YACZ,OAAO,mBAAmB,KAAK,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM;QAC1D;QAEA,IAAI,eAAe,KAAK,KAAK,CAAC,mBAAmB,KAAK,KAAK,CAAC;QAC5D,IAAI,cAAc;YAChB,OAAO,WAAW,KAAK,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM;QACtD;QAEA,IAAI,WAAW,KAAK,KAAK,CAAC,kCAAkC,CAAC,EAAE;QAC/D,MAAM,IAAI,MAAM,qCAAqC;IACvD;IAEA,iBAAiB,eAAe,EAAE;QAChC,OAAO,gBAAgB,OAAO,CAAC,+BAA+B,IAAI,IAAI;IACxE;IAEA,MAAM,GAAG,EAAE;QACT,IAAI,OAAO,QAAQ,UAAU,OAAO;QACpC,OACE,OAAO,IAAI,QAAQ,KAAK,YACxB,OAAO,IAAI,SAAS,KAAK,YACzB,MAAM,OAAO,CAAC,IAAI,QAAQ;IAE9B;IAEA,eAAe,GAAG,EAAE;QAClB,IAAI,WAAW,IAAI,KAAK,CAAC;QACzB,IAAI,CAAC,UAAU;QAEf,iDAAiD;QACjD,IAAI,QAAQ,IAAI,WAAW,CAAC,SAAS,GAAG;QACxC,IAAI,MAAM,IAAI,OAAO,CAAC,MAAM;QAE5B,IAAI,QAAQ,CAAC,KAAK,MAAM,CAAC,GAAG;YAC1B,mDAAmD;YACnD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,SAAS,CAAC,OAAO;QAC/D;IACF;IAEA,SAAS,IAAI,EAAE;QACb,IAAI,CAAC,IAAI,GAAG,QAAQ;QACpB,IAAI,WAAW,OAAO;YACpB,IAAI,CAAC,OAAO,GAAG;YACf,OAAO,aAAa,MAAM,SAAS,QAAQ,GAAG,IAAI;QACpD;IACF;IAEA,QAAQ,IAAI,EAAE,IAAI,EAAE;QAClB,IAAI,SAAS,OAAO,OAAO;QAE3B,IAAI,MAAM;YACR,IAAI,OAAO,SAAS,UAAU;gBAC5B,OAAO;YACT,OAAO,IAAI,OAAO,SAAS,YAAY;gBACrC,IAAI,WAAW,KAAK;gBACpB,IAAI,UAAU;oBACZ,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC;oBACxB,IAAI,CAAC,KAAK;wBACR,MAAM,IAAI,MACR,yCAAyC,SAAS,QAAQ;oBAE9D;oBACA,OAAO;gBACT;YACF,OAAO,IAAI,gBAAgB,mBAAmB;gBAC5C,OAAO,mBAAmB,aAAa,CAAC,MAAM,QAAQ;YACxD,OAAO,IAAI,gBAAgB,oBAAoB;gBAC7C,OAAO,KAAK,QAAQ;YACtB,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;gBAC3B,OAAO,KAAK,SAAS,CAAC;YACxB,OAAO;gBACL,MAAM,IAAI,MACR,6CAA6C,KAAK,QAAQ;YAE9D;QACF,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE;YACtB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU;QAC1C,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;YAC1B,IAAI,MAAM,IAAI,CAAC,UAAU;YACzB,IAAI,MAAM,MAAM,KAAK,QAAQ,OAAO;YACpC,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB;IACF;IAEA,UAAU,MAAM,EAAE,KAAK,EAAE;QACvB,IAAI,CAAC,QAAQ,OAAO;QACpB,OAAO,OAAO,MAAM,CAAC,GAAG,MAAM,MAAM,MAAM;IAC5C;IAEA,cAAc;QACZ,OAAO,CAAC,CAAC,CACP,IAAI,CAAC,QAAQ,GAAG,cAAc,IAC9B,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,MAAM,GAAG,CAC1C;IACF;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,YAAY,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 4601, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/input.js"], "sourcesContent": ["'use strict'\n\nlet { nanoid } = require('nanoid/non-secure')\nlet { isAbsolute, resolve } = require('path')\nlet { SourceMapConsumer, SourceMapGenerator } = require('source-map-js')\nlet { fileURLToPath, pathToFileURL } = require('url')\n\nlet CssSyntaxError = require('./css-syntax-error')\nlet PreviousMap = require('./previous-map')\nlet terminalHighlight = require('./terminal-highlight')\n\nlet lineToIndexCache = Symbol('lineToIndexCache')\n\nlet sourceMapAvailable = Boolean(SourceMapConsumer && SourceMapGenerator)\nlet pathAvailable = Boolean(resolve && isAbsolute)\n\nfunction getLineToIndex(input) {\n  if (input[lineToIndexCache]) return input[lineToIndexCache]\n  let lines = input.css.split('\\n')\n  let lineToIndex = new Array(lines.length)\n  let prevIndex = 0\n\n  for (let i = 0, l = lines.length; i < l; i++) {\n    lineToIndex[i] = prevIndex\n    prevIndex += lines[i].length + 1\n  }\n\n  input[lineToIndexCache] = lineToIndex\n  return lineToIndex\n}\n\nclass Input {\n  get from() {\n    return this.file || this.id\n  }\n\n  constructor(css, opts = {}) {\n    if (\n      css === null ||\n      typeof css === 'undefined' ||\n      (typeof css === 'object' && !css.toString)\n    ) {\n      throw new Error(`PostCSS received ${css} instead of CSS string`)\n    }\n\n    this.css = css.toString()\n\n    if (this.css[0] === '\\uFEFF' || this.css[0] === '\\uFFFE') {\n      this.hasBOM = true\n      this.css = this.css.slice(1)\n    } else {\n      this.hasBOM = false\n    }\n\n    this.document = this.css\n    if (opts.document) this.document = opts.document.toString()\n\n    if (opts.from) {\n      if (\n        !pathAvailable ||\n        /^\\w+:\\/\\//.test(opts.from) ||\n        isAbsolute(opts.from)\n      ) {\n        this.file = opts.from\n      } else {\n        this.file = resolve(opts.from)\n      }\n    }\n\n    if (pathAvailable && sourceMapAvailable) {\n      let map = new PreviousMap(this.css, opts)\n      if (map.text) {\n        this.map = map\n        let file = map.consumer().file\n        if (!this.file && file) this.file = this.mapResolve(file)\n      }\n    }\n\n    if (!this.file) {\n      this.id = '<input css ' + nanoid(6) + '>'\n    }\n    if (this.map) this.map.file = this.from\n  }\n\n  error(message, line, column, opts = {}) {\n    let endColumn, endLine, endOffset, offset, result\n\n    if (line && typeof line === 'object') {\n      let start = line\n      let end = column\n      if (typeof start.offset === 'number') {\n        offset = start.offset\n        let pos = this.fromOffset(offset)\n        line = pos.line\n        column = pos.col\n      } else {\n        line = start.line\n        column = start.column\n        offset = this.fromLineAndColumn(line, column)\n      }\n      if (typeof end.offset === 'number') {\n        endOffset = end.offset\n        let pos = this.fromOffset(endOffset)\n        endLine = pos.line\n        endColumn = pos.col\n      } else {\n        endLine = end.line\n        endColumn = end.column\n        endOffset = this.fromLineAndColumn(end.line, end.column)\n      }\n    } else if (!column) {\n      offset = line\n      let pos = this.fromOffset(offset)\n      line = pos.line\n      column = pos.col\n    } else {\n      offset = this.fromLineAndColumn(line, column)\n    }\n\n    let origin = this.origin(line, column, endLine, endColumn)\n    if (origin) {\n      result = new CssSyntaxError(\n        message,\n        origin.endLine === undefined\n          ? origin.line\n          : { column: origin.column, line: origin.line },\n        origin.endLine === undefined\n          ? origin.column\n          : { column: origin.endColumn, line: origin.endLine },\n        origin.source,\n        origin.file,\n        opts.plugin\n      )\n    } else {\n      result = new CssSyntaxError(\n        message,\n        endLine === undefined ? line : { column, line },\n        endLine === undefined ? column : { column: endColumn, line: endLine },\n        this.css,\n        this.file,\n        opts.plugin\n      )\n    }\n\n    result.input = { column, endColumn, endLine, endOffset, line, offset, source: this.css }\n    if (this.file) {\n      if (pathToFileURL) {\n        result.input.url = pathToFileURL(this.file).toString()\n      }\n      result.input.file = this.file\n    }\n\n    return result\n  }\n\n  fromLineAndColumn(line, column) {\n    let lineToIndex = getLineToIndex(this)\n    let index = lineToIndex[line - 1]\n    return index + column - 1\n  }\n\n  fromOffset(offset) {\n    let lineToIndex = getLineToIndex(this)\n    let lastLine = lineToIndex[lineToIndex.length - 1]\n\n    let min = 0\n    if (offset >= lastLine) {\n      min = lineToIndex.length - 1\n    } else {\n      let max = lineToIndex.length - 2\n      let mid\n      while (min < max) {\n        mid = min + ((max - min) >> 1)\n        if (offset < lineToIndex[mid]) {\n          max = mid - 1\n        } else if (offset >= lineToIndex[mid + 1]) {\n          min = mid + 1\n        } else {\n          min = mid\n          break\n        }\n      }\n    }\n    return {\n      col: offset - lineToIndex[min] + 1,\n      line: min + 1\n    }\n  }\n\n  mapResolve(file) {\n    if (/^\\w+:\\/\\//.test(file)) {\n      return file\n    }\n    return resolve(this.map.consumer().sourceRoot || this.map.root || '.', file)\n  }\n\n  origin(line, column, endLine, endColumn) {\n    if (!this.map) return false\n    let consumer = this.map.consumer()\n\n    let from = consumer.originalPositionFor({ column, line })\n    if (!from.source) return false\n\n    let to\n    if (typeof endLine === 'number') {\n      to = consumer.originalPositionFor({ column: endColumn, line: endLine })\n    }\n\n    let fromUrl\n\n    if (isAbsolute(from.source)) {\n      fromUrl = pathToFileURL(from.source)\n    } else {\n      fromUrl = new URL(\n        from.source,\n        this.map.consumer().sourceRoot || pathToFileURL(this.map.mapFile)\n      )\n    }\n\n    let result = {\n      column: from.column,\n      endColumn: to && to.column,\n      endLine: to && to.line,\n      line: from.line,\n      url: fromUrl.toString()\n    }\n\n    if (fromUrl.protocol === 'file:') {\n      if (fileURLToPath) {\n        result.file = fileURLToPath(fromUrl)\n      } else {\n        /* c8 ignore next 2 */\n        throw new Error(`file: protocol is not available in this PostCSS build`)\n      }\n    }\n\n    let source = consumer.sourceContentFor(from.source)\n    if (source) result.source = source\n\n    return result\n  }\n\n  toJSON() {\n    let json = {}\n    for (let name of ['hasBOM', 'css', 'file', 'id']) {\n      if (this[name] != null) {\n        json[name] = this[name]\n      }\n    }\n    if (this.map) {\n      json.map = { ...this.map }\n      if (json.map.consumerCache) {\n        json.map.consumerCache = undefined\n      }\n    }\n    return json\n  }\n}\n\nmodule.exports = Input\nInput.default = Input\n\nif (terminalHighlight && terminalHighlight.registerInput) {\n  terminalHighlight.registerInput(Input)\n}\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,EAAE,MAAM,EAAE;AACd,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE;AAC3B,IAAI,EAAE,iBAAiB,EAAE,kBAAkB,EAAE;AAC7C,IAAI,EAAE,aAAa,EAAE,aAAa,EAAE;AAEpC,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,mBAAmB,OAAO;AAE9B,IAAI,qBAAqB,QAAQ,qBAAqB;AACtD,IAAI,gBAAgB,QAAQ,WAAW;AAEvC,SAAS,eAAe,KAAK;IAC3B,IAAI,KAAK,CAAC,iBAAiB,EAAE,OAAO,KAAK,CAAC,iBAAiB;IAC3D,IAAI,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC;IAC5B,IAAI,cAAc,IAAI,MAAM,MAAM,MAAM;IACxC,IAAI,YAAY;IAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAK;QAC5C,WAAW,CAAC,EAAE,GAAG;QACjB,aAAa,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG;IACjC;IAEA,KAAK,CAAC,iBAAiB,GAAG;IAC1B,OAAO;AACT;AAEA,MAAM;IACJ,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE;IAC7B;IAEA,YAAY,GAAG,EAAE,OAAO,CAAC,CAAC,CAAE;QAC1B,IACE,QAAQ,QACR,OAAO,QAAQ,eACd,OAAO,QAAQ,YAAY,CAAC,IAAI,QAAQ,EACzC;YACA,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,IAAI,sBAAsB,CAAC;QACjE;QAEA,IAAI,CAAC,GAAG,GAAG,IAAI,QAAQ;QAEvB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,UAAU;YACxD,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QAC5B,OAAO;YACL,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG;QACxB,IAAI,KAAK,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ,CAAC,QAAQ;QAEzD,IAAI,KAAK,IAAI,EAAE;YACb,IACE,CAAC,iBACD,YAAY,IAAI,CAAC,KAAK,IAAI,KAC1B,WAAW,KAAK,IAAI,GACpB;gBACA,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;YACvB,OAAO;gBACL,IAAI,CAAC,IAAI,GAAG,QAAQ,KAAK,IAAI;YAC/B;QACF;QAEA,IAAI,iBAAiB,oBAAoB;YACvC,IAAI,MAAM,IAAI,YAAY,IAAI,CAAC,GAAG,EAAE;YACpC,IAAI,IAAI,IAAI,EAAE;gBACZ,IAAI,CAAC,GAAG,GAAG;gBACX,IAAI,OAAO,IAAI,QAAQ,GAAG,IAAI;gBAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;YACtD;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,CAAC,EAAE,GAAG,gBAAgB,OAAO,KAAK;QACxC;QACA,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;IACzC;IAEA,MAAM,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE;QACtC,IAAI,WAAW,SAAS,WAAW,QAAQ;QAE3C,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,QAAQ;YACZ,IAAI,MAAM;YACV,IAAI,OAAO,MAAM,MAAM,KAAK,UAAU;gBACpC,SAAS,MAAM,MAAM;gBACrB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC;gBAC1B,OAAO,IAAI,IAAI;gBACf,SAAS,IAAI,GAAG;YAClB,OAAO;gBACL,OAAO,MAAM,IAAI;gBACjB,SAAS,MAAM,MAAM;gBACrB,SAAS,IAAI,CAAC,iBAAiB,CAAC,MAAM;YACxC;YACA,IAAI,OAAO,IAAI,MAAM,KAAK,UAAU;gBAClC,YAAY,IAAI,MAAM;gBACtB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC;gBAC1B,UAAU,IAAI,IAAI;gBAClB,YAAY,IAAI,GAAG;YACrB,OAAO;gBACL,UAAU,IAAI,IAAI;gBAClB,YAAY,IAAI,MAAM;gBACtB,YAAY,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,EAAE,IAAI,MAAM;YACzD;QACF,OAAO,IAAI,CAAC,QAAQ;YAClB,SAAS;YACT,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC;YAC1B,OAAO,IAAI,IAAI;YACf,SAAS,IAAI,GAAG;QAClB,OAAO;YACL,SAAS,IAAI,CAAC,iBAAiB,CAAC,MAAM;QACxC;QAEA,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,QAAQ,SAAS;QAChD,IAAI,QAAQ;YACV,SAAS,IAAI,eACX,SACA,OAAO,OAAO,KAAK,YACf,OAAO,IAAI,GACX;gBAAE,QAAQ,OAAO,MAAM;gBAAE,MAAM,OAAO,IAAI;YAAC,GAC/C,OAAO,OAAO,KAAK,YACf,OAAO,MAAM,GACb;gBAAE,QAAQ,OAAO,SAAS;gBAAE,MAAM,OAAO,OAAO;YAAC,GACrD,OAAO,MAAM,EACb,OAAO,IAAI,EACX,KAAK,MAAM;QAEf,OAAO;YACL,SAAS,IAAI,eACX,SACA,YAAY,YAAY,OAAO;gBAAE;gBAAQ;YAAK,GAC9C,YAAY,YAAY,SAAS;gBAAE,QAAQ;gBAAW,MAAM;YAAQ,GACpE,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,IAAI,EACT,KAAK,MAAM;QAEf;QAEA,OAAO,KAAK,GAAG;YAAE;YAAQ;YAAW;YAAS;YAAW;YAAM;YAAQ,QAAQ,IAAI,CAAC,GAAG;QAAC;QACvF,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,eAAe;gBACjB,OAAO,KAAK,CAAC,GAAG,GAAG,cAAc,IAAI,CAAC,IAAI,EAAE,QAAQ;YACtD;YACA,OAAO,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QAC/B;QAEA,OAAO;IACT;IAEA,kBAAkB,IAAI,EAAE,MAAM,EAAE;QAC9B,IAAI,cAAc,eAAe,IAAI;QACrC,IAAI,QAAQ,WAAW,CAAC,OAAO,EAAE;QACjC,OAAO,QAAQ,SAAS;IAC1B;IAEA,WAAW,MAAM,EAAE;QACjB,IAAI,cAAc,eAAe,IAAI;QACrC,IAAI,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;QAElD,IAAI,MAAM;QACV,IAAI,UAAU,UAAU;YACtB,MAAM,YAAY,MAAM,GAAG;QAC7B,OAAO;YACL,IAAI,MAAM,YAAY,MAAM,GAAG;YAC/B,IAAI;YACJ,MAAO,MAAM,IAAK;gBAChB,MAAM,MAAM,CAAC,AAAC,MAAM,OAAQ,CAAC;gBAC7B,IAAI,SAAS,WAAW,CAAC,IAAI,EAAE;oBAC7B,MAAM,MAAM;gBACd,OAAO,IAAI,UAAU,WAAW,CAAC,MAAM,EAAE,EAAE;oBACzC,MAAM,MAAM;gBACd,OAAO;oBACL,MAAM;oBACN;gBACF;YACF;QACF;QACA,OAAO;YACL,KAAK,SAAS,WAAW,CAAC,IAAI,GAAG;YACjC,MAAM,MAAM;QACd;IACF;IAEA,WAAW,IAAI,EAAE;QACf,IAAI,YAAY,IAAI,CAAC,OAAO;YAC1B,OAAO;QACT;QACA,OAAO,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK;IACzE;IAEA,OAAO,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE;QACvC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO;QACtB,IAAI,WAAW,IAAI,CAAC,GAAG,CAAC,QAAQ;QAEhC,IAAI,OAAO,SAAS,mBAAmB,CAAC;YAAE;YAAQ;QAAK;QACvD,IAAI,CAAC,KAAK,MAAM,EAAE,OAAO;QAEzB,IAAI;QACJ,IAAI,OAAO,YAAY,UAAU;YAC/B,KAAK,SAAS,mBAAmB,CAAC;gBAAE,QAAQ;gBAAW,MAAM;YAAQ;QACvE;QAEA,IAAI;QAEJ,IAAI,WAAW,KAAK,MAAM,GAAG;YAC3B,UAAU,cAAc,KAAK,MAAM;QACrC,OAAO;YACL,UAAU,IAAI,IACZ,KAAK,MAAM,EACX,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,UAAU,IAAI,cAAc,IAAI,CAAC,GAAG,CAAC,OAAO;QAEpE;QAEA,IAAI,SAAS;YACX,QAAQ,KAAK,MAAM;YACnB,WAAW,MAAM,GAAG,MAAM;YAC1B,SAAS,MAAM,GAAG,IAAI;YACtB,MAAM,KAAK,IAAI;YACf,KAAK,QAAQ,QAAQ;QACvB;QAEA,IAAI,QAAQ,QAAQ,KAAK,SAAS;YAChC,IAAI,eAAe;gBACjB,OAAO,IAAI,GAAG,cAAc;YAC9B,OAAO;gBACL,oBAAoB,GACpB,MAAM,IAAI,MAAM,CAAC,qDAAqD,CAAC;YACzE;QACF;QAEA,IAAI,SAAS,SAAS,gBAAgB,CAAC,KAAK,MAAM;QAClD,IAAI,QAAQ,OAAO,MAAM,GAAG;QAE5B,OAAO;IACT;IAEA,SAAS;QACP,IAAI,OAAO,CAAC;QACZ,KAAK,IAAI,QAAQ;YAAC;YAAU;YAAO;YAAQ;SAAK,CAAE;YAChD,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM;gBACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;YACzB;QACF;QACA,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,KAAK,GAAG,GAAG;gBAAE,GAAG,IAAI,CAAC,GAAG;YAAC;YACzB,IAAI,KAAK,GAAG,CAAC,aAAa,EAAE;gBAC1B,KAAK,GAAG,CAAC,aAAa,GAAG;YAC3B;QACF;QACA,OAAO;IACT;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,MAAM,OAAO,GAAG;AAEhB,IAAI,qBAAqB,kBAAkB,aAAa,EAAE;IACxD,kBAAkB,aAAa,CAAC;AAClC", "ignoreList": [0]}}, {"offset": {"line": 4838, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/root.js"], "sourcesContent": ["'use strict'\n\nlet Container = require('./container')\n\nlet LazyR<PERSON>ult, Processor\n\nclass Root extends Container {\n  constructor(defaults) {\n    super(defaults)\n    this.type = 'root'\n    if (!this.nodes) this.nodes = []\n  }\n\n  normalize(child, sample, type) {\n    let nodes = super.normalize(child)\n\n    if (sample) {\n      if (type === 'prepend') {\n        if (this.nodes.length > 1) {\n          sample.raws.before = this.nodes[1].raws.before\n        } else {\n          delete sample.raws.before\n        }\n      } else if (this.first !== sample) {\n        for (let node of nodes) {\n          node.raws.before = sample.raws.before\n        }\n      }\n    }\n\n    return nodes\n  }\n\n  removeChild(child, ignore) {\n    let index = this.index(child)\n\n    if (!ignore && index === 0 && this.nodes.length > 1) {\n      this.nodes[1].raws.before = this.nodes[index].raws.before\n    }\n\n    return super.removeChild(child)\n  }\n\n  toResult(opts = {}) {\n    let lazy = new LazyResult(new Processor(), this, opts)\n    return lazy.stringify()\n  }\n}\n\nRoot.registerLazyResult = dependant => {\n  LazyResult = dependant\n}\n\nRoot.registerProcessor = dependant => {\n  Processor = dependant\n}\n\nmodule.exports = Root\nRoot.default = Root\n\nContainer.registerRoot(Root)\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,IAAI,YAAY;AAEhB,MAAM,aAAa;IACjB,YAAY,QAAQ,CAAE;QACpB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE;IAClC;IAEA,UAAU,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;QAC7B,IAAI,QAAQ,KAAK,CAAC,UAAU;QAE5B,IAAI,QAAQ;YACV,IAAI,SAAS,WAAW;gBACtB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;oBACzB,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM;gBAChD,OAAO;oBACL,OAAO,OAAO,IAAI,CAAC,MAAM;gBAC3B;YACF,OAAO,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ;gBAChC,KAAK,IAAI,QAAQ,MAAO;oBACtB,KAAK,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,MAAM;gBACvC;YACF;QACF;QAEA,OAAO;IACT;IAEA,YAAY,KAAK,EAAE,MAAM,EAAE;QACzB,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC;QAEvB,IAAI,CAAC,UAAU,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YACnD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;QAC3D;QAEA,OAAO,KAAK,CAAC,YAAY;IAC3B;IAEA,SAAS,OAAO,CAAC,CAAC,EAAE;QAClB,IAAI,OAAO,IAAI,WAAW,IAAI,aAAa,IAAI,EAAE;QACjD,OAAO,KAAK,SAAS;IACvB;AACF;AAEA,KAAK,kBAAkB,GAAG,CAAA;IACxB,aAAa;AACf;AAEA,KAAK,iBAAiB,GAAG,CAAA;IACvB,YAAY;AACd;AAEA,OAAO,OAAO,GAAG;AACjB,KAAK,OAAO,GAAG;AAEf,UAAU,YAAY,CAAC", "ignoreList": [0]}}, {"offset": {"line": 4890, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/list.js"], "sourcesContent": ["'use strict'\n\nlet list = {\n  comma(string) {\n    return list.split(string, [','], true)\n  },\n\n  space(string) {\n    let spaces = [' ', '\\n', '\\t']\n    return list.split(string, spaces)\n  },\n\n  split(string, separators, last) {\n    let array = []\n    let current = ''\n    let split = false\n\n    let func = 0\n    let inQuote = false\n    let prevQuote = ''\n    let escape = false\n\n    for (let letter of string) {\n      if (escape) {\n        escape = false\n      } else if (letter === '\\\\') {\n        escape = true\n      } else if (inQuote) {\n        if (letter === prevQuote) {\n          inQuote = false\n        }\n      } else if (letter === '\"' || letter === \"'\") {\n        inQuote = true\n        prevQuote = letter\n      } else if (letter === '(') {\n        func += 1\n      } else if (letter === ')') {\n        if (func > 0) func -= 1\n      } else if (func === 0) {\n        if (separators.includes(letter)) split = true\n      }\n\n      if (split) {\n        if (current !== '') array.push(current.trim())\n        current = ''\n        split = false\n      } else {\n        current += letter\n      }\n    }\n\n    if (last || current !== '') array.push(current.trim())\n    return array\n  }\n}\n\nmodule.exports = list\nlist.default = list\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,OAAO;IACT,OAAM,MAAM;QACV,OAAO,KAAK,KAAK,CAAC,QAAQ;YAAC;SAAI,EAAE;IACnC;IAEA,OAAM,MAAM;QACV,IAAI,SAAS;YAAC;YAAK;YAAM;SAAK;QAC9B,OAAO,KAAK,KAAK,CAAC,QAAQ;IAC5B;IAEA,OAAM,MAAM,EAAE,UAAU,EAAE,IAAI;QAC5B,IAAI,QAAQ,EAAE;QACd,IAAI,UAAU;QACd,IAAI,QAAQ;QAEZ,IAAI,OAAO;QACX,IAAI,UAAU;QACd,IAAI,YAAY;QAChB,IAAI,SAAS;QAEb,KAAK,IAAI,UAAU,OAAQ;YACzB,IAAI,QAAQ;gBACV,SAAS;YACX,OAAO,IAAI,WAAW,MAAM;gBAC1B,SAAS;YACX,OAAO,IAAI,SAAS;gBAClB,IAAI,WAAW,WAAW;oBACxB,UAAU;gBACZ;YACF,OAAO,IAAI,WAAW,OAAO,WAAW,KAAK;gBAC3C,UAAU;gBACV,YAAY;YACd,OAAO,IAAI,WAAW,KAAK;gBACzB,QAAQ;YACV,OAAO,IAAI,WAAW,KAAK;gBACzB,IAAI,OAAO,GAAG,QAAQ;YACxB,OAAO,IAAI,SAAS,GAAG;gBACrB,IAAI,WAAW,QAAQ,CAAC,SAAS,QAAQ;YAC3C;YAEA,IAAI,OAAO;gBACT,IAAI,YAAY,IAAI,MAAM,IAAI,CAAC,QAAQ,IAAI;gBAC3C,UAAU;gBACV,QAAQ;YACV,OAAO;gBACL,WAAW;YACb;QACF;QAEA,IAAI,QAAQ,YAAY,IAAI,MAAM,IAAI,CAAC,QAAQ,IAAI;QACnD,OAAO;IACT;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,KAAK,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 4951, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/rule.js"], "sourcesContent": ["'use strict'\n\nlet Container = require('./container')\nlet list = require('./list')\n\nclass Rule extends Container {\n  get selectors() {\n    return list.comma(this.selector)\n  }\n\n  set selectors(values) {\n    let match = this.selector ? this.selector.match(/,\\s*/) : null\n    let sep = match ? match[0] : ',' + this.raw('between', 'beforeOpen')\n    this.selector = values.join(sep)\n  }\n\n  constructor(defaults) {\n    super(defaults)\n    this.type = 'rule'\n    if (!this.nodes) this.nodes = []\n  }\n}\n\nmodule.exports = Rule\nRule.default = Rule\n\nContainer.registerRule(Rule)\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AAEJ,MAAM,aAAa;IACjB,IAAI,YAAY;QACd,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ;IACjC;IAEA,IAAI,UAAU,MAAM,EAAE;QACpB,IAAI,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU;QAC1D,IAAI,MAAM,QAAQ,KAAK,CAAC,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW;QACvD,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,CAAC;IAC9B;IAEA,YAAY,QAAQ,CAAE;QACpB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE;IAClC;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,KAAK,OAAO,GAAG;AAEf,UAAU,YAAY,CAAC", "ignoreList": [0]}}, {"offset": {"line": 4977, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/fromJSON.js"], "sourcesContent": ["'use strict'\n\nlet AtRule = require('./at-rule')\nlet Comment = require('./comment')\nlet Declaration = require('./declaration')\nlet Input = require('./input')\nlet PreviousMap = require('./previous-map')\nlet Root = require('./root')\nlet Rule = require('./rule')\n\nfunction fromJSON(json, inputs) {\n  if (Array.isArray(json)) return json.map(n => fromJSON(n))\n\n  let { inputs: ownInputs, ...defaults } = json\n  if (ownInputs) {\n    inputs = []\n    for (let input of ownInputs) {\n      let inputHydrated = { ...input, __proto__: Input.prototype }\n      if (inputHydrated.map) {\n        inputHydrated.map = {\n          ...inputHydrated.map,\n          __proto__: PreviousMap.prototype\n        }\n      }\n      inputs.push(inputHydrated)\n    }\n  }\n  if (defaults.nodes) {\n    defaults.nodes = json.nodes.map(n => fromJSON(n, inputs))\n  }\n  if (defaults.source) {\n    let { inputId, ...source } = defaults.source\n    defaults.source = source\n    if (inputId != null) {\n      defaults.source.input = inputs[inputId]\n    }\n  }\n  if (defaults.type === 'root') {\n    return new Root(defaults)\n  } else if (defaults.type === 'decl') {\n    return new Declaration(defaults)\n  } else if (defaults.type === 'rule') {\n    return new Rule(defaults)\n  } else if (defaults.type === 'comment') {\n    return new Comment(defaults)\n  } else if (defaults.type === 'atrule') {\n    return new AtRule(defaults)\n  } else {\n    throw new Error('Unknown node type: ' + json.type)\n  }\n}\n\nmodule.exports = fromJSON\nfromJSON.default = fromJSON\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,SAAS,IAAI,EAAE,MAAM;IAC5B,IAAI,MAAM,OAAO,CAAC,OAAO,OAAO,KAAK,GAAG,CAAC,CAAA,IAAK,SAAS;IAEvD,IAAI,EAAE,QAAQ,SAAS,EAAE,GAAG,UAAU,GAAG;IACzC,IAAI,WAAW;QACb,SAAS,EAAE;QACX,KAAK,IAAI,SAAS,UAAW;YAC3B,IAAI,gBAAgB;gBAAE,GAAG,KAAK;gBAAE,WAAW,MAAM,SAAS;YAAC;YAC3D,IAAI,cAAc,GAAG,EAAE;gBACrB,cAAc,GAAG,GAAG;oBAClB,GAAG,cAAc,GAAG;oBACpB,WAAW,YAAY,SAAS;gBAClC;YACF;YACA,OAAO,IAAI,CAAC;QACd;IACF;IACA,IAAI,SAAS,KAAK,EAAE;QAClB,SAAS,KAAK,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,IAAK,SAAS,GAAG;IACnD;IACA,IAAI,SAAS,MAAM,EAAE;QACnB,IAAI,EAAE,OAAO,EAAE,GAAG,QAAQ,GAAG,SAAS,MAAM;QAC5C,SAAS,MAAM,GAAG;QAClB,IAAI,WAAW,MAAM;YACnB,SAAS,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ;QACzC;IACF;IACA,IAAI,SAAS,IAAI,KAAK,QAAQ;QAC5B,OAAO,IAAI,KAAK;IAClB,OAAO,IAAI,SAAS,IAAI,KAAK,QAAQ;QACnC,OAAO,IAAI,YAAY;IACzB,OAAO,IAAI,SAAS,IAAI,KAAK,QAAQ;QACnC,OAAO,IAAI,KAAK;IAClB,OAAO,IAAI,SAAS,IAAI,KAAK,WAAW;QACtC,OAAO,IAAI,QAAQ;IACrB,OAAO,IAAI,SAAS,IAAI,KAAK,UAAU;QACrC,OAAO,IAAI,OAAO;IACpB,OAAO;QACL,MAAM,IAAI,MAAM,wBAAwB,KAAK,IAAI;IACnD;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,SAAS,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 5035, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/map-generator.js"], "sourcesContent": ["'use strict'\n\nlet { dirname, relative, resolve, sep } = require('path')\nlet { SourceMapConsumer, SourceMapGenerator } = require('source-map-js')\nlet { pathToFileURL } = require('url')\n\nlet Input = require('./input')\n\nlet sourceMapAvailable = Boolean(SourceMapConsumer && SourceMapGenerator)\nlet pathAvailable = Boolean(dirname && resolve && relative && sep)\n\nclass MapGenerator {\n  constructor(stringify, root, opts, cssString) {\n    this.stringify = stringify\n    this.mapOpts = opts.map || {}\n    this.root = root\n    this.opts = opts\n    this.css = cssString\n    this.originalCSS = cssString\n    this.usesFileUrls = !this.mapOpts.from && this.mapOpts.absolute\n\n    this.memoizedFileURLs = new Map()\n    this.memoizedPaths = new Map()\n    this.memoizedURLs = new Map()\n  }\n\n  addAnnotation() {\n    let content\n\n    if (this.isInline()) {\n      content =\n        'data:application/json;base64,' + this.toBase64(this.map.toString())\n    } else if (typeof this.mapOpts.annotation === 'string') {\n      content = this.mapOpts.annotation\n    } else if (typeof this.mapOpts.annotation === 'function') {\n      content = this.mapOpts.annotation(this.opts.to, this.root)\n    } else {\n      content = this.outputFile() + '.map'\n    }\n    let eol = '\\n'\n    if (this.css.includes('\\r\\n')) eol = '\\r\\n'\n\n    this.css += eol + '/*# sourceMappingURL=' + content + ' */'\n  }\n\n  applyPrevMaps() {\n    for (let prev of this.previous()) {\n      let from = this.toUrl(this.path(prev.file))\n      let root = prev.root || dirname(prev.file)\n      let map\n\n      if (this.mapOpts.sourcesContent === false) {\n        map = new SourceMapConsumer(prev.text)\n        if (map.sourcesContent) {\n          map.sourcesContent = null\n        }\n      } else {\n        map = prev.consumer()\n      }\n\n      this.map.applySourceMap(map, from, this.toUrl(this.path(root)))\n    }\n  }\n\n  clearAnnotation() {\n    if (this.mapOpts.annotation === false) return\n\n    if (this.root) {\n      let node\n      for (let i = this.root.nodes.length - 1; i >= 0; i--) {\n        node = this.root.nodes[i]\n        if (node.type !== 'comment') continue\n        if (node.text.startsWith('# sourceMappingURL=')) {\n          this.root.removeChild(i)\n        }\n      }\n    } else if (this.css) {\n      this.css = this.css.replace(/\\n*\\/\\*#[\\S\\s]*?\\*\\/$/gm, '')\n    }\n  }\n\n  generate() {\n    this.clearAnnotation()\n    if (pathAvailable && sourceMapAvailable && this.isMap()) {\n      return this.generateMap()\n    } else {\n      let result = ''\n      this.stringify(this.root, i => {\n        result += i\n      })\n      return [result]\n    }\n  }\n\n  generateMap() {\n    if (this.root) {\n      this.generateString()\n    } else if (this.previous().length === 1) {\n      let prev = this.previous()[0].consumer()\n      prev.file = this.outputFile()\n      this.map = SourceMapGenerator.fromSourceMap(prev, {\n        ignoreInvalidMapping: true\n      })\n    } else {\n      this.map = new SourceMapGenerator({\n        file: this.outputFile(),\n        ignoreInvalidMapping: true\n      })\n      this.map.addMapping({\n        generated: { column: 0, line: 1 },\n        original: { column: 0, line: 1 },\n        source: this.opts.from\n          ? this.toUrl(this.path(this.opts.from))\n          : '<no source>'\n      })\n    }\n\n    if (this.isSourcesContent()) this.setSourcesContent()\n    if (this.root && this.previous().length > 0) this.applyPrevMaps()\n    if (this.isAnnotation()) this.addAnnotation()\n\n    if (this.isInline()) {\n      return [this.css]\n    } else {\n      return [this.css, this.map]\n    }\n  }\n\n  generateString() {\n    this.css = ''\n    this.map = new SourceMapGenerator({\n      file: this.outputFile(),\n      ignoreInvalidMapping: true\n    })\n\n    let line = 1\n    let column = 1\n\n    let noSource = '<no source>'\n    let mapping = {\n      generated: { column: 0, line: 0 },\n      original: { column: 0, line: 0 },\n      source: ''\n    }\n\n    let last, lines\n    this.stringify(this.root, (str, node, type) => {\n      this.css += str\n\n      if (node && type !== 'end') {\n        mapping.generated.line = line\n        mapping.generated.column = column - 1\n        if (node.source && node.source.start) {\n          mapping.source = this.sourcePath(node)\n          mapping.original.line = node.source.start.line\n          mapping.original.column = node.source.start.column - 1\n          this.map.addMapping(mapping)\n        } else {\n          mapping.source = noSource\n          mapping.original.line = 1\n          mapping.original.column = 0\n          this.map.addMapping(mapping)\n        }\n      }\n\n      lines = str.match(/\\n/g)\n      if (lines) {\n        line += lines.length\n        last = str.lastIndexOf('\\n')\n        column = str.length - last\n      } else {\n        column += str.length\n      }\n\n      if (node && type !== 'start') {\n        let p = node.parent || { raws: {} }\n        let childless =\n          node.type === 'decl' || (node.type === 'atrule' && !node.nodes)\n        if (!childless || node !== p.last || p.raws.semicolon) {\n          if (node.source && node.source.end) {\n            mapping.source = this.sourcePath(node)\n            mapping.original.line = node.source.end.line\n            mapping.original.column = node.source.end.column - 1\n            mapping.generated.line = line\n            mapping.generated.column = column - 2\n            this.map.addMapping(mapping)\n          } else {\n            mapping.source = noSource\n            mapping.original.line = 1\n            mapping.original.column = 0\n            mapping.generated.line = line\n            mapping.generated.column = column - 1\n            this.map.addMapping(mapping)\n          }\n        }\n      }\n    })\n  }\n\n  isAnnotation() {\n    if (this.isInline()) {\n      return true\n    }\n    if (typeof this.mapOpts.annotation !== 'undefined') {\n      return this.mapOpts.annotation\n    }\n    if (this.previous().length) {\n      return this.previous().some(i => i.annotation)\n    }\n    return true\n  }\n\n  isInline() {\n    if (typeof this.mapOpts.inline !== 'undefined') {\n      return this.mapOpts.inline\n    }\n\n    let annotation = this.mapOpts.annotation\n    if (typeof annotation !== 'undefined' && annotation !== true) {\n      return false\n    }\n\n    if (this.previous().length) {\n      return this.previous().some(i => i.inline)\n    }\n    return true\n  }\n\n  isMap() {\n    if (typeof this.opts.map !== 'undefined') {\n      return !!this.opts.map\n    }\n    return this.previous().length > 0\n  }\n\n  isSourcesContent() {\n    if (typeof this.mapOpts.sourcesContent !== 'undefined') {\n      return this.mapOpts.sourcesContent\n    }\n    if (this.previous().length) {\n      return this.previous().some(i => i.withContent())\n    }\n    return true\n  }\n\n  outputFile() {\n    if (this.opts.to) {\n      return this.path(this.opts.to)\n    } else if (this.opts.from) {\n      return this.path(this.opts.from)\n    } else {\n      return 'to.css'\n    }\n  }\n\n  path(file) {\n    if (this.mapOpts.absolute) return file\n    if (file.charCodeAt(0) === 60 /* `<` */) return file\n    if (/^\\w+:\\/\\//.test(file)) return file\n    let cached = this.memoizedPaths.get(file)\n    if (cached) return cached\n\n    let from = this.opts.to ? dirname(this.opts.to) : '.'\n\n    if (typeof this.mapOpts.annotation === 'string') {\n      from = dirname(resolve(from, this.mapOpts.annotation))\n    }\n\n    let path = relative(from, file)\n    this.memoizedPaths.set(file, path)\n\n    return path\n  }\n\n  previous() {\n    if (!this.previousMaps) {\n      this.previousMaps = []\n      if (this.root) {\n        this.root.walk(node => {\n          if (node.source && node.source.input.map) {\n            let map = node.source.input.map\n            if (!this.previousMaps.includes(map)) {\n              this.previousMaps.push(map)\n            }\n          }\n        })\n      } else {\n        let input = new Input(this.originalCSS, this.opts)\n        if (input.map) this.previousMaps.push(input.map)\n      }\n    }\n\n    return this.previousMaps\n  }\n\n  setSourcesContent() {\n    let already = {}\n    if (this.root) {\n      this.root.walk(node => {\n        if (node.source) {\n          let from = node.source.input.from\n          if (from && !already[from]) {\n            already[from] = true\n            let fromUrl = this.usesFileUrls\n              ? this.toFileUrl(from)\n              : this.toUrl(this.path(from))\n            this.map.setSourceContent(fromUrl, node.source.input.css)\n          }\n        }\n      })\n    } else if (this.css) {\n      let from = this.opts.from\n        ? this.toUrl(this.path(this.opts.from))\n        : '<no source>'\n      this.map.setSourceContent(from, this.css)\n    }\n  }\n\n  sourcePath(node) {\n    if (this.mapOpts.from) {\n      return this.toUrl(this.mapOpts.from)\n    } else if (this.usesFileUrls) {\n      return this.toFileUrl(node.source.input.from)\n    } else {\n      return this.toUrl(this.path(node.source.input.from))\n    }\n  }\n\n  toBase64(str) {\n    if (Buffer) {\n      return Buffer.from(str).toString('base64')\n    } else {\n      return window.btoa(unescape(encodeURIComponent(str)))\n    }\n  }\n\n  toFileUrl(path) {\n    let cached = this.memoizedFileURLs.get(path)\n    if (cached) return cached\n\n    if (pathToFileURL) {\n      let fileURL = pathToFileURL(path).toString()\n      this.memoizedFileURLs.set(path, fileURL)\n\n      return fileURL\n    } else {\n      throw new Error(\n        '`map.absolute` option is not available in this PostCSS build'\n      )\n    }\n  }\n\n  toUrl(path) {\n    let cached = this.memoizedURLs.get(path)\n    if (cached) return cached\n\n    if (sep === '\\\\') {\n      path = path.replace(/\\\\/g, '/')\n    }\n\n    let url = encodeURI(path).replace(/[#?]/g, encodeURIComponent)\n    this.memoizedURLs.set(path, url)\n\n    return url\n  }\n}\n\nmodule.exports = MapGenerator\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;AACvC,IAAI,EAAE,iBAAiB,EAAE,kBAAkB,EAAE;AAC7C,IAAI,EAAE,aAAa,EAAE;AAErB,IAAI;AAEJ,IAAI,qBAAqB,QAAQ,qBAAqB;AACtD,IAAI,gBAAgB,QAAQ,WAAW,WAAW,YAAY;AAE9D,MAAM;IACJ,YAAY,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAE;QAC5C,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE/D,IAAI,CAAC,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI;IAC1B;IAEA,gBAAgB;QACd,IAAI;QAEJ,IAAI,IAAI,CAAC,QAAQ,IAAI;YACnB,UACE,kCAAkC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ;QACrE,OAAO,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,UAAU;YACtD,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU;QACnC,OAAO,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,YAAY;YACxD,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI;QAC3D,OAAO;YACL,UAAU,IAAI,CAAC,UAAU,KAAK;QAChC;QACA,IAAI,MAAM;QACV,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,MAAM;QAErC,IAAI,CAAC,GAAG,IAAI,MAAM,0BAA0B,UAAU;IACxD;IAEA,gBAAgB;QACd,KAAK,IAAI,QAAQ,IAAI,CAAC,QAAQ,GAAI;YAChC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;YACzC,IAAI,OAAO,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI;YACzC,IAAI;YAEJ,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,OAAO;gBACzC,MAAM,IAAI,kBAAkB,KAAK,IAAI;gBACrC,IAAI,IAAI,cAAc,EAAE;oBACtB,IAAI,cAAc,GAAG;gBACvB;YACF,OAAO;gBACL,MAAM,KAAK,QAAQ;YACrB;YAEA,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1D;IACF;IAEA,kBAAkB;QAChB,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,OAAO;QAEvC,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI;YACJ,IAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;gBACpD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACzB,IAAI,KAAK,IAAI,KAAK,WAAW;gBAC7B,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,wBAAwB;oBAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;gBACxB;YACF;QACF,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE;YACnB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,2BAA2B;QACzD;IACF;IAEA,WAAW;QACT,IAAI,CAAC,eAAe;QACpB,IAAI,iBAAiB,sBAAsB,IAAI,CAAC,KAAK,IAAI;YACvD,OAAO,IAAI,CAAC,WAAW;QACzB,OAAO;YACL,IAAI,SAAS;YACb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;gBACxB,UAAU;YACZ;YACA,OAAO;gBAAC;aAAO;QACjB;IACF;IAEA,cAAc;QACZ,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,cAAc;QACrB,OAAO,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,KAAK,GAAG;YACvC,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,QAAQ;YACtC,KAAK,IAAI,GAAG,IAAI,CAAC,UAAU;YAC3B,IAAI,CAAC,GAAG,GAAG,mBAAmB,aAAa,CAAC,MAAM;gBAChD,sBAAsB;YACxB;QACF,OAAO;YACL,IAAI,CAAC,GAAG,GAAG,IAAI,mBAAmB;gBAChC,MAAM,IAAI,CAAC,UAAU;gBACrB,sBAAsB;YACxB;YACA,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;gBAClB,WAAW;oBAAE,QAAQ;oBAAG,MAAM;gBAAE;gBAChC,UAAU;oBAAE,QAAQ;oBAAG,MAAM;gBAAE;gBAC/B,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,GAClB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KACnC;YACN;QACF;QAEA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB;QACnD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa;QAC/D,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa;QAE3C,IAAI,IAAI,CAAC,QAAQ,IAAI;YACnB,OAAO;gBAAC,IAAI,CAAC,GAAG;aAAC;QACnB,OAAO;YACL,OAAO;gBAAC,IAAI,CAAC,GAAG;gBAAE,IAAI,CAAC,GAAG;aAAC;QAC7B;IACF;IAEA,iBAAiB;QACf,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG,IAAI,mBAAmB;YAChC,MAAM,IAAI,CAAC,UAAU;YACrB,sBAAsB;QACxB;QAEA,IAAI,OAAO;QACX,IAAI,SAAS;QAEb,IAAI,WAAW;QACf,IAAI,UAAU;YACZ,WAAW;gBAAE,QAAQ;gBAAG,MAAM;YAAE;YAChC,UAAU;gBAAE,QAAQ;gBAAG,MAAM;YAAE;YAC/B,QAAQ;QACV;QAEA,IAAI,MAAM;QACV,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,MAAM;YACpC,IAAI,CAAC,GAAG,IAAI;YAEZ,IAAI,QAAQ,SAAS,OAAO;gBAC1B,QAAQ,SAAS,CAAC,IAAI,GAAG;gBACzB,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAS;gBACpC,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,KAAK,EAAE;oBACpC,QAAQ,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;oBACjC,QAAQ,QAAQ,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI;oBAC9C,QAAQ,QAAQ,CAAC,MAAM,GAAG,KAAK,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;oBACrD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;gBACtB,OAAO;oBACL,QAAQ,MAAM,GAAG;oBACjB,QAAQ,QAAQ,CAAC,IAAI,GAAG;oBACxB,QAAQ,QAAQ,CAAC,MAAM,GAAG;oBAC1B,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;gBACtB;YACF;YAEA,QAAQ,IAAI,KAAK,CAAC;YAClB,IAAI,OAAO;gBACT,QAAQ,MAAM,MAAM;gBACpB,OAAO,IAAI,WAAW,CAAC;gBACvB,SAAS,IAAI,MAAM,GAAG;YACxB,OAAO;gBACL,UAAU,IAAI,MAAM;YACtB;YAEA,IAAI,QAAQ,SAAS,SAAS;gBAC5B,IAAI,IAAI,KAAK,MAAM,IAAI;oBAAE,MAAM,CAAC;gBAAE;gBAClC,IAAI,YACF,KAAK,IAAI,KAAK,UAAW,KAAK,IAAI,KAAK,YAAY,CAAC,KAAK,KAAK;gBAChE,IAAI,CAAC,aAAa,SAAS,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE;oBACrD,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE;wBAClC,QAAQ,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;wBACjC,QAAQ,QAAQ,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI;wBAC5C,QAAQ,QAAQ,CAAC,MAAM,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG;wBACnD,QAAQ,SAAS,CAAC,IAAI,GAAG;wBACzB,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAS;wBACpC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBACtB,OAAO;wBACL,QAAQ,MAAM,GAAG;wBACjB,QAAQ,QAAQ,CAAC,IAAI,GAAG;wBACxB,QAAQ,QAAQ,CAAC,MAAM,GAAG;wBAC1B,QAAQ,SAAS,CAAC,IAAI,GAAG;wBACzB,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAS;wBACpC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBACtB;gBACF;YACF;QACF;IACF;IAEA,eAAe;QACb,IAAI,IAAI,CAAC,QAAQ,IAAI;YACnB,OAAO;QACT;QACA,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,aAAa;YAClD,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;QAChC;QACA,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,EAAE;YAC1B,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU;QAC/C;QACA,OAAO;IACT;IAEA,WAAW;QACT,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,aAAa;YAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;QAC5B;QAEA,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,UAAU;QACxC,IAAI,OAAO,eAAe,eAAe,eAAe,MAAM;YAC5D,OAAO;QACT;QAEA,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,EAAE;YAC1B,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM;QAC3C;QACA,OAAO;IACT;IAEA,QAAQ;QACN,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,aAAa;YACxC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;QACxB;QACA,OAAO,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG;IAClC;IAEA,mBAAmB;QACjB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,aAAa;YACtD,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc;QACpC;QACA,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,EAAE;YAC1B,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW;QAChD;QACA,OAAO;IACT;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YAChB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC/B,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;QACjC,OAAO;YACL,OAAO;QACT;IACF;IAEA,KAAK,IAAI,EAAE;QACT,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO;QAClC,IAAI,KAAK,UAAU,CAAC,OAAO,GAAG,OAAO,KAAI,OAAO;QAChD,IAAI,YAAY,IAAI,CAAC,OAAO,OAAO;QACnC,IAAI,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACpC,IAAI,QAAQ,OAAO;QAEnB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI;QAElD,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,UAAU;YAC/C,OAAO,QAAQ,QAAQ,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU;QACtD;QAEA,IAAI,OAAO,SAAS,MAAM;QAC1B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM;QAE7B,OAAO;IACT;IAEA,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,EAAE;YACtB,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACb,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;wBACxC,IAAI,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG;wBAC/B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;4BACpC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;wBACzB;oBACF;gBACF;YACF,OAAO;gBACL,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI;gBACjD,IAAI,MAAM,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG;YACjD;QACF;QAEA,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,oBAAoB;QAClB,IAAI,UAAU,CAAC;QACf,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACb,IAAI,KAAK,MAAM,EAAE;oBACf,IAAI,OAAO,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI;oBACjC,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE;wBAC1B,OAAO,CAAC,KAAK,GAAG;wBAChB,IAAI,UAAU,IAAI,CAAC,YAAY,GAC3B,IAAI,CAAC,SAAS,CAAC,QACf,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;wBACzB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,SAAS,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG;oBAC1D;gBACF;YACF;QACF,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE;YACnB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,GACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KACnC;YACJ,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,GAAG;QAC1C;IACF;IAEA,WAAW,IAAI,EAAE;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACrB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;QACrC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI;QAC9C,OAAO;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI;QACpD;IACF;IAEA,SAAS,GAAG,EAAE;QACZ,wCAAY;YACV,OAAO,OAAO,IAAI,CAAC,KAAK,QAAQ,CAAC;QACnC,OAAO;;QAEP;IACF;IAEA,UAAU,IAAI,EAAE;QACd,IAAI,SAAS,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACvC,IAAI,QAAQ,OAAO;QAEnB,IAAI,eAAe;YACjB,IAAI,UAAU,cAAc,MAAM,QAAQ;YAC1C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM;YAEhC,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MACR;QAEJ;IACF;IAEA,MAAM,IAAI,EAAE;QACV,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACnC,IAAI,QAAQ,OAAO;QAEnB,IAAI,QAAQ,MAAM;YAChB,OAAO,KAAK,OAAO,CAAC,OAAO;QAC7B;QAEA,IAAI,MAAM,UAAU,MAAM,OAAO,CAAC,SAAS;QAC3C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM;QAE5B,OAAO;IACT;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 5370, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/parser.js"], "sourcesContent": ["'use strict'\n\nlet AtRule = require('./at-rule')\nlet Comment = require('./comment')\nlet Declaration = require('./declaration')\nlet Root = require('./root')\nlet Rule = require('./rule')\nlet tokenizer = require('./tokenize')\n\nconst SAFE_COMMENT_NEIGHBOR = {\n  empty: true,\n  space: true\n}\n\nfunction findLastWithPosition(tokens) {\n  for (let i = tokens.length - 1; i >= 0; i--) {\n    let token = tokens[i]\n    let pos = token[3] || token[2]\n    if (pos) return pos\n  }\n}\n\nclass Parser {\n  constructor(input) {\n    this.input = input\n\n    this.root = new Root()\n    this.current = this.root\n    this.spaces = ''\n    this.semicolon = false\n\n    this.createTokenizer()\n    this.root.source = { input, start: { column: 1, line: 1, offset: 0 } }\n  }\n\n  atrule(token) {\n    let node = new AtRule()\n    node.name = token[1].slice(1)\n    if (node.name === '') {\n      this.unnamedAtrule(node, token)\n    }\n    this.init(node, token[2])\n\n    let type\n    let prev\n    let shift\n    let last = false\n    let open = false\n    let params = []\n    let brackets = []\n\n    while (!this.tokenizer.endOfFile()) {\n      token = this.tokenizer.nextToken()\n      type = token[0]\n\n      if (type === '(' || type === '[') {\n        brackets.push(type === '(' ? ')' : ']')\n      } else if (type === '{' && brackets.length > 0) {\n        brackets.push('}')\n      } else if (type === brackets[brackets.length - 1]) {\n        brackets.pop()\n      }\n\n      if (brackets.length === 0) {\n        if (type === ';') {\n          node.source.end = this.getPosition(token[2])\n          node.source.end.offset++\n          this.semicolon = true\n          break\n        } else if (type === '{') {\n          open = true\n          break\n        } else if (type === '}') {\n          if (params.length > 0) {\n            shift = params.length - 1\n            prev = params[shift]\n            while (prev && prev[0] === 'space') {\n              prev = params[--shift]\n            }\n            if (prev) {\n              node.source.end = this.getPosition(prev[3] || prev[2])\n              node.source.end.offset++\n            }\n          }\n          this.end(token)\n          break\n        } else {\n          params.push(token)\n        }\n      } else {\n        params.push(token)\n      }\n\n      if (this.tokenizer.endOfFile()) {\n        last = true\n        break\n      }\n    }\n\n    node.raws.between = this.spacesAndCommentsFromEnd(params)\n    if (params.length) {\n      node.raws.afterName = this.spacesAndCommentsFromStart(params)\n      this.raw(node, 'params', params)\n      if (last) {\n        token = params[params.length - 1]\n        node.source.end = this.getPosition(token[3] || token[2])\n        node.source.end.offset++\n        this.spaces = node.raws.between\n        node.raws.between = ''\n      }\n    } else {\n      node.raws.afterName = ''\n      node.params = ''\n    }\n\n    if (open) {\n      node.nodes = []\n      this.current = node\n    }\n  }\n\n  checkMissedSemicolon(tokens) {\n    let colon = this.colon(tokens)\n    if (colon === false) return\n\n    let founded = 0\n    let token\n    for (let j = colon - 1; j >= 0; j--) {\n      token = tokens[j]\n      if (token[0] !== 'space') {\n        founded += 1\n        if (founded === 2) break\n      }\n    }\n    // If the token is a word, e.g. `!important`, `red` or any other valid property's value.\n    // Then we need to return the colon after that word token. [3] is the \"end\" colon of that word.\n    // And because we need it after that one we do +1 to get the next one.\n    throw this.input.error(\n      'Missed semicolon',\n      token[0] === 'word' ? token[3] + 1 : token[2]\n    )\n  }\n\n  colon(tokens) {\n    let brackets = 0\n    let prev, token, type\n    for (let [i, element] of tokens.entries()) {\n      token = element\n      type = token[0]\n\n      if (type === '(') {\n        brackets += 1\n      }\n      if (type === ')') {\n        brackets -= 1\n      }\n      if (brackets === 0 && type === ':') {\n        if (!prev) {\n          this.doubleColon(token)\n        } else if (prev[0] === 'word' && prev[1] === 'progid') {\n          continue\n        } else {\n          return i\n        }\n      }\n\n      prev = token\n    }\n    return false\n  }\n\n  comment(token) {\n    let node = new Comment()\n    this.init(node, token[2])\n    node.source.end = this.getPosition(token[3] || token[2])\n    node.source.end.offset++\n\n    let text = token[1].slice(2, -2)\n    if (/^\\s*$/.test(text)) {\n      node.text = ''\n      node.raws.left = text\n      node.raws.right = ''\n    } else {\n      let match = text.match(/^(\\s*)([^]*\\S)(\\s*)$/)\n      node.text = match[2]\n      node.raws.left = match[1]\n      node.raws.right = match[3]\n    }\n  }\n\n  createTokenizer() {\n    this.tokenizer = tokenizer(this.input)\n  }\n\n  decl(tokens, customProperty) {\n    let node = new Declaration()\n    this.init(node, tokens[0][2])\n\n    let last = tokens[tokens.length - 1]\n    if (last[0] === ';') {\n      this.semicolon = true\n      tokens.pop()\n    }\n\n    node.source.end = this.getPosition(\n      last[3] || last[2] || findLastWithPosition(tokens)\n    )\n    node.source.end.offset++\n\n    while (tokens[0][0] !== 'word') {\n      if (tokens.length === 1) this.unknownWord(tokens)\n      node.raws.before += tokens.shift()[1]\n    }\n    node.source.start = this.getPosition(tokens[0][2])\n\n    node.prop = ''\n    while (tokens.length) {\n      let type = tokens[0][0]\n      if (type === ':' || type === 'space' || type === 'comment') {\n        break\n      }\n      node.prop += tokens.shift()[1]\n    }\n\n    node.raws.between = ''\n\n    let token\n    while (tokens.length) {\n      token = tokens.shift()\n\n      if (token[0] === ':') {\n        node.raws.between += token[1]\n        break\n      } else {\n        if (token[0] === 'word' && /\\w/.test(token[1])) {\n          this.unknownWord([token])\n        }\n        node.raws.between += token[1]\n      }\n    }\n\n    if (node.prop[0] === '_' || node.prop[0] === '*') {\n      node.raws.before += node.prop[0]\n      node.prop = node.prop.slice(1)\n    }\n\n    let firstSpaces = []\n    let next\n    while (tokens.length) {\n      next = tokens[0][0]\n      if (next !== 'space' && next !== 'comment') break\n      firstSpaces.push(tokens.shift())\n    }\n\n    this.precheckMissedSemicolon(tokens)\n\n    for (let i = tokens.length - 1; i >= 0; i--) {\n      token = tokens[i]\n      if (token[1].toLowerCase() === '!important') {\n        node.important = true\n        let string = this.stringFrom(tokens, i)\n        string = this.spacesFromEnd(tokens) + string\n        if (string !== ' !important') node.raws.important = string\n        break\n      } else if (token[1].toLowerCase() === 'important') {\n        let cache = tokens.slice(0)\n        let str = ''\n        for (let j = i; j > 0; j--) {\n          let type = cache[j][0]\n          if (str.trim().startsWith('!') && type !== 'space') {\n            break\n          }\n          str = cache.pop()[1] + str\n        }\n        if (str.trim().startsWith('!')) {\n          node.important = true\n          node.raws.important = str\n          tokens = cache\n        }\n      }\n\n      if (token[0] !== 'space' && token[0] !== 'comment') {\n        break\n      }\n    }\n\n    let hasWord = tokens.some(i => i[0] !== 'space' && i[0] !== 'comment')\n\n    if (hasWord) {\n      node.raws.between += firstSpaces.map(i => i[1]).join('')\n      firstSpaces = []\n    }\n    this.raw(node, 'value', firstSpaces.concat(tokens), customProperty)\n\n    if (node.value.includes(':') && !customProperty) {\n      this.checkMissedSemicolon(tokens)\n    }\n  }\n\n  doubleColon(token) {\n    throw this.input.error(\n      'Double colon',\n      { offset: token[2] },\n      { offset: token[2] + token[1].length }\n    )\n  }\n\n  emptyRule(token) {\n    let node = new Rule()\n    this.init(node, token[2])\n    node.selector = ''\n    node.raws.between = ''\n    this.current = node\n  }\n\n  end(token) {\n    if (this.current.nodes && this.current.nodes.length) {\n      this.current.raws.semicolon = this.semicolon\n    }\n    this.semicolon = false\n\n    this.current.raws.after = (this.current.raws.after || '') + this.spaces\n    this.spaces = ''\n\n    if (this.current.parent) {\n      this.current.source.end = this.getPosition(token[2])\n      this.current.source.end.offset++\n      this.current = this.current.parent\n    } else {\n      this.unexpectedClose(token)\n    }\n  }\n\n  endFile() {\n    if (this.current.parent) this.unclosedBlock()\n    if (this.current.nodes && this.current.nodes.length) {\n      this.current.raws.semicolon = this.semicolon\n    }\n    this.current.raws.after = (this.current.raws.after || '') + this.spaces\n    this.root.source.end = this.getPosition(this.tokenizer.position())\n  }\n\n  freeSemicolon(token) {\n    this.spaces += token[1]\n    if (this.current.nodes) {\n      let prev = this.current.nodes[this.current.nodes.length - 1]\n      if (prev && prev.type === 'rule' && !prev.raws.ownSemicolon) {\n        prev.raws.ownSemicolon = this.spaces\n        this.spaces = ''\n        prev.source.end = this.getPosition(token[2])\n        prev.source.end.offset += prev.raws.ownSemicolon.length\n      }\n    }\n  }\n\n  // Helpers\n\n  getPosition(offset) {\n    let pos = this.input.fromOffset(offset)\n    return {\n      column: pos.col,\n      line: pos.line,\n      offset\n    }\n  }\n\n  init(node, offset) {\n    this.current.push(node)\n    node.source = {\n      input: this.input,\n      start: this.getPosition(offset)\n    }\n    node.raws.before = this.spaces\n    this.spaces = ''\n    if (node.type !== 'comment') this.semicolon = false\n  }\n\n  other(start) {\n    let end = false\n    let type = null\n    let colon = false\n    let bracket = null\n    let brackets = []\n    let customProperty = start[1].startsWith('--')\n\n    let tokens = []\n    let token = start\n    while (token) {\n      type = token[0]\n      tokens.push(token)\n\n      if (type === '(' || type === '[') {\n        if (!bracket) bracket = token\n        brackets.push(type === '(' ? ')' : ']')\n      } else if (customProperty && colon && type === '{') {\n        if (!bracket) bracket = token\n        brackets.push('}')\n      } else if (brackets.length === 0) {\n        if (type === ';') {\n          if (colon) {\n            this.decl(tokens, customProperty)\n            return\n          } else {\n            break\n          }\n        } else if (type === '{') {\n          this.rule(tokens)\n          return\n        } else if (type === '}') {\n          this.tokenizer.back(tokens.pop())\n          end = true\n          break\n        } else if (type === ':') {\n          colon = true\n        }\n      } else if (type === brackets[brackets.length - 1]) {\n        brackets.pop()\n        if (brackets.length === 0) bracket = null\n      }\n\n      token = this.tokenizer.nextToken()\n    }\n\n    if (this.tokenizer.endOfFile()) end = true\n    if (brackets.length > 0) this.unclosedBracket(bracket)\n\n    if (end && colon) {\n      if (!customProperty) {\n        while (tokens.length) {\n          token = tokens[tokens.length - 1][0]\n          if (token !== 'space' && token !== 'comment') break\n          this.tokenizer.back(tokens.pop())\n        }\n      }\n      this.decl(tokens, customProperty)\n    } else {\n      this.unknownWord(tokens)\n    }\n  }\n\n  parse() {\n    let token\n    while (!this.tokenizer.endOfFile()) {\n      token = this.tokenizer.nextToken()\n\n      switch (token[0]) {\n        case 'space':\n          this.spaces += token[1]\n          break\n\n        case ';':\n          this.freeSemicolon(token)\n          break\n\n        case '}':\n          this.end(token)\n          break\n\n        case 'comment':\n          this.comment(token)\n          break\n\n        case 'at-word':\n          this.atrule(token)\n          break\n\n        case '{':\n          this.emptyRule(token)\n          break\n\n        default:\n          this.other(token)\n          break\n      }\n    }\n    this.endFile()\n  }\n\n  precheckMissedSemicolon(/* tokens */) {\n    // Hook for Safe Parser\n  }\n\n  raw(node, prop, tokens, customProperty) {\n    let token, type\n    let length = tokens.length\n    let value = ''\n    let clean = true\n    let next, prev\n\n    for (let i = 0; i < length; i += 1) {\n      token = tokens[i]\n      type = token[0]\n      if (type === 'space' && i === length - 1 && !customProperty) {\n        clean = false\n      } else if (type === 'comment') {\n        prev = tokens[i - 1] ? tokens[i - 1][0] : 'empty'\n        next = tokens[i + 1] ? tokens[i + 1][0] : 'empty'\n        if (!SAFE_COMMENT_NEIGHBOR[prev] && !SAFE_COMMENT_NEIGHBOR[next]) {\n          if (value.slice(-1) === ',') {\n            clean = false\n          } else {\n            value += token[1]\n          }\n        } else {\n          clean = false\n        }\n      } else {\n        value += token[1]\n      }\n    }\n    if (!clean) {\n      let raw = tokens.reduce((all, i) => all + i[1], '')\n      node.raws[prop] = { raw, value }\n    }\n    node[prop] = value\n  }\n\n  rule(tokens) {\n    tokens.pop()\n\n    let node = new Rule()\n    this.init(node, tokens[0][2])\n\n    node.raws.between = this.spacesAndCommentsFromEnd(tokens)\n    this.raw(node, 'selector', tokens)\n    this.current = node\n  }\n\n  spacesAndCommentsFromEnd(tokens) {\n    let lastTokenType\n    let spaces = ''\n    while (tokens.length) {\n      lastTokenType = tokens[tokens.length - 1][0]\n      if (lastTokenType !== 'space' && lastTokenType !== 'comment') break\n      spaces = tokens.pop()[1] + spaces\n    }\n    return spaces\n  }\n\n  // Errors\n\n  spacesAndCommentsFromStart(tokens) {\n    let next\n    let spaces = ''\n    while (tokens.length) {\n      next = tokens[0][0]\n      if (next !== 'space' && next !== 'comment') break\n      spaces += tokens.shift()[1]\n    }\n    return spaces\n  }\n\n  spacesFromEnd(tokens) {\n    let lastTokenType\n    let spaces = ''\n    while (tokens.length) {\n      lastTokenType = tokens[tokens.length - 1][0]\n      if (lastTokenType !== 'space') break\n      spaces = tokens.pop()[1] + spaces\n    }\n    return spaces\n  }\n\n  stringFrom(tokens, from) {\n    let result = ''\n    for (let i = from; i < tokens.length; i++) {\n      result += tokens[i][1]\n    }\n    tokens.splice(from, tokens.length - from)\n    return result\n  }\n\n  unclosedBlock() {\n    let pos = this.current.source.start\n    throw this.input.error('Unclosed block', pos.line, pos.column)\n  }\n\n  unclosedBracket(bracket) {\n    throw this.input.error(\n      'Unclosed bracket',\n      { offset: bracket[2] },\n      { offset: bracket[2] + 1 }\n    )\n  }\n\n  unexpectedClose(token) {\n    throw this.input.error(\n      'Unexpected }',\n      { offset: token[2] },\n      { offset: token[2] + 1 }\n    )\n  }\n\n  unknownWord(tokens) {\n    throw this.input.error(\n      'Unknown word ' + tokens[0][1],\n      { offset: tokens[0][2] },\n      { offset: tokens[0][2] + tokens[0][1].length }\n    )\n  }\n\n  unnamedAtrule(node, token) {\n    throw this.input.error(\n      'At-rule without name',\n      { offset: token[2] },\n      { offset: token[2] + token[1].length }\n    )\n  }\n}\n\nmodule.exports = Parser\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,wBAAwB;IAC5B,OAAO;IACP,OAAO;AACT;AAEA,SAAS,qBAAqB,MAAM;IAClC,IAAK,IAAI,IAAI,OAAO,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC3C,IAAI,QAAQ,MAAM,CAAC,EAAE;QACrB,IAAI,MAAM,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;QAC9B,IAAI,KAAK,OAAO;IAClB;AACF;AAEA,MAAM;IACJ,YAAY,KAAK,CAAE;QACjB,IAAI,CAAC,KAAK,GAAG;QAEb,IAAI,CAAC,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;YAAE;YAAO,OAAO;gBAAE,QAAQ;gBAAG,MAAM;gBAAG,QAAQ;YAAE;QAAE;IACvE;IAEA,OAAO,KAAK,EAAE;QACZ,IAAI,OAAO,IAAI;QACf,KAAK,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC3B,IAAI,KAAK,IAAI,KAAK,IAAI;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM;QAC3B;QACA,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAExB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,SAAS,EAAE;QACf,IAAI,WAAW,EAAE;QAEjB,MAAO,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAI;YAClC,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS;YAChC,OAAO,KAAK,CAAC,EAAE;YAEf,IAAI,SAAS,OAAO,SAAS,KAAK;gBAChC,SAAS,IAAI,CAAC,SAAS,MAAM,MAAM;YACrC,OAAO,IAAI,SAAS,OAAO,SAAS,MAAM,GAAG,GAAG;gBAC9C,SAAS,IAAI,CAAC;YAChB,OAAO,IAAI,SAAS,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,EAAE;gBACjD,SAAS,GAAG;YACd;YAEA,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,IAAI,SAAS,KAAK;oBAChB,KAAK,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;oBAC3C,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM;oBACtB,IAAI,CAAC,SAAS,GAAG;oBACjB;gBACF,OAAO,IAAI,SAAS,KAAK;oBACvB,OAAO;oBACP;gBACF,OAAO,IAAI,SAAS,KAAK;oBACvB,IAAI,OAAO,MAAM,GAAG,GAAG;wBACrB,QAAQ,OAAO,MAAM,GAAG;wBACxB,OAAO,MAAM,CAAC,MAAM;wBACpB,MAAO,QAAQ,IAAI,CAAC,EAAE,KAAK,QAAS;4BAClC,OAAO,MAAM,CAAC,EAAE,MAAM;wBACxB;wBACA,IAAI,MAAM;4BACR,KAAK,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE;4BACrD,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM;wBACxB;oBACF;oBACA,IAAI,CAAC,GAAG,CAAC;oBACT;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;YAEA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI;gBAC9B,OAAO;gBACP;YACF;QACF;QAEA,KAAK,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAClD,IAAI,OAAO,MAAM,EAAE;YACjB,KAAK,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC;YACtD,IAAI,CAAC,GAAG,CAAC,MAAM,UAAU;YACzB,IAAI,MAAM;gBACR,QAAQ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;gBACjC,KAAK,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;gBACvD,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM;gBACtB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,OAAO;gBAC/B,KAAK,IAAI,CAAC,OAAO,GAAG;YACtB;QACF,OAAO;YACL,KAAK,IAAI,CAAC,SAAS,GAAG;YACtB,KAAK,MAAM,GAAG;QAChB;QAEA,IAAI,MAAM;YACR,KAAK,KAAK,GAAG,EAAE;YACf,IAAI,CAAC,OAAO,GAAG;QACjB;IACF;IAEA,qBAAqB,MAAM,EAAE;QAC3B,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,UAAU,OAAO;QAErB,IAAI,UAAU;QACd,IAAI;QACJ,IAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,GAAG,IAAK;YACnC,QAAQ,MAAM,CAAC,EAAE;YACjB,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS;gBACxB,WAAW;gBACX,IAAI,YAAY,GAAG;YACrB;QACF;QACA,wFAAwF;QACxF,+FAA+F;QAC/F,sEAAsE;QACtE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,oBACA,KAAK,CAAC,EAAE,KAAK,SAAS,KAAK,CAAC,EAAE,GAAG,IAAI,KAAK,CAAC,EAAE;IAEjD;IAEA,MAAM,MAAM,EAAE;QACZ,IAAI,WAAW;QACf,IAAI,MAAM,OAAO;QACjB,KAAK,IAAI,CAAC,GAAG,QAAQ,IAAI,OAAO,OAAO,GAAI;YACzC,QAAQ;YACR,OAAO,KAAK,CAAC,EAAE;YAEf,IAAI,SAAS,KAAK;gBAChB,YAAY;YACd;YACA,IAAI,SAAS,KAAK;gBAChB,YAAY;YACd;YACA,IAAI,aAAa,KAAK,SAAS,KAAK;gBAClC,IAAI,CAAC,MAAM;oBACT,IAAI,CAAC,WAAW,CAAC;gBACnB,OAAO,IAAI,IAAI,CAAC,EAAE,KAAK,UAAU,IAAI,CAAC,EAAE,KAAK,UAAU;oBACrD;gBACF,OAAO;oBACL,OAAO;gBACT;YACF;YAEA,OAAO;QACT;QACA,OAAO;IACT;IAEA,QAAQ,KAAK,EAAE;QACb,IAAI,OAAO,IAAI;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,KAAK,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;QACvD,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM;QAEtB,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9B,IAAI,QAAQ,IAAI,CAAC,OAAO;YACtB,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,CAAC,IAAI,GAAG;YACjB,KAAK,IAAI,CAAC,KAAK,GAAG;QACpB,OAAO;YACL,IAAI,QAAQ,KAAK,KAAK,CAAC;YACvB,KAAK,IAAI,GAAG,KAAK,CAAC,EAAE;YACpB,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;YACzB,KAAK,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE;QAC5B;IACF;IAEA,kBAAkB;QAChB,IAAI,CAAC,SAAS,GAAG,UAAU,IAAI,CAAC,KAAK;IACvC;IAEA,KAAK,MAAM,EAAE,cAAc,EAAE;QAC3B,IAAI,OAAO,IAAI;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE;QAE5B,IAAI,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QACpC,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;YACnB,IAAI,CAAC,SAAS,GAAG;YACjB,OAAO,GAAG;QACZ;QAEA,KAAK,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAChC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,qBAAqB;QAE7C,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM;QAEtB,MAAO,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,OAAQ;YAC9B,IAAI,OAAO,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;YAC1C,KAAK,IAAI,CAAC,MAAM,IAAI,OAAO,KAAK,EAAE,CAAC,EAAE;QACvC;QACA,KAAK,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;QAEjD,KAAK,IAAI,GAAG;QACZ,MAAO,OAAO,MAAM,CAAE;YACpB,IAAI,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE;YACvB,IAAI,SAAS,OAAO,SAAS,WAAW,SAAS,WAAW;gBAC1D;YACF;YACA,KAAK,IAAI,IAAI,OAAO,KAAK,EAAE,CAAC,EAAE;QAChC;QAEA,KAAK,IAAI,CAAC,OAAO,GAAG;QAEpB,IAAI;QACJ,MAAO,OAAO,MAAM,CAAE;YACpB,QAAQ,OAAO,KAAK;YAEpB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;gBACpB,KAAK,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE;gBAC7B;YACF,OAAO;gBACL,IAAI,KAAK,CAAC,EAAE,KAAK,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG;oBAC9C,IAAI,CAAC,WAAW,CAAC;wBAAC;qBAAM;gBAC1B;gBACA,KAAK,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE;YAC/B;QACF;QAEA,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK;YAChD,KAAK,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE;YAChC,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;QAC9B;QAEA,IAAI,cAAc,EAAE;QACpB,IAAI;QACJ,MAAO,OAAO,MAAM,CAAE;YACpB,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE;YACnB,IAAI,SAAS,WAAW,SAAS,WAAW;YAC5C,YAAY,IAAI,CAAC,OAAO,KAAK;QAC/B;QAEA,IAAI,CAAC,uBAAuB,CAAC;QAE7B,IAAK,IAAI,IAAI,OAAO,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC3C,QAAQ,MAAM,CAAC,EAAE;YACjB,IAAI,KAAK,CAAC,EAAE,CAAC,WAAW,OAAO,cAAc;gBAC3C,KAAK,SAAS,GAAG;gBACjB,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ;gBACrC,SAAS,IAAI,CAAC,aAAa,CAAC,UAAU;gBACtC,IAAI,WAAW,eAAe,KAAK,IAAI,CAAC,SAAS,GAAG;gBACpD;YACF,OAAO,IAAI,KAAK,CAAC,EAAE,CAAC,WAAW,OAAO,aAAa;gBACjD,IAAI,QAAQ,OAAO,KAAK,CAAC;gBACzB,IAAI,MAAM;gBACV,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC,EAAE;oBACtB,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,QAAQ,SAAS,SAAS;wBAClD;oBACF;oBACA,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,GAAG;gBACzB;gBACA,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,MAAM;oBAC9B,KAAK,SAAS,GAAG;oBACjB,KAAK,IAAI,CAAC,SAAS,GAAG;oBACtB,SAAS;gBACX;YACF;YAEA,IAAI,KAAK,CAAC,EAAE,KAAK,WAAW,KAAK,CAAC,EAAE,KAAK,WAAW;gBAClD;YACF;QACF;QAEA,IAAI,UAAU,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK;QAE5D,IAAI,SAAS;YACX,KAAK,IAAI,CAAC,OAAO,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;YACrD,cAAc,EAAE;QAClB;QACA,IAAI,CAAC,GAAG,CAAC,MAAM,SAAS,YAAY,MAAM,CAAC,SAAS;QAEpD,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB;YAC/C,IAAI,CAAC,oBAAoB,CAAC;QAC5B;IACF;IAEA,YAAY,KAAK,EAAE;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,gBACA;YAAE,QAAQ,KAAK,CAAC,EAAE;QAAC,GACnB;YAAE,QAAQ,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;QAAC;IAEzC;IAEA,UAAU,KAAK,EAAE;QACf,IAAI,OAAO,IAAI;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,KAAK,QAAQ,GAAG;QAChB,KAAK,IAAI,CAAC,OAAO,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,IAAI,KAAK,EAAE;QACT,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;YACnD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;QAC9C;QACA,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM;QACvE,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;YACnD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM;YAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;QACpC,OAAO;YACL,IAAI,CAAC,eAAe,CAAC;QACvB;IACF;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa;QAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;YACnD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;QAC9C;QACA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM;QACvE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;IACjE;IAEA,cAAc,KAAK,EAAE;QACnB,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE;QACvB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;YAC5D,IAAI,QAAQ,KAAK,IAAI,KAAK,UAAU,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE;gBAC3D,KAAK,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM;gBACpC,IAAI,CAAC,MAAM,GAAG;gBACd,KAAK,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;gBAC3C,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM;YACzD;QACF;IACF;IAEA,UAAU;IAEV,YAAY,MAAM,EAAE;QAClB,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;QAChC,OAAO;YACL,QAAQ,IAAI,GAAG;YACf,MAAM,IAAI,IAAI;YACd;QACF;IACF;IAEA,KAAK,IAAI,EAAE,MAAM,EAAE;QACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAClB,KAAK,MAAM,GAAG;YACZ,OAAO,IAAI,CAAC,KAAK;YACjB,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;QACA,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,KAAK,IAAI,KAAK,WAAW,IAAI,CAAC,SAAS,GAAG;IAChD;IAEA,MAAM,KAAK,EAAE;QACX,IAAI,MAAM;QACV,IAAI,OAAO;QACX,IAAI,QAAQ;QACZ,IAAI,UAAU;QACd,IAAI,WAAW,EAAE;QACjB,IAAI,iBAAiB,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC;QAEzC,IAAI,SAAS,EAAE;QACf,IAAI,QAAQ;QACZ,MAAO,MAAO;YACZ,OAAO,KAAK,CAAC,EAAE;YACf,OAAO,IAAI,CAAC;YAEZ,IAAI,SAAS,OAAO,SAAS,KAAK;gBAChC,IAAI,CAAC,SAAS,UAAU;gBACxB,SAAS,IAAI,CAAC,SAAS,MAAM,MAAM;YACrC,OAAO,IAAI,kBAAkB,SAAS,SAAS,KAAK;gBAClD,IAAI,CAAC,SAAS,UAAU;gBACxB,SAAS,IAAI,CAAC;YAChB,OAAO,IAAI,SAAS,MAAM,KAAK,GAAG;gBAChC,IAAI,SAAS,KAAK;oBAChB,IAAI,OAAO;wBACT,IAAI,CAAC,IAAI,CAAC,QAAQ;wBAClB;oBACF,OAAO;wBACL;oBACF;gBACF,OAAO,IAAI,SAAS,KAAK;oBACvB,IAAI,CAAC,IAAI,CAAC;oBACV;gBACF,OAAO,IAAI,SAAS,KAAK;oBACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,GAAG;oBAC9B,MAAM;oBACN;gBACF,OAAO,IAAI,SAAS,KAAK;oBACvB,QAAQ;gBACV;YACF,OAAO,IAAI,SAAS,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,EAAE;gBACjD,SAAS,GAAG;gBACZ,IAAI,SAAS,MAAM,KAAK,GAAG,UAAU;YACvC;YAEA,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS;QAClC;QAEA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,MAAM;QACtC,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC;QAE9C,IAAI,OAAO,OAAO;YAChB,IAAI,CAAC,gBAAgB;gBACnB,MAAO,OAAO,MAAM,CAAE;oBACpB,QAAQ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE;oBACpC,IAAI,UAAU,WAAW,UAAU,WAAW;oBAC9C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,GAAG;gBAChC;YACF;YACA,IAAI,CAAC,IAAI,CAAC,QAAQ;QACpB,OAAO;YACL,IAAI,CAAC,WAAW,CAAC;QACnB;IACF;IAEA,QAAQ;QACN,IAAI;QACJ,MAAO,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAI;YAClC,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS;YAEhC,OAAQ,KAAK,CAAC,EAAE;gBACd,KAAK;oBACH,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE;oBACvB;gBAEF,KAAK;oBACH,IAAI,CAAC,aAAa,CAAC;oBACnB;gBAEF,KAAK;oBACH,IAAI,CAAC,GAAG,CAAC;oBACT;gBAEF,KAAK;oBACH,IAAI,CAAC,OAAO,CAAC;oBACb;gBAEF,KAAK;oBACH,IAAI,CAAC,MAAM,CAAC;oBACZ;gBAEF,KAAK;oBACH,IAAI,CAAC,SAAS,CAAC;oBACf;gBAEF;oBACE,IAAI,CAAC,KAAK,CAAC;oBACX;YACJ;QACF;QACA,IAAI,CAAC,OAAO;IACd;IAEA,0BAAsC;IACpC,uBAAuB;IACzB;IAEA,IAAI,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE;QACtC,IAAI,OAAO;QACX,IAAI,SAAS,OAAO,MAAM;QAC1B,IAAI,QAAQ;QACZ,IAAI,QAAQ;QACZ,IAAI,MAAM;QAEV,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;YAClC,QAAQ,MAAM,CAAC,EAAE;YACjB,OAAO,KAAK,CAAC,EAAE;YACf,IAAI,SAAS,WAAW,MAAM,SAAS,KAAK,CAAC,gBAAgB;gBAC3D,QAAQ;YACV,OAAO,IAAI,SAAS,WAAW;gBAC7B,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;gBAC1C,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;gBAC1C,IAAI,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE;oBAChE,IAAI,MAAM,KAAK,CAAC,CAAC,OAAO,KAAK;wBAC3B,QAAQ;oBACV,OAAO;wBACL,SAAS,KAAK,CAAC,EAAE;oBACnB;gBACF,OAAO;oBACL,QAAQ;gBACV;YACF,OAAO;gBACL,SAAS,KAAK,CAAC,EAAE;YACnB;QACF;QACA,IAAI,CAAC,OAAO;YACV,IAAI,MAAM,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,CAAC,EAAE,EAAE;YAChD,KAAK,IAAI,CAAC,KAAK,GAAG;gBAAE;gBAAK;YAAM;QACjC;QACA,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,KAAK,MAAM,EAAE;QACX,OAAO,GAAG;QAEV,IAAI,OAAO,IAAI;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE;QAE5B,KAAK,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAClD,IAAI,CAAC,GAAG,CAAC,MAAM,YAAY;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,yBAAyB,MAAM,EAAE;QAC/B,IAAI;QACJ,IAAI,SAAS;QACb,MAAO,OAAO,MAAM,CAAE;YACpB,gBAAgB,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE;YAC5C,IAAI,kBAAkB,WAAW,kBAAkB,WAAW;YAC9D,SAAS,OAAO,GAAG,EAAE,CAAC,EAAE,GAAG;QAC7B;QACA,OAAO;IACT;IAEA,SAAS;IAET,2BAA2B,MAAM,EAAE;QACjC,IAAI;QACJ,IAAI,SAAS;QACb,MAAO,OAAO,MAAM,CAAE;YACpB,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE;YACnB,IAAI,SAAS,WAAW,SAAS,WAAW;YAC5C,UAAU,OAAO,KAAK,EAAE,CAAC,EAAE;QAC7B;QACA,OAAO;IACT;IAEA,cAAc,MAAM,EAAE;QACpB,IAAI;QACJ,IAAI,SAAS;QACb,MAAO,OAAO,MAAM,CAAE;YACpB,gBAAgB,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE;YAC5C,IAAI,kBAAkB,SAAS;YAC/B,SAAS,OAAO,GAAG,EAAE,CAAC,EAAE,GAAG;QAC7B;QACA,OAAO;IACT;IAEA,WAAW,MAAM,EAAE,IAAI,EAAE;QACvB,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,MAAM,IAAI,OAAO,MAAM,EAAE,IAAK;YACzC,UAAU,MAAM,CAAC,EAAE,CAAC,EAAE;QACxB;QACA,OAAO,MAAM,CAAC,MAAM,OAAO,MAAM,GAAG;QACpC,OAAO;IACT;IAEA,gBAAgB;QACd,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;QACnC,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,kBAAkB,IAAI,IAAI,EAAE,IAAI,MAAM;IAC/D;IAEA,gBAAgB,OAAO,EAAE;QACvB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,oBACA;YAAE,QAAQ,OAAO,CAAC,EAAE;QAAC,GACrB;YAAE,QAAQ,OAAO,CAAC,EAAE,GAAG;QAAE;IAE7B;IAEA,gBAAgB,KAAK,EAAE;QACrB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,gBACA;YAAE,QAAQ,KAAK,CAAC,EAAE;QAAC,GACnB;YAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAAE;IAE3B;IAEA,YAAY,MAAM,EAAE;QAClB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,kBAAkB,MAAM,CAAC,EAAE,CAAC,EAAE,EAC9B;YAAE,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE;QAAC,GACvB;YAAE,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM;QAAC;IAEjD;IAEA,cAAc,IAAI,EAAE,KAAK,EAAE;QACzB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CACpB,wBACA;YAAE,QAAQ,KAAK,CAAC,EAAE;QAAC,GACnB;YAAE,QAAQ,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;QAAC;IAEzC;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 5914, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/parse.js"], "sourcesContent": ["'use strict'\n\nlet Container = require('./container')\nlet Input = require('./input')\nlet Parser = require('./parser')\n\nfunction parse(css, opts) {\n  let input = new Input(css, opts)\n  let parser = new Parser(input)\n  try {\n    parser.parse()\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (e.name === 'CssSyntaxError' && opts && opts.from) {\n        if (/\\.scss$/i.test(opts.from)) {\n          e.message +=\n            '\\nYou tried to parse SCSS with ' +\n            'the standard CSS parser; ' +\n            'try again with the postcss-scss parser'\n        } else if (/\\.sass/i.test(opts.from)) {\n          e.message +=\n            '\\nYou tried to parse Sass with ' +\n            'the standard CSS parser; ' +\n            'try again with the postcss-sass parser'\n        } else if (/\\.less$/i.test(opts.from)) {\n          e.message +=\n            '\\nYou tried to parse Less with ' +\n            'the standard CSS parser; ' +\n            'try again with the postcss-less parser'\n        }\n      }\n    }\n    throw e\n  }\n\n  return parser.root\n}\n\nmodule.exports = parse\nparse.default = parse\n\nContainer.registerParse(parse)\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,MAAM,GAAG,EAAE,IAAI;IACtB,IAAI,QAAQ,IAAI,MAAM,KAAK;IAC3B,IAAI,SAAS,IAAI,OAAO;IACxB,IAAI;QACF,OAAO,KAAK;IACd,EAAE,OAAO,GAAG;QACV,wCAA2C;YACzC,IAAI,EAAE,IAAI,KAAK,oBAAoB,QAAQ,KAAK,IAAI,EAAE;gBACpD,IAAI,WAAW,IAAI,CAAC,KAAK,IAAI,GAAG;oBAC9B,EAAE,OAAO,IACP,oCACA,8BACA;gBACJ,OAAO,IAAI,UAAU,IAAI,CAAC,KAAK,IAAI,GAAG;oBACpC,EAAE,OAAO,IACP,oCACA,8BACA;gBACJ,OAAO,IAAI,WAAW,IAAI,CAAC,KAAK,IAAI,GAAG;oBACrC,EAAE,OAAO,IACP,oCACA,8BACA;gBACJ;YACF;QACF;QACA,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;AAEA,OAAO,OAAO,GAAG;AACjB,MAAM,OAAO,GAAG;AAEhB,UAAU,aAAa,CAAC", "ignoreList": [0]}}, {"offset": {"line": 5947, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/warning.js"], "sourcesContent": ["'use strict'\n\nclass Warning {\n  constructor(text, opts = {}) {\n    this.type = 'warning'\n    this.text = text\n\n    if (opts.node && opts.node.source) {\n      let range = opts.node.rangeBy(opts)\n      this.line = range.start.line\n      this.column = range.start.column\n      this.endLine = range.end.line\n      this.endColumn = range.end.column\n    }\n\n    for (let opt in opts) this[opt] = opts[opt]\n  }\n\n  toString() {\n    if (this.node) {\n      return this.node.error(this.text, {\n        index: this.index,\n        plugin: this.plugin,\n        word: this.word\n      }).message\n    }\n\n    if (this.plugin) {\n      return this.plugin + ': ' + this.text\n    }\n\n    return this.text\n  }\n}\n\nmodule.exports = Warning\nWarning.default = Warning\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;IACJ,YAAY,IAAI,EAAE,OAAO,CAAC,CAAC,CAAE;QAC3B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QAEZ,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;YACjC,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC;YAC9B,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,IAAI;YAC5B,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM;YAChC,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG,CAAC,IAAI;YAC7B,IAAI,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,MAAM;QACnC;QAEA,IAAK,IAAI,OAAO,KAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;IAC7C;IAEA,WAAW;QACT,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE;gBAChC,OAAO,IAAI,CAAC,KAAK;gBACjB,QAAQ,IAAI,CAAC,MAAM;gBACnB,MAAM,IAAI,CAAC,IAAI;YACjB,GAAG,OAAO;QACZ;QAEA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,IAAI;QACvC;QAEA,OAAO,IAAI,CAAC,IAAI;IAClB;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,QAAQ,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 5982, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/result.js"], "sourcesContent": ["'use strict'\n\nlet Warning = require('./warning')\n\nclass Result {\n  get content() {\n    return this.css\n  }\n\n  constructor(processor, root, opts) {\n    this.processor = processor\n    this.messages = []\n    this.root = root\n    this.opts = opts\n    this.css = ''\n    this.map = undefined\n  }\n\n  toString() {\n    return this.css\n  }\n\n  warn(text, opts = {}) {\n    if (!opts.plugin) {\n      if (this.lastPlugin && this.lastPlugin.postcssPlugin) {\n        opts.plugin = this.lastPlugin.postcssPlugin\n      }\n    }\n\n    let warning = new Warning(text, opts)\n    this.messages.push(warning)\n\n    return warning\n  }\n\n  warnings() {\n    return this.messages.filter(i => i.type === 'warning')\n  }\n}\n\nmodule.exports = Result\nResult.default = Result\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,MAAM;IACJ,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,GAAG;IACjB;IAEA,YAAY,SAAS,EAAE,IAAI,EAAE,IAAI,CAAE;QACjC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,WAAW;QACT,OAAO,IAAI,CAAC,GAAG;IACjB;IAEA,KAAK,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE;QACpB,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE;gBACpD,KAAK,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;YAC7C;QACF;QAEA,IAAI,UAAU,IAAI,QAAQ,MAAM;QAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAEnB,OAAO;IACT;IAEA,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IAC9C;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 6020, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/warn-once.js"], "sourcesContent": ["/* eslint-disable no-console */\n'use strict'\n\nlet printed = {}\n\nmodule.exports = function warnOnce(message) {\n  if (printed[message]) return\n  printed[message] = true\n\n  if (typeof console !== 'undefined' && console.warn) {\n    console.warn(message)\n  }\n}\n"], "names": [], "mappings": "AAAA,6BAA6B,GAC7B;AAEA,IAAI,UAAU,CAAC;AAEf,OAAO,OAAO,GAAG,SAAS,SAAS,OAAO;IACxC,IAAI,OAAO,CAAC,QAAQ,EAAE;IACtB,OAAO,CAAC,QAAQ,GAAG;IAEnB,IAAI,OAAO,YAAY,eAAe,QAAQ,IAAI,EAAE;QAClD,QAAQ,IAAI,CAAC;IACf;AACF", "ignoreList": [0]}}, {"offset": {"line": 6034, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/lazy-result.js"], "sourcesContent": ["'use strict'\n\nlet Container = require('./container')\nlet Document = require('./document')\nlet MapGenerator = require('./map-generator')\nlet parse = require('./parse')\nlet Result = require('./result')\nlet Root = require('./root')\nlet stringify = require('./stringify')\nlet { isClean, my } = require('./symbols')\nlet warnOnce = require('./warn-once')\n\nconst TYPE_TO_CLASS_NAME = {\n  atrule: 'AtRule',\n  comment: 'Comment',\n  decl: 'Declaration',\n  document: 'Document',\n  root: 'Root',\n  rule: 'Rule'\n}\n\nconst PLUGIN_PROPS = {\n  AtRule: true,\n  AtRuleExit: true,\n  Comment: true,\n  CommentExit: true,\n  Declaration: true,\n  DeclarationExit: true,\n  Document: true,\n  DocumentExit: true,\n  Once: true,\n  OnceExit: true,\n  postcssPlugin: true,\n  prepare: true,\n  Root: true,\n  RootExit: true,\n  Rule: true,\n  RuleExit: true\n}\n\nconst NOT_VISITORS = {\n  Once: true,\n  postcssPlugin: true,\n  prepare: true\n}\n\nconst CHILDREN = 0\n\nfunction isPromise(obj) {\n  return typeof obj === 'object' && typeof obj.then === 'function'\n}\n\nfunction getEvents(node) {\n  let key = false\n  let type = TYPE_TO_CLASS_NAME[node.type]\n  if (node.type === 'decl') {\n    key = node.prop.toLowerCase()\n  } else if (node.type === 'atrule') {\n    key = node.name.toLowerCase()\n  }\n\n  if (key && node.append) {\n    return [\n      type,\n      type + '-' + key,\n      CHILDREN,\n      type + 'Exit',\n      type + 'Exit-' + key\n    ]\n  } else if (key) {\n    return [type, type + '-' + key, type + 'Exit', type + 'Exit-' + key]\n  } else if (node.append) {\n    return [type, CHILDREN, type + 'Exit']\n  } else {\n    return [type, type + 'Exit']\n  }\n}\n\nfunction toStack(node) {\n  let events\n  if (node.type === 'document') {\n    events = ['Document', CHILDREN, 'DocumentExit']\n  } else if (node.type === 'root') {\n    events = ['Root', CHILDREN, 'RootExit']\n  } else {\n    events = getEvents(node)\n  }\n\n  return {\n    eventIndex: 0,\n    events,\n    iterator: 0,\n    node,\n    visitorIndex: 0,\n    visitors: []\n  }\n}\n\nfunction cleanMarks(node) {\n  node[isClean] = false\n  if (node.nodes) node.nodes.forEach(i => cleanMarks(i))\n  return node\n}\n\nlet postcss = {}\n\nclass LazyResult {\n  get content() {\n    return this.stringify().content\n  }\n\n  get css() {\n    return this.stringify().css\n  }\n\n  get map() {\n    return this.stringify().map\n  }\n\n  get messages() {\n    return this.sync().messages\n  }\n\n  get opts() {\n    return this.result.opts\n  }\n\n  get processor() {\n    return this.result.processor\n  }\n\n  get root() {\n    return this.sync().root\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'LazyResult'\n  }\n\n  constructor(processor, css, opts) {\n    this.stringified = false\n    this.processed = false\n\n    let root\n    if (\n      typeof css === 'object' &&\n      css !== null &&\n      (css.type === 'root' || css.type === 'document')\n    ) {\n      root = cleanMarks(css)\n    } else if (css instanceof LazyResult || css instanceof Result) {\n      root = cleanMarks(css.root)\n      if (css.map) {\n        if (typeof opts.map === 'undefined') opts.map = {}\n        if (!opts.map.inline) opts.map.inline = false\n        opts.map.prev = css.map\n      }\n    } else {\n      let parser = parse\n      if (opts.syntax) parser = opts.syntax.parse\n      if (opts.parser) parser = opts.parser\n      if (parser.parse) parser = parser.parse\n\n      try {\n        root = parser(css, opts)\n      } catch (error) {\n        this.processed = true\n        this.error = error\n      }\n\n      if (root && !root[my]) {\n        /* c8 ignore next 2 */\n        Container.rebuild(root)\n      }\n    }\n\n    this.result = new Result(processor, root, opts)\n    this.helpers = { ...postcss, postcss, result: this.result }\n    this.plugins = this.processor.plugins.map(plugin => {\n      if (typeof plugin === 'object' && plugin.prepare) {\n        return { ...plugin, ...plugin.prepare(this.result) }\n      } else {\n        return plugin\n      }\n    })\n  }\n\n  async() {\n    if (this.error) return Promise.reject(this.error)\n    if (this.processed) return Promise.resolve(this.result)\n    if (!this.processing) {\n      this.processing = this.runAsync()\n    }\n    return this.processing\n  }\n\n  catch(onRejected) {\n    return this.async().catch(onRejected)\n  }\n\n  finally(onFinally) {\n    return this.async().then(onFinally, onFinally)\n  }\n\n  getAsyncError() {\n    throw new Error('Use process(css).then(cb) to work with async plugins')\n  }\n\n  handleError(error, node) {\n    let plugin = this.result.lastPlugin\n    try {\n      if (node) node.addToError(error)\n      this.error = error\n      if (error.name === 'CssSyntaxError' && !error.plugin) {\n        error.plugin = plugin.postcssPlugin\n        error.setMessage()\n      } else if (plugin.postcssVersion) {\n        if (process.env.NODE_ENV !== 'production') {\n          let pluginName = plugin.postcssPlugin\n          let pluginVer = plugin.postcssVersion\n          let runtimeVer = this.result.processor.version\n          let a = pluginVer.split('.')\n          let b = runtimeVer.split('.')\n\n          if (a[0] !== b[0] || parseInt(a[1]) > parseInt(b[1])) {\n            // eslint-disable-next-line no-console\n            console.error(\n              'Unknown error from PostCSS plugin. Your current PostCSS ' +\n                'version is ' +\n                runtimeVer +\n                ', but ' +\n                pluginName +\n                ' uses ' +\n                pluginVer +\n                '. Perhaps this is the source of the error below.'\n            )\n          }\n        }\n      }\n    } catch (err) {\n      /* c8 ignore next 3 */\n      // eslint-disable-next-line no-console\n      if (console && console.error) console.error(err)\n    }\n    return error\n  }\n\n  prepareVisitors() {\n    this.listeners = {}\n    let add = (plugin, type, cb) => {\n      if (!this.listeners[type]) this.listeners[type] = []\n      this.listeners[type].push([plugin, cb])\n    }\n    for (let plugin of this.plugins) {\n      if (typeof plugin === 'object') {\n        for (let event in plugin) {\n          if (!PLUGIN_PROPS[event] && /^[A-Z]/.test(event)) {\n            throw new Error(\n              `Unknown event ${event} in ${plugin.postcssPlugin}. ` +\n                `Try to update PostCSS (${this.processor.version} now).`\n            )\n          }\n          if (!NOT_VISITORS[event]) {\n            if (typeof plugin[event] === 'object') {\n              for (let filter in plugin[event]) {\n                if (filter === '*') {\n                  add(plugin, event, plugin[event][filter])\n                } else {\n                  add(\n                    plugin,\n                    event + '-' + filter.toLowerCase(),\n                    plugin[event][filter]\n                  )\n                }\n              }\n            } else if (typeof plugin[event] === 'function') {\n              add(plugin, event, plugin[event])\n            }\n          }\n        }\n      }\n    }\n    this.hasListener = Object.keys(this.listeners).length > 0\n  }\n\n  async runAsync() {\n    this.plugin = 0\n    for (let i = 0; i < this.plugins.length; i++) {\n      let plugin = this.plugins[i]\n      let promise = this.runOnRoot(plugin)\n      if (isPromise(promise)) {\n        try {\n          await promise\n        } catch (error) {\n          throw this.handleError(error)\n        }\n      }\n    }\n\n    this.prepareVisitors()\n    if (this.hasListener) {\n      let root = this.result.root\n      while (!root[isClean]) {\n        root[isClean] = true\n        let stack = [toStack(root)]\n        while (stack.length > 0) {\n          let promise = this.visitTick(stack)\n          if (isPromise(promise)) {\n            try {\n              await promise\n            } catch (e) {\n              let node = stack[stack.length - 1].node\n              throw this.handleError(e, node)\n            }\n          }\n        }\n      }\n\n      if (this.listeners.OnceExit) {\n        for (let [plugin, visitor] of this.listeners.OnceExit) {\n          this.result.lastPlugin = plugin\n          try {\n            if (root.type === 'document') {\n              let roots = root.nodes.map(subRoot =>\n                visitor(subRoot, this.helpers)\n              )\n\n              await Promise.all(roots)\n            } else {\n              await visitor(root, this.helpers)\n            }\n          } catch (e) {\n            throw this.handleError(e)\n          }\n        }\n      }\n    }\n\n    this.processed = true\n    return this.stringify()\n  }\n\n  runOnRoot(plugin) {\n    this.result.lastPlugin = plugin\n    try {\n      if (typeof plugin === 'object' && plugin.Once) {\n        if (this.result.root.type === 'document') {\n          let roots = this.result.root.nodes.map(root =>\n            plugin.Once(root, this.helpers)\n          )\n\n          if (isPromise(roots[0])) {\n            return Promise.all(roots)\n          }\n\n          return roots\n        }\n\n        return plugin.Once(this.result.root, this.helpers)\n      } else if (typeof plugin === 'function') {\n        return plugin(this.result.root, this.result)\n      }\n    } catch (error) {\n      throw this.handleError(error)\n    }\n  }\n\n  stringify() {\n    if (this.error) throw this.error\n    if (this.stringified) return this.result\n    this.stringified = true\n\n    this.sync()\n\n    let opts = this.result.opts\n    let str = stringify\n    if (opts.syntax) str = opts.syntax.stringify\n    if (opts.stringifier) str = opts.stringifier\n    if (str.stringify) str = str.stringify\n\n    let map = new MapGenerator(str, this.result.root, this.result.opts)\n    let data = map.generate()\n    this.result.css = data[0]\n    this.result.map = data[1]\n\n    return this.result\n  }\n\n  sync() {\n    if (this.error) throw this.error\n    if (this.processed) return this.result\n    this.processed = true\n\n    if (this.processing) {\n      throw this.getAsyncError()\n    }\n\n    for (let plugin of this.plugins) {\n      let promise = this.runOnRoot(plugin)\n      if (isPromise(promise)) {\n        throw this.getAsyncError()\n      }\n    }\n\n    this.prepareVisitors()\n    if (this.hasListener) {\n      let root = this.result.root\n      while (!root[isClean]) {\n        root[isClean] = true\n        this.walkSync(root)\n      }\n      if (this.listeners.OnceExit) {\n        if (root.type === 'document') {\n          for (let subRoot of root.nodes) {\n            this.visitSync(this.listeners.OnceExit, subRoot)\n          }\n        } else {\n          this.visitSync(this.listeners.OnceExit, root)\n        }\n      }\n    }\n\n    return this.result\n  }\n\n  then(onFulfilled, onRejected) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!('from' in this.opts)) {\n        warnOnce(\n          'Without `from` option PostCSS could generate wrong source map ' +\n            'and will not find Browserslist config. Set it to CSS file path ' +\n            'or to `undefined` to prevent this warning.'\n        )\n      }\n    }\n    return this.async().then(onFulfilled, onRejected)\n  }\n\n  toString() {\n    return this.css\n  }\n\n  visitSync(visitors, node) {\n    for (let [plugin, visitor] of visitors) {\n      this.result.lastPlugin = plugin\n      let promise\n      try {\n        promise = visitor(node, this.helpers)\n      } catch (e) {\n        throw this.handleError(e, node.proxyOf)\n      }\n      if (node.type !== 'root' && node.type !== 'document' && !node.parent) {\n        return true\n      }\n      if (isPromise(promise)) {\n        throw this.getAsyncError()\n      }\n    }\n  }\n\n  visitTick(stack) {\n    let visit = stack[stack.length - 1]\n    let { node, visitors } = visit\n\n    if (node.type !== 'root' && node.type !== 'document' && !node.parent) {\n      stack.pop()\n      return\n    }\n\n    if (visitors.length > 0 && visit.visitorIndex < visitors.length) {\n      let [plugin, visitor] = visitors[visit.visitorIndex]\n      visit.visitorIndex += 1\n      if (visit.visitorIndex === visitors.length) {\n        visit.visitors = []\n        visit.visitorIndex = 0\n      }\n      this.result.lastPlugin = plugin\n      try {\n        return visitor(node.toProxy(), this.helpers)\n      } catch (e) {\n        throw this.handleError(e, node)\n      }\n    }\n\n    if (visit.iterator !== 0) {\n      let iterator = visit.iterator\n      let child\n      while ((child = node.nodes[node.indexes[iterator]])) {\n        node.indexes[iterator] += 1\n        if (!child[isClean]) {\n          child[isClean] = true\n          stack.push(toStack(child))\n          return\n        }\n      }\n      visit.iterator = 0\n      delete node.indexes[iterator]\n    }\n\n    let events = visit.events\n    while (visit.eventIndex < events.length) {\n      let event = events[visit.eventIndex]\n      visit.eventIndex += 1\n      if (event === CHILDREN) {\n        if (node.nodes && node.nodes.length) {\n          node[isClean] = true\n          visit.iterator = node.getIterator()\n        }\n        return\n      } else if (this.listeners[event]) {\n        visit.visitors = this.listeners[event]\n        return\n      }\n    }\n    stack.pop()\n  }\n\n  walkSync(node) {\n    node[isClean] = true\n    let events = getEvents(node)\n    for (let event of events) {\n      if (event === CHILDREN) {\n        if (node.nodes) {\n          node.each(child => {\n            if (!child[isClean]) this.walkSync(child)\n          })\n        }\n      } else {\n        let visitors = this.listeners[event]\n        if (visitors) {\n          if (this.visitSync(visitors, node.toProxy())) return\n        }\n      }\n    }\n  }\n\n  warnings() {\n    return this.sync().warnings()\n  }\n}\n\nLazyResult.registerPostcss = dependant => {\n  postcss = dependant\n}\n\nmodule.exports = LazyResult\nLazyResult.default = LazyResult\n\nRoot.registerLazyResult(LazyResult)\nDocument.registerLazyResult(LazyResult)\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;AACnB,IAAI;AAEJ,MAAM,qBAAqB;IACzB,QAAQ;IACR,SAAS;IACT,MAAM;IACN,UAAU;IACV,MAAM;IACN,MAAM;AACR;AAEA,MAAM,eAAe;IACnB,QAAQ;IACR,YAAY;IACZ,SAAS;IACT,aAAa;IACb,aAAa;IACb,iBAAiB;IACjB,UAAU;IACV,cAAc;IACd,MAAM;IACN,UAAU;IACV,eAAe;IACf,SAAS;IACT,MAAM;IACN,UAAU;IACV,MAAM;IACN,UAAU;AACZ;AAEA,MAAM,eAAe;IACnB,MAAM;IACN,eAAe;IACf,SAAS;AACX;AAEA,MAAM,WAAW;AAEjB,SAAS,UAAU,GAAG;IACpB,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,IAAI,KAAK;AACxD;AAEA,SAAS,UAAU,IAAI;IACrB,IAAI,MAAM;IACV,IAAI,OAAO,kBAAkB,CAAC,KAAK,IAAI,CAAC;IACxC,IAAI,KAAK,IAAI,KAAK,QAAQ;QACxB,MAAM,KAAK,IAAI,CAAC,WAAW;IAC7B,OAAO,IAAI,KAAK,IAAI,KAAK,UAAU;QACjC,MAAM,KAAK,IAAI,CAAC,WAAW;IAC7B;IAEA,IAAI,OAAO,KAAK,MAAM,EAAE;QACtB,OAAO;YACL;YACA,OAAO,MAAM;YACb;YACA,OAAO;YACP,OAAO,UAAU;SAClB;IACH,OAAO,IAAI,KAAK;QACd,OAAO;YAAC;YAAM,OAAO,MAAM;YAAK,OAAO;YAAQ,OAAO,UAAU;SAAI;IACtE,OAAO,IAAI,KAAK,MAAM,EAAE;QACtB,OAAO;YAAC;YAAM;YAAU,OAAO;SAAO;IACxC,OAAO;QACL,OAAO;YAAC;YAAM,OAAO;SAAO;IAC9B;AACF;AAEA,SAAS,QAAQ,IAAI;IACnB,IAAI;IACJ,IAAI,KAAK,IAAI,KAAK,YAAY;QAC5B,SAAS;YAAC;YAAY;YAAU;SAAe;IACjD,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;QAC/B,SAAS;YAAC;YAAQ;YAAU;SAAW;IACzC,OAAO;QACL,SAAS,UAAU;IACrB;IAEA,OAAO;QACL,YAAY;QACZ;QACA,UAAU;QACV;QACA,cAAc;QACd,UAAU,EAAE;IACd;AACF;AAEA,SAAS,WAAW,IAAI;IACtB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,KAAK,KAAK,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,CAAA,IAAK,WAAW;IACnD,OAAO;AACT;AAEA,IAAI,UAAU,CAAC;AAEf,MAAM;IACJ,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,GAAG,OAAO;IACjC;IAEA,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,GAAG,GAAG;IAC7B;IAEA,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,GAAG,GAAG;IAC7B;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI,GAAG,QAAQ;IAC7B;IAEA,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;IAC9B;IAEA,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI;IACzB;IAEA,IAAI,CAAC,OAAO,WAAW,CAAC,GAAG;QACzB,OAAO;IACT;IAEA,YAAY,SAAS,EAAE,GAAG,EAAE,IAAI,CAAE;QAChC,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI;QACJ,IACE,OAAO,QAAQ,YACf,QAAQ,QACR,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,UAAU,GAC/C;YACA,OAAO,WAAW;QACpB,OAAO,IAAI,eAAe,cAAc,eAAe,QAAQ;YAC7D,OAAO,WAAW,IAAI,IAAI;YAC1B,IAAI,IAAI,GAAG,EAAE;gBACX,IAAI,OAAO,KAAK,GAAG,KAAK,aAAa,KAAK,GAAG,GAAG,CAAC;gBACjD,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,MAAM,GAAG;gBACxC,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG;YACzB;QACF,OAAO;YACL,IAAI,SAAS;YACb,IAAI,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM,CAAC,KAAK;YAC3C,IAAI,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM;YACrC,IAAI,OAAO,KAAK,EAAE,SAAS,OAAO,KAAK;YAEvC,IAAI;gBACF,OAAO,OAAO,KAAK;YACrB,EAAE,OAAO,OAAO;gBACd,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,KAAK,GAAG;YACf;YAEA,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE;gBACrB,oBAAoB,GACpB,UAAU,OAAO,CAAC;YACpB;QACF;QAEA,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,WAAW,MAAM;QAC1C,IAAI,CAAC,OAAO,GAAG;YAAE,GAAG,OAAO;YAAE;YAAS,QAAQ,IAAI,CAAC,MAAM;QAAC;QAC1D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;YACxC,IAAI,OAAO,WAAW,YAAY,OAAO,OAAO,EAAE;gBAChD,OAAO;oBAAE,GAAG,MAAM;oBAAE,GAAG,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;gBAAC;YACrD,OAAO;gBACL,OAAO;YACT;QACF;IACF;IAEA,QAAQ;QACN,IAAI,IAAI,CAAC,KAAK,EAAE,OAAO,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK;QAChD,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM;QACtD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ;QACjC;QACA,OAAO,IAAI,CAAC,UAAU;IACxB;IAEA,MAAM,UAAU,EAAE;QAChB,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAC5B;IAEA,QAAQ,SAAS,EAAE;QACjB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW;IACtC;IAEA,gBAAgB;QACd,MAAM,IAAI,MAAM;IAClB;IAEA,YAAY,KAAK,EAAE,IAAI,EAAE;QACvB,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,UAAU;QACnC,IAAI;YACF,IAAI,MAAM,KAAK,UAAU,CAAC;YAC1B,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,MAAM,IAAI,KAAK,oBAAoB,CAAC,MAAM,MAAM,EAAE;gBACpD,MAAM,MAAM,GAAG,OAAO,aAAa;gBACnC,MAAM,UAAU;YAClB,OAAO,IAAI,OAAO,cAAc,EAAE;gBAChC,wCAA2C;oBACzC,IAAI,aAAa,OAAO,aAAa;oBACrC,IAAI,YAAY,OAAO,cAAc;oBACrC,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO;oBAC9C,IAAI,IAAI,UAAU,KAAK,CAAC;oBACxB,IAAI,IAAI,WAAW,KAAK,CAAC;oBAEzB,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,SAAS,CAAC,CAAC,EAAE,GAAG;wBACpD,sCAAsC;wBACtC,QAAQ,KAAK,CACX,6DACE,gBACA,aACA,WACA,aACA,WACA,YACA;oBAEN;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,oBAAoB,GACpB,sCAAsC;YACtC,IAAI,WAAW,QAAQ,KAAK,EAAE,QAAQ,KAAK,CAAC;QAC9C;QACA,OAAO;IACT;IAEA,kBAAkB;QAChB,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,MAAM,CAAC,QAAQ,MAAM;YACvB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE;YACpD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAC;gBAAQ;aAAG;QACxC;QACA,KAAK,IAAI,UAAU,IAAI,CAAC,OAAO,CAAE;YAC/B,IAAI,OAAO,WAAW,UAAU;gBAC9B,IAAK,IAAI,SAAS,OAAQ;oBACxB,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,QAAQ;wBAChD,MAAM,IAAI,MACR,CAAC,cAAc,EAAE,MAAM,IAAI,EAAE,OAAO,aAAa,CAAC,EAAE,CAAC,GACnD,CAAC,uBAAuB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;oBAE9D;oBACA,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;wBACxB,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,UAAU;4BACrC,IAAK,IAAI,UAAU,MAAM,CAAC,MAAM,CAAE;gCAChC,IAAI,WAAW,KAAK;oCAClB,IAAI,QAAQ,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO;gCAC1C,OAAO;oCACL,IACE,QACA,QAAQ,MAAM,OAAO,WAAW,IAChC,MAAM,CAAC,MAAM,CAAC,OAAO;gCAEzB;4BACF;wBACF,OAAO,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,YAAY;4BAC9C,IAAI,QAAQ,OAAO,MAAM,CAAC,MAAM;wBAClC;oBACF;gBACF;YACF;QACF;QACA,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,GAAG;IAC1D;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,MAAM,GAAG;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;YAC5C,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE;YAC5B,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC;YAC7B,IAAI,UAAU,UAAU;gBACtB,IAAI;oBACF,MAAM;gBACR,EAAE,OAAO,OAAO;oBACd,MAAM,IAAI,CAAC,WAAW,CAAC;gBACzB;YACF;QACF;QAEA,IAAI,CAAC,eAAe;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;YAC3B,MAAO,CAAC,IAAI,CAAC,QAAQ,CAAE;gBACrB,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,QAAQ;oBAAC,QAAQ;iBAAM;gBAC3B,MAAO,MAAM,MAAM,GAAG,EAAG;oBACvB,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC;oBAC7B,IAAI,UAAU,UAAU;wBACtB,IAAI;4BACF,MAAM;wBACR,EAAE,OAAO,GAAG;4BACV,IAAI,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,IAAI;4BACvC,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;wBAC5B;oBACF;gBACF;YACF;YAEA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;gBAC3B,KAAK,IAAI,CAAC,QAAQ,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAE;oBACrD,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG;oBACzB,IAAI;wBACF,IAAI,KAAK,IAAI,KAAK,YAAY;4BAC5B,IAAI,QAAQ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,UACzB,QAAQ,SAAS,IAAI,CAAC,OAAO;4BAG/B,MAAM,QAAQ,GAAG,CAAC;wBACpB,OAAO;4BACL,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAO;wBAClC;oBACF,EAAE,OAAO,GAAG;wBACV,MAAM,IAAI,CAAC,WAAW,CAAC;oBACzB;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA,UAAU,MAAM,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG;QACzB,IAAI;YACF,IAAI,OAAO,WAAW,YAAY,OAAO,IAAI,EAAE;gBAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY;oBACxC,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,OACrC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO;oBAGhC,IAAI,UAAU,KAAK,CAAC,EAAE,GAAG;wBACvB,OAAO,QAAQ,GAAG,CAAC;oBACrB;oBAEA,OAAO;gBACT;gBAEA,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO;YACnD,OAAO,IAAI,OAAO,WAAW,YAAY;gBACvC,OAAO,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,CAAC,WAAW,CAAC;QACzB;IACF;IAEA,YAAY;QACV,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,KAAK;QAChC,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,MAAM;QACxC,IAAI,CAAC,WAAW,GAAG;QAEnB,IAAI,CAAC,IAAI;QAET,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;QAC3B,IAAI,MAAM;QACV,IAAI,KAAK,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC,SAAS;QAC5C,IAAI,KAAK,WAAW,EAAE,MAAM,KAAK,WAAW;QAC5C,IAAI,IAAI,SAAS,EAAE,MAAM,IAAI,SAAS;QAEtC,IAAI,MAAM,IAAI,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QAClE,IAAI,OAAO,IAAI,QAAQ;QACvB,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;QAEzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,OAAO;QACL,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,KAAK;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,MAAM;QACtC,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,MAAM,IAAI,CAAC,aAAa;QAC1B;QAEA,KAAK,IAAI,UAAU,IAAI,CAAC,OAAO,CAAE;YAC/B,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC;YAC7B,IAAI,UAAU,UAAU;gBACtB,MAAM,IAAI,CAAC,aAAa;YAC1B;QACF;QAEA,IAAI,CAAC,eAAe;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;YAC3B,MAAO,CAAC,IAAI,CAAC,QAAQ,CAAE;gBACrB,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,QAAQ,CAAC;YAChB;YACA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;gBAC3B,IAAI,KAAK,IAAI,KAAK,YAAY;oBAC5B,KAAK,IAAI,WAAW,KAAK,KAAK,CAAE;wBAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;oBAC1C;gBACF,OAAO;oBACL,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;gBAC1C;YACF;QACF;QAEA,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,KAAK,WAAW,EAAE,UAAU,EAAE;QAC5B,wCAA2C;YACzC,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,IAAI,GAAG;gBAC1B,SACE,mEACE,oEACA;YAEN;QACF;QACA,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa;IACxC;IAEA,WAAW;QACT,OAAO,IAAI,CAAC,GAAG;IACjB;IAEA,UAAU,QAAQ,EAAE,IAAI,EAAE;QACxB,KAAK,IAAI,CAAC,QAAQ,QAAQ,IAAI,SAAU;YACtC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG;YACzB,IAAI;YACJ,IAAI;gBACF,UAAU,QAAQ,MAAM,IAAI,CAAC,OAAO;YACtC,EAAE,OAAO,GAAG;gBACV,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,OAAO;YACxC;YACA,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,cAAc,CAAC,KAAK,MAAM,EAAE;gBACpE,OAAO;YACT;YACA,IAAI,UAAU,UAAU;gBACtB,MAAM,IAAI,CAAC,aAAa;YAC1B;QACF;IACF;IAEA,UAAU,KAAK,EAAE;QACf,IAAI,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QACnC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;QAEzB,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,cAAc,CAAC,KAAK,MAAM,EAAE;YACpE,MAAM,GAAG;YACT;QACF;QAEA,IAAI,SAAS,MAAM,GAAG,KAAK,MAAM,YAAY,GAAG,SAAS,MAAM,EAAE;YAC/D,IAAI,CAAC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,MAAM,YAAY,CAAC;YACpD,MAAM,YAAY,IAAI;YACtB,IAAI,MAAM,YAAY,KAAK,SAAS,MAAM,EAAE;gBAC1C,MAAM,QAAQ,GAAG,EAAE;gBACnB,MAAM,YAAY,GAAG;YACvB;YACA,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG;YACzB,IAAI;gBACF,OAAO,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO;YAC7C,EAAE,OAAO,GAAG;gBACV,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG;YAC5B;QACF;QAEA,IAAI,MAAM,QAAQ,KAAK,GAAG;YACxB,IAAI,WAAW,MAAM,QAAQ;YAC7B,IAAI;YACJ,MAAQ,QAAQ,KAAK,KAAK,CAAC,KAAK,OAAO,CAAC,SAAS,CAAC,CAAG;gBACnD,KAAK,OAAO,CAAC,SAAS,IAAI;gBAC1B,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;oBACnB,KAAK,CAAC,QAAQ,GAAG;oBACjB,MAAM,IAAI,CAAC,QAAQ;oBACnB;gBACF;YACF;YACA,MAAM,QAAQ,GAAG;YACjB,OAAO,KAAK,OAAO,CAAC,SAAS;QAC/B;QAEA,IAAI,SAAS,MAAM,MAAM;QACzB,MAAO,MAAM,UAAU,GAAG,OAAO,MAAM,CAAE;YACvC,IAAI,QAAQ,MAAM,CAAC,MAAM,UAAU,CAAC;YACpC,MAAM,UAAU,IAAI;YACpB,IAAI,UAAU,UAAU;gBACtB,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE;oBACnC,IAAI,CAAC,QAAQ,GAAG;oBAChB,MAAM,QAAQ,GAAG,KAAK,WAAW;gBACnC;gBACA;YACF,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;gBACtC;YACF;QACF;QACA,MAAM,GAAG;IACX;IAEA,SAAS,IAAI,EAAE;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,SAAS,UAAU;QACvB,KAAK,IAAI,SAAS,OAAQ;YACxB,IAAI,UAAU,UAAU;gBACtB,IAAI,KAAK,KAAK,EAAE;oBACd,KAAK,IAAI,CAAC,CAAA;wBACR,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;oBACrC;gBACF;YACF,OAAO;gBACL,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM;gBACpC,IAAI,UAAU;oBACZ,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,OAAO,KAAK;gBAChD;YACF;QACF;IACF;IAEA,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,GAAG,QAAQ;IAC7B;AACF;AAEA,WAAW,eAAe,GAAG,CAAA;IAC3B,UAAU;AACZ;AAEA,OAAO,OAAO,GAAG;AACjB,WAAW,OAAO,GAAG;AAErB,KAAK,kBAAkB,CAAC;AACxB,SAAS,kBAAkB,CAAC", "ignoreList": [0]}}, {"offset": {"line": 6527, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/no-work-result.js"], "sourcesContent": ["'use strict'\n\nlet MapGenerator = require('./map-generator')\nlet parse = require('./parse')\nconst Result = require('./result')\nlet stringify = require('./stringify')\nlet warnOnce = require('./warn-once')\n\nclass NoWorkResult {\n  get content() {\n    return this.result.css\n  }\n\n  get css() {\n    return this.result.css\n  }\n\n  get map() {\n    return this.result.map\n  }\n\n  get messages() {\n    return []\n  }\n\n  get opts() {\n    return this.result.opts\n  }\n\n  get processor() {\n    return this.result.processor\n  }\n\n  get root() {\n    if (this._root) {\n      return this._root\n    }\n\n    let root\n    let parser = parse\n\n    try {\n      root = parser(this._css, this._opts)\n    } catch (error) {\n      this.error = error\n    }\n\n    if (this.error) {\n      throw this.error\n    } else {\n      this._root = root\n      return root\n    }\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'NoWorkResult'\n  }\n\n  constructor(processor, css, opts) {\n    css = css.toString()\n    this.stringified = false\n\n    this._processor = processor\n    this._css = css\n    this._opts = opts\n    this._map = undefined\n    let root\n\n    let str = stringify\n    this.result = new Result(this._processor, root, this._opts)\n    this.result.css = css\n\n    let self = this\n    Object.defineProperty(this.result, 'root', {\n      get() {\n        return self.root\n      }\n    })\n\n    let map = new MapGenerator(str, root, this._opts, css)\n    if (map.isMap()) {\n      let [generatedCSS, generatedMap] = map.generate()\n      if (generatedCSS) {\n        this.result.css = generatedCSS\n      }\n      if (generatedMap) {\n        this.result.map = generatedMap\n      }\n    } else {\n      map.clearAnnotation()\n      this.result.css = map.css\n    }\n  }\n\n  async() {\n    if (this.error) return Promise.reject(this.error)\n    return Promise.resolve(this.result)\n  }\n\n  catch(onRejected) {\n    return this.async().catch(onRejected)\n  }\n\n  finally(onFinally) {\n    return this.async().then(onFinally, onFinally)\n  }\n\n  sync() {\n    if (this.error) throw this.error\n    return this.result\n  }\n\n  then(onFulfilled, onRejected) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!('from' in this._opts)) {\n        warnOnce(\n          'Without `from` option PostCSS could generate wrong source map ' +\n            'and will not find Browserslist config. Set it to CSS file path ' +\n            'or to `undefined` to prevent this warning.'\n        )\n      }\n    }\n\n    return this.async().then(onFulfilled, onRejected)\n  }\n\n  toString() {\n    return this._css\n  }\n\n  warnings() {\n    return []\n  }\n}\n\nmodule.exports = NoWorkResult\nNoWorkResult.default = NoWorkResult\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,MAAM;AACN,IAAI;AACJ,IAAI;AAEJ,MAAM;IACJ,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG;IACxB;IAEA,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG;IACxB;IAEA,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG;IACxB;IAEA,IAAI,WAAW;QACb,OAAO,EAAE;IACX;IAEA,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;IAC9B;IAEA,IAAI,OAAO;QACT,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,KAAK;QACnB;QAEA,IAAI;QACJ,IAAI,SAAS;QAEb,IAAI;YACF,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK;QACrC,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,KAAK,GAAG;QACf;QAEA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,MAAM,IAAI,CAAC,KAAK;QAClB,OAAO;YACL,IAAI,CAAC,KAAK,GAAG;YACb,OAAO;QACT;IACF;IAEA,IAAI,CAAC,OAAO,WAAW,CAAC,GAAG;QACzB,OAAO;IACT;IAEA,YAAY,SAAS,EAAE,GAAG,EAAE,IAAI,CAAE;QAChC,MAAM,IAAI,QAAQ;QAClB,IAAI,CAAC,WAAW,GAAG;QAEnB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI;QAEJ,IAAI,MAAM;QACV,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,KAAK;QAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG;QAElB,IAAI,OAAO,IAAI;QACf,OAAO,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;YACzC;gBACE,OAAO,KAAK,IAAI;YAClB;QACF;QAEA,IAAI,MAAM,IAAI,aAAa,KAAK,MAAM,IAAI,CAAC,KAAK,EAAE;QAClD,IAAI,IAAI,KAAK,IAAI;YACf,IAAI,CAAC,cAAc,aAAa,GAAG,IAAI,QAAQ;YAC/C,IAAI,cAAc;gBAChB,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG;YACpB;YACA,IAAI,cAAc;gBAChB,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG;YACpB;QACF,OAAO;YACL,IAAI,eAAe;YACnB,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG;QAC3B;IACF;IAEA,QAAQ;QACN,IAAI,IAAI,CAAC,KAAK,EAAE,OAAO,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK;QAChD,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM;IACpC;IAEA,MAAM,UAAU,EAAE;QAChB,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAC5B;IAEA,QAAQ,SAAS,EAAE;QACjB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW;IACtC;IAEA,OAAO;QACL,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,KAAK;QAChC,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,KAAK,WAAW,EAAE,UAAU,EAAE;QAC5B,wCAA2C;YACzC,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,KAAK,GAAG;gBAC3B,SACE,mEACE,oEACA;YAEN;QACF;QAEA,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa;IACxC;IAEA,WAAW;QACT,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA,WAAW;QACT,OAAO,EAAE;IACX;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,aAAa,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 6640, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/processor.js"], "sourcesContent": ["'use strict'\n\nlet Document = require('./document')\nlet LazyResult = require('./lazy-result')\nlet NoWorkResult = require('./no-work-result')\nlet Root = require('./root')\n\nclass Processor {\n  constructor(plugins = []) {\n    this.version = '8.5.6'\n    this.plugins = this.normalize(plugins)\n  }\n\n  normalize(plugins) {\n    let normalized = []\n    for (let i of plugins) {\n      if (i.postcss === true) {\n        i = i()\n      } else if (i.postcss) {\n        i = i.postcss\n      }\n\n      if (typeof i === 'object' && Array.isArray(i.plugins)) {\n        normalized = normalized.concat(i.plugins)\n      } else if (typeof i === 'object' && i.postcssPlugin) {\n        normalized.push(i)\n      } else if (typeof i === 'function') {\n        normalized.push(i)\n      } else if (typeof i === 'object' && (i.parse || i.stringify)) {\n        if (process.env.NODE_ENV !== 'production') {\n          throw new Error(\n            'PostCSS syntaxes cannot be used as plugins. Instead, please use ' +\n              'one of the syntax/parser/stringifier options as outlined ' +\n              'in your PostCSS runner documentation.'\n          )\n        }\n      } else {\n        throw new Error(i + ' is not a PostCSS plugin')\n      }\n    }\n    return normalized\n  }\n\n  process(css, opts = {}) {\n    if (\n      !this.plugins.length &&\n      !opts.parser &&\n      !opts.stringifier &&\n      !opts.syntax\n    ) {\n      return new NoWorkResult(this, css, opts)\n    } else {\n      return new LazyResult(this, css, opts)\n    }\n  }\n\n  use(plugin) {\n    this.plugins = this.plugins.concat(this.normalize([plugin]))\n    return this\n  }\n}\n\nmodule.exports = Processor\nProcessor.default = Processor\n\nRoot.registerProcessor(Processor)\nDocument.registerProcessor(Processor)\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM;IACJ,YAAY,UAAU,EAAE,CAAE;QACxB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;IAChC;IAEA,UAAU,OAAO,EAAE;QACjB,IAAI,aAAa,EAAE;QACnB,KAAK,IAAI,KAAK,QAAS;YACrB,IAAI,EAAE,OAAO,KAAK,MAAM;gBACtB,IAAI;YACN,OAAO,IAAI,EAAE,OAAO,EAAE;gBACpB,IAAI,EAAE,OAAO;YACf;YAEA,IAAI,OAAO,MAAM,YAAY,MAAM,OAAO,CAAC,EAAE,OAAO,GAAG;gBACrD,aAAa,WAAW,MAAM,CAAC,EAAE,OAAO;YAC1C,OAAO,IAAI,OAAO,MAAM,YAAY,EAAE,aAAa,EAAE;gBACnD,WAAW,IAAI,CAAC;YAClB,OAAO,IAAI,OAAO,MAAM,YAAY;gBAClC,WAAW,IAAI,CAAC;YAClB,OAAO,IAAI,OAAO,MAAM,YAAY,CAAC,EAAE,KAAK,IAAI,EAAE,SAAS,GAAG;gBAC5D,wCAA2C;oBACzC,MAAM,IAAI,MACR,qEACE,8DACA;gBAEN;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,IAAI;YACtB;QACF;QACA,OAAO;IACT;IAEA,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QACtB,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IACpB,CAAC,KAAK,MAAM,IACZ,CAAC,KAAK,WAAW,IACjB,CAAC,KAAK,MAAM,EACZ;YACA,OAAO,IAAI,aAAa,IAAI,EAAE,KAAK;QACrC,OAAO;YACL,OAAO,IAAI,WAAW,IAAI,EAAE,KAAK;QACnC;IACF;IAEA,IAAI,MAAM,EAAE;QACV,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAAC;SAAO;QAC1D,OAAO,IAAI;IACb;AACF;AAEA,OAAO,OAAO,GAAG;AACjB,UAAU,OAAO,GAAG;AAEpB,KAAK,iBAAiB,CAAC;AACvB,SAAS,iBAAiB,CAAC", "ignoreList": [0]}}, {"offset": {"line": 6697, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/postcss.js"], "sourcesContent": ["'use strict'\n\nlet AtRule = require('./at-rule')\nlet Comment = require('./comment')\nlet Container = require('./container')\nlet CssSyntaxError = require('./css-syntax-error')\nlet Declaration = require('./declaration')\nlet Document = require('./document')\nlet fromJSON = require('./fromJSON')\nlet Input = require('./input')\nlet LazyResult = require('./lazy-result')\nlet list = require('./list')\nlet Node = require('./node')\nlet parse = require('./parse')\nlet Processor = require('./processor')\nlet Result = require('./result.js')\nlet Root = require('./root')\nlet Rule = require('./rule')\nlet stringify = require('./stringify')\nlet Warning = require('./warning')\n\nfunction postcss(...plugins) {\n  if (plugins.length === 1 && Array.isArray(plugins[0])) {\n    plugins = plugins[0]\n  }\n  return new Processor(plugins)\n}\n\npostcss.plugin = function plugin(name, initializer) {\n  let warningPrinted = false\n  function creator(...args) {\n    // eslint-disable-next-line no-console\n    if (console && console.warn && !warningPrinted) {\n      warningPrinted = true\n      // eslint-disable-next-line no-console\n      console.warn(\n        name +\n          ': postcss.plugin was deprecated. Migration guide:\\n' +\n          'https://evilmartians.com/chronicles/postcss-8-plugin-migration'\n      )\n      if (process.env.LANG && process.env.LANG.startsWith('cn')) {\n        /* c8 ignore next 7 */\n        // eslint-disable-next-line no-console\n        console.warn(\n          name +\n            ': 里面 postcss.plugin 被弃用. 迁移指南:\\n' +\n            'https://www.w3ctech.com/topic/2226'\n        )\n      }\n    }\n    let transformer = initializer(...args)\n    transformer.postcssPlugin = name\n    transformer.postcssVersion = new Processor().version\n    return transformer\n  }\n\n  let cache\n  Object.defineProperty(creator, 'postcss', {\n    get() {\n      if (!cache) cache = creator()\n      return cache\n    }\n  })\n\n  creator.process = function (css, processOpts, pluginOpts) {\n    return postcss([creator(pluginOpts)]).process(css, processOpts)\n  }\n\n  return creator\n}\n\npostcss.stringify = stringify\npostcss.parse = parse\npostcss.fromJSON = fromJSON\npostcss.list = list\n\npostcss.comment = defaults => new Comment(defaults)\npostcss.atRule = defaults => new AtRule(defaults)\npostcss.decl = defaults => new Declaration(defaults)\npostcss.rule = defaults => new Rule(defaults)\npostcss.root = defaults => new Root(defaults)\npostcss.document = defaults => new Document(defaults)\n\npostcss.CssSyntaxError = CssSyntaxError\npostcss.Declaration = Declaration\npostcss.Container = Container\npostcss.Processor = Processor\npostcss.Document = Document\npostcss.Comment = Comment\npostcss.Warning = Warning\npostcss.AtRule = AtRule\npostcss.Result = Result\npostcss.Input = Input\npostcss.Rule = Rule\npostcss.Root = Root\npostcss.Node = Node\n\nLazyResult.registerPostcss(postcss)\n\nmodule.exports = postcss\npostcss.default = postcss\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS,QAAQ,GAAG,OAAO;IACzB,IAAI,QAAQ,MAAM,KAAK,KAAK,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG;QACrD,UAAU,OAAO,CAAC,EAAE;IACtB;IACA,OAAO,IAAI,UAAU;AACvB;AAEA,QAAQ,MAAM,GAAG,SAAS,OAAO,IAAI,EAAE,WAAW;IAChD,IAAI,iBAAiB;IACrB,SAAS,QAAQ,GAAG,IAAI;QACtB,sCAAsC;QACtC,IAAI,WAAW,QAAQ,IAAI,IAAI,CAAC,gBAAgB;YAC9C,iBAAiB;YACjB,sCAAsC;YACtC,QAAQ,IAAI,CACV,OACE,wDACA;YAEJ,IAAI,QAAQ,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO;gBACzD,oBAAoB,GACpB,sCAAsC;gBACtC,QAAQ,IAAI,CACV,OACE,qCACA;YAEN;QACF;QACA,IAAI,cAAc,eAAe;QACjC,YAAY,aAAa,GAAG;QAC5B,YAAY,cAAc,GAAG,IAAI,YAAY,OAAO;QACpD,OAAO;IACT;IAEA,IAAI;IACJ,OAAO,cAAc,CAAC,SAAS,WAAW;QACxC;YACE,IAAI,CAAC,OAAO,QAAQ;YACpB,OAAO;QACT;IACF;IAEA,QAAQ,OAAO,GAAG,SAAU,GAAG,EAAE,WAAW,EAAE,UAAU;QACtD,OAAO,QAAQ;YAAC,QAAQ;SAAY,EAAE,OAAO,CAAC,KAAK;IACrD;IAEA,OAAO;AACT;AAEA,QAAQ,SAAS,GAAG;AACpB,QAAQ,KAAK,GAAG;AAChB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,IAAI,GAAG;AAEf,QAAQ,OAAO,GAAG,CAAA,WAAY,IAAI,QAAQ;AAC1C,QAAQ,MAAM,GAAG,CAAA,WAAY,IAAI,OAAO;AACxC,QAAQ,IAAI,GAAG,CAAA,WAAY,IAAI,YAAY;AAC3C,QAAQ,IAAI,GAAG,CAAA,WAAY,IAAI,KAAK;AACpC,QAAQ,IAAI,GAAG,CAAA,WAAY,IAAI,KAAK;AACpC,QAAQ,QAAQ,GAAG,CAAA,WAAY,IAAI,SAAS;AAE5C,QAAQ,cAAc,GAAG;AACzB,QAAQ,WAAW,GAAG;AACtB,QAAQ,SAAS,GAAG;AACpB,QAAQ,SAAS,GAAG;AACpB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,OAAO,GAAG;AAClB,QAAQ,OAAO,GAAG;AAClB,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG;AACjB,QAAQ,KAAK,GAAG;AAChB,QAAQ,IAAI,GAAG;AACf,QAAQ,IAAI,GAAG;AACf,QAAQ,IAAI,GAAG;AAEf,WAAW,eAAe,CAAC;AAE3B,OAAO,OAAO,GAAG;AACjB,QAAQ,OAAO,GAAG", "ignoreList": [0]}}, {"offset": {"line": 6786, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/postcss/lib/postcss.mjs"], "sourcesContent": ["import postcss from './postcss.js'\n\nexport default postcss\n\nexport const stringify = postcss.stringify\nexport const fromJSON = postcss.fromJSON\nexport const plugin = postcss.plugin\nexport const parse = postcss.parse\nexport const list = postcss.list\n\nexport const document = postcss.document\nexport const comment = postcss.comment\nexport const atRule = postcss.atRule\nexport const rule = postcss.rule\nexport const decl = postcss.decl\nexport const root = postcss.root\n\nexport const CssSyntaxError = postcss.CssSyntaxError\nexport const Declaration = postcss.Declaration\nexport const Container = postcss.Container\nexport const Processor = postcss.Processor\nexport const Document = postcss.Document\nexport const Comment = postcss.Comment\nexport const Warning = postcss.Warning\nexport const AtRule = postcss.AtRule\nexport const Result = postcss.Result\nexport const Input = postcss.Input\nexport const Rule = postcss.Rule\nexport const Root = postcss.Root\nexport const Node = postcss.Node\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;uCAEe,sIAAA,CAAA,UAAO;AAEf,MAAM,YAAY,sIAAA,CAAA,UAAO,CAAC,SAAS;AACnC,MAAM,WAAW,sIAAA,CAAA,UAAO,CAAC,QAAQ;AACjC,MAAM,SAAS,sIAAA,CAAA,UAAO,CAAC,MAAM;AAC7B,MAAM,QAAQ,sIAAA,CAAA,UAAO,CAAC,KAAK;AAC3B,MAAM,OAAO,sIAAA,CAAA,UAAO,CAAC,IAAI;AAEzB,MAAM,WAAW,sIAAA,CAAA,UAAO,CAAC,QAAQ;AACjC,MAAM,UAAU,sIAAA,CAAA,UAAO,CAAC,OAAO;AAC/B,MAAM,SAAS,sIAAA,CAAA,UAAO,CAAC,MAAM;AAC7B,MAAM,OAAO,sIAAA,CAAA,UAAO,CAAC,IAAI;AACzB,MAAM,OAAO,sIAAA,CAAA,UAAO,CAAC,IAAI;AACzB,MAAM,OAAO,sIAAA,CAAA,UAAO,CAAC,IAAI;AAEzB,MAAM,iBAAiB,sIAAA,CAAA,UAAO,CAAC,cAAc;AAC7C,MAAM,cAAc,sIAAA,CAAA,UAAO,CAAC,WAAW;AACvC,MAAM,YAAY,sIAAA,CAAA,UAAO,CAAC,SAAS;AACnC,MAAM,YAAY,sIAAA,CAAA,UAAO,CAAC,SAAS;AACnC,MAAM,WAAW,sIAAA,CAAA,UAAO,CAAC,QAAQ;AACjC,MAAM,UAAU,sIAAA,CAAA,UAAO,CAAC,OAAO;AAC/B,MAAM,UAAU,sIAAA,CAAA,UAAO,CAAC,OAAO;AAC/B,MAAM,SAAS,sIAAA,CAAA,UAAO,CAAC,MAAM;AAC7B,MAAM,SAAS,sIAAA,CAAA,UAAO,CAAC,MAAM;AAC7B,MAAM,QAAQ,sIAAA,CAAA,UAAO,CAAC,KAAK;AAC3B,MAAM,OAAO,sIAAA,CAAA,UAAO,CAAC,IAAI;AACzB,MAAM,OAAO,sIAAA,CAAA,UAAO,CAAC,IAAI;AACzB,MAAM,OAAO,sIAAA,CAAA,UAAO,CAAC,IAAI", "ignoreList": [0]}}]}