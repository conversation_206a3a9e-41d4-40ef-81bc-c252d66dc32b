{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iPACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/lib/auth/client.ts"], "sourcesContent": ["import { createAuthClient } from \"better-auth/react\"\n\nexport const authClient = createAuthClient({\n  baseURL: process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\",\n})\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,SAAS,6DAAmC;AAC9C", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/auth/login-form.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useRouter } from \"next/navigation\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { authClient } from \"@/lib/auth/client\"\nimport { Eye, EyeOff, Loader2 } from \"lucide-react\"\n\nexport function LoginForm() {\n  const [email, setEmail] = useState(\"\")\n  const [password, setPassword] = useState(\"\")\n  const [showPassword, setShowPassword] = useState(false)\n  const [rememberMe, setRememberMe] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState(\"\")\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError(\"\")\n\n    try {\n      const { data, error } = await authClient.signIn.email({\n        email,\n        password,\n        rememberMe,\n      })\n\n      if (error) {\n        setError(error.message || \"Failed to sign in\")\n      } else {\n        router.push(\"/study\")\n      }\n    } catch (err) {\n      setError(\"An unexpected error occurred\")\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      {error && (\n        <div className=\"p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md\">\n          {error}\n        </div>\n      )}\n\n      <div className=\"space-y-2\">\n        <label htmlFor=\"email\" className=\"text-sm font-medium\">\n          Email or Username\n        </label>\n        <Input\n          id=\"email\"\n          type=\"email\"\n          placeholder=\"Enter your email or username\"\n          value={email}\n          onChange={(e) => setEmail(e.target.value)}\n          required\n        />\n      </div>\n\n      <div className=\"space-y-2\">\n        <label htmlFor=\"password\" className=\"text-sm font-medium\">\n          Password\n        </label>\n        <div className=\"relative\">\n          <Input\n            id=\"password\"\n            type={showPassword ? \"text\" : \"password\"}\n            placeholder=\"Enter your password\"\n            value={password}\n            onChange={(e) => setPassword(e.target.value)}\n            required\n          />\n          <Button\n            type=\"button\"\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\n            onClick={() => setShowPassword(!showPassword)}\n          >\n            {showPassword ? (\n              <EyeOff className=\"h-4 w-4\" />\n            ) : (\n              <Eye className=\"h-4 w-4\" />\n            )}\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-2\">\n          <input\n            id=\"remember\"\n            type=\"checkbox\"\n            checked={rememberMe}\n            onChange={(e) => setRememberMe(e.target.checked)}\n            className=\"h-4 w-4 rounded border-border\"\n          />\n          <label htmlFor=\"remember\" className=\"text-sm\">\n            Remember me\n          </label>\n        </div>\n        <Button variant=\"link\" className=\"px-0 text-sm\">\n          Forgot password?\n        </Button>\n      </div>\n\n      <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n        {isLoading ? (\n          <>\n            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n            Signing in...\n          </>\n        ) : (\n          \"Sign In\"\n        )}\n      </Button>\n    </form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,4HAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,CAAC;gBACpD;gBACA;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;YACrC,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAQ,WAAU;kCAAsB;;;;;;kCAGvD,8OAAC,iIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,QAAQ;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAW,WAAU;kCAAsB;;;;;;kCAG1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,eAAe,SAAS;gCAC9B,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,QAAQ;;;;;;0CAEV,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,gBAAgB,CAAC;0CAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAElB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;gCAC/C,WAAU;;;;;;0CAEZ,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAAU;;;;;;;;;;;;kCAIhD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAO,WAAU;kCAAe;;;;;;;;;;;;0BAKlD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,WAAU;gBAAS,UAAU;0BAChD,0BACC;;sCACE,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAA8B;;mCAInD;;;;;;;;;;;;AAKV", "debugId": null}}]}