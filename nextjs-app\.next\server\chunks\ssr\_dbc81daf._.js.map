{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/auth/login-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/login-form.tsx <module evaluation>\",\n    \"LoginForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/auth/login-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoginForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/login-form.tsx\",\n    \"LoginForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gDACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iPACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/auth/auth-layout.tsx"], "sourcesContent": ["import Image from \"next/image\"\nimport Link from \"next/link\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Search, StickyNote, Brain, BookOpen } from \"lucide-react\"\n\ninterface AuthLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle: string\n  showSocialLogin?: boolean\n  footerText?: string\n  footerLinkText?: string\n  footerLinkHref?: string\n}\n\nexport function AuthLayout({\n  children,\n  title,\n  subtitle,\n  showSocialLogin = false,\n  footerText,\n  footerLinkText,\n  footerLinkHref,\n}: AuthLayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b border-border\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <Image\n              src=\"/logo.png\"\n              alt=\"Tadabbur AI\"\n              width={32}\n              height={32}\n              className=\"rounded\"\n            />\n            <span className=\"text-xl font-semibold\"><PERSON><PERSON><PERSON> AI</span>\n          </Link>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto\">\n          {/* Auth Form */}\n          <div className=\"flex items-center justify-center\">\n            <div className=\"w-full max-w-md space-y-6\">\n              {/* Form Header */}\n              <div className=\"text-center space-y-2\">\n                <h1 className=\"text-2xl font-bold\">{title}</h1>\n                <p className=\"text-muted-foreground\">{subtitle}</p>\n              </div>\n\n              {/* Form */}\n              {children}\n\n              {/* Social Login */}\n              {showSocialLogin && (\n                <>\n                  <div className=\"relative\">\n                    <div className=\"absolute inset-0 flex items-center\">\n                      <span className=\"w-full border-t border-border\" />\n                    </div>\n                    <div className=\"relative flex justify-center text-xs uppercase\">\n                      <span className=\"bg-background px-2 text-muted-foreground\">or</span>\n                    </div>\n                  </div>\n\n                  <Button variant=\"outline\" className=\"w-full\" disabled>\n                    <svg className=\"mr-2 h-4 w-4\" viewBox=\"0 0 24 24\">\n                      <path\n                        d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                        fill=\"#4285F4\"\n                      />\n                      <path\n                        d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                        fill=\"#34A853\"\n                      />\n                      <path\n                        d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                        fill=\"#FBBC05\"\n                      />\n                      <path\n                        d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                        fill=\"#EA4335\"\n                      />\n                    </svg>\n                    Continue with Google\n                  </Button>\n                </>\n              )}\n\n              {/* Footer */}\n              {footerText && footerLinkText && footerLinkHref && (\n                <div className=\"text-center text-sm\">\n                  <span className=\"text-muted-foreground\">{footerText} </span>\n                  <Link href={footerLinkHref} className=\"text-primary hover:underline\">\n                    {footerLinkText}\n                  </Link>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Side Info */}\n          <div className=\"hidden lg:flex items-center justify-center\">\n            <div className=\"max-w-md space-y-6\">\n              <h2 className=\"text-2xl font-bold\">Deepen Your Understanding</h2>\n              <p className=\"text-muted-foreground\">\n                Join thousands of Muslims exploring the Quran with AI-powered insights,\n                personalized study notes, and interactive learning tools.\n              </p>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <Search className=\"h-5 w-5 text-primary\" />\n                  <span className=\"text-sm\">Intelligent Quranic search</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <StickyNote className=\"h-5 w-5 text-primary\" />\n                  <span className=\"text-sm\">Personalized study notes</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <Brain className=\"h-5 w-5 text-primary\" />\n                  <span className=\"text-sm\">Interactive learning tools</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <BookOpen className=\"h-5 w-5 text-primary\" />\n                  <span className=\"text-sm\">Contextual explanations</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAYO,SAAS,WAAW,EACzB,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,kBAAkB,KAAK,EACvB,UAAU,EACV,cAAc,EACd,cAAc,EACE;IAChB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAM9C,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;oCAIvC;oCAGA,iCACC;;0DACE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;;;;;;0DAI/D,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;gDAAS,QAAQ;;kEACnD,8OAAC;wDAAI,WAAU;wDAAe,SAAQ;;0EACpC,8OAAC;gEACC,GAAE;gEACF,MAAK;;;;;;0EAEP,8OAAC;gEACC,GAAE;gEACF,MAAK;;;;;;0EAEP,8OAAC;gEACC,GAAE;gEACF,MAAK;;;;;;0EAEP,8OAAC;gEACC,GAAE;gEACF,MAAK;;;;;;;;;;;;oDAEH;;;;;;;;;oCAOX,cAAc,kBAAkB,gCAC/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDAAyB;oDAAW;;;;;;;0DACpD,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM;gDAAgB,WAAU;0DACnC;;;;;;;;;;;;;;;;;;;;;;;sCAQX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/app/auth/login/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\"\nimport { LoginForm } from \"@/components/auth/login-form\"\nimport { AuthLayout } from \"@/components/auth/auth-layout\"\n\nexport const metadata: Metadata = {\n  title: \"Login\",\n  description: \"Sign in to your Tadabbur AI account\",\n}\n\nexport default function LoginPage() {\n  return (\n    <AuthLayout\n      title=\"Welcome Back\"\n      subtitle=\"Sign in to continue your Quranic journey\"\n      showSocialLogin={true}\n      footerText=\"Don't have an account?\"\n      footerLinkText=\"Create one here\"\n      footerLinkHref=\"/auth/register\"\n    >\n      <LoginForm />\n    </AuthLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC,4IAAA,CAAA,aAAU;QACT,OAAM;QACN,UAAS;QACT,iBAAiB;QACjB,YAAW;QACX,gBAAe;QACf,gBAAe;kBAEf,cAAA,8OAAC,2IAAA,CAAA,YAAS;;;;;;;;;;AAGhB", "debugId": null}}]}