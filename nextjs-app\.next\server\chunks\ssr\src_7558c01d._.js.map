{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/loading/loading-spinner.tsx"], "sourcesContent": ["import { Loader2 } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n  text?: string\n}\n\nexport function LoadingSpinner({ size = \"md\", className, text }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\",\n    lg: \"h-8 w-8\"\n  }\n\n  return (\n    <div className={cn(\"flex items-center justify-center\", className)}>\n      <div className=\"flex flex-col items-center space-y-2\">\n        <Loader2 className={cn(\"animate-spin text-primary\", sizeClasses[size])} />\n        {text && (\n          <p className=\"text-sm text-muted-foreground\">{text}</p>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport function PageLoading() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <LoadingSpinner size=\"lg\" text=\"Loading...\" />\n    </div>\n  )\n}\n\nexport function ComponentLoading({ className }: { className?: string }) {\n  return (\n    <div className={cn(\"flex items-center justify-center p-8\", className)}>\n      <LoadingSpinner text=\"Loading...\" />\n    </div>\n  )\n}\n\nexport function InlineLoading({ text = \"Loading...\" }: { text?: string }) {\n  return (\n    <div className=\"flex items-center space-x-2\">\n      <Loader2 className=\"h-4 w-4 animate-spin\" />\n      <span className=\"text-sm text-muted-foreground\">{text}</span>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAQO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAuB;IAClF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B,WAAW,CAAC,KAAK;;;;;;gBACpE,sBACC,8OAAC;oBAAE,WAAU;8BAAiC;;;;;;;;;;;;;;;;;AAKxD;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAe,MAAK;YAAK,MAAK;;;;;;;;;;;AAGrC;AAEO,SAAS,iBAAiB,EAAE,SAAS,EAA0B;IACpE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;kBACzD,cAAA,8OAAC;YAAe,MAAK;;;;;;;;;;;AAG3B;AAEO,SAAS,cAAc,EAAE,OAAO,YAAY,EAAqB;IACtE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BACnB,8OAAC;gBAAK,WAAU;0BAAiC;;;;;;;;;;;;AAGvD", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/app/loading.tsx"], "sourcesContent": ["import { PageLoading } from \"@/components/loading/loading-spinner\"\n\nexport default function Loading() {\n  return <PageLoading />\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,mJAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}]}