(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/node_modules_8834b263._.js", {

"[project]/node_modules/uncrypto/dist/crypto.web.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>_crypto),
    "getRandomValues": (()=>getRandomValues),
    "randomUUID": (()=>randomUUID),
    "subtle": (()=>subtle)
});
const webCrypto = globalThis.crypto;
const subtle = webCrypto.subtle;
const randomUUID = ()=>{
    return webCrypto.randomUUID();
};
const getRandomValues = (array)=>{
    return webCrypto.getRandomValues(array);
};
const _crypto = {
    randomUUID,
    getRandomValues,
    subtle
};
;
}}),
"[project]/node_modules/rou3/dist/index.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addRoute": (()=>addRoute),
    "createRouter": (()=>createRouter),
    "findAllRoutes": (()=>findAllRoutes),
    "findRoute": (()=>findRoute),
    "removeRoute": (()=>removeRoute)
});
const EmptyObject = /* @__PURE__ */ (()=>{
    const C = function() {};
    C.prototype = /* @__PURE__ */ Object.create(null);
    return C;
})();
function createRouter() {
    const ctx = {
        root: {
            key: ""
        },
        static: new EmptyObject()
    };
    return ctx;
}
function splitPath(path) {
    return path.split("/").filter(Boolean);
}
function getMatchParams(segments, paramsMap) {
    const params = new EmptyObject();
    for (const [index, name] of paramsMap){
        const segment = index < 0 ? segments.slice(-1 * index).join("/") : segments[index];
        if (typeof name === "string") {
            params[name] = segment;
        } else {
            const match = segment.match(name);
            if (match) {
                for(const key in match.groups){
                    params[key] = match.groups[key];
                }
            }
        }
    }
    return params;
}
function addRoute(ctx, method = "", path, data) {
    const segments = splitPath(path);
    let node = ctx.root;
    let _unnamedParamIndex = 0;
    const paramsMap = [];
    for(let i = 0; i < segments.length; i++){
        const segment = segments[i];
        if (segment.startsWith("**")) {
            if (!node.wildcard) {
                node.wildcard = {
                    key: "**"
                };
            }
            node = node.wildcard;
            paramsMap.push([
                -i,
                segment.split(":")[1] || "_",
                segment.length === 2
            ]);
            break;
        }
        if (segment === "*" || segment.includes(":")) {
            if (!node.param) {
                node.param = {
                    key: "*"
                };
            }
            node = node.param;
            const isOptional = segment === "*";
            paramsMap.push([
                i,
                isOptional ? `_${_unnamedParamIndex++}` : _getParamMatcher(segment),
                isOptional
            ]);
            continue;
        }
        const child = node.static?.[segment];
        if (child) {
            node = child;
        } else {
            const staticNode = {
                key: segment
            };
            if (!node.static) {
                node.static = new EmptyObject();
            }
            node.static[segment] = staticNode;
            node = staticNode;
        }
    }
    const hasParams = paramsMap.length > 0;
    if (!node.methods) {
        node.methods = new EmptyObject();
    }
    if (!node.methods[method]) {
        node.methods[method] = [];
    }
    node.methods[method].push({
        data: data || null,
        paramsMap: hasParams ? paramsMap : void 0
    });
    if (!hasParams) {
        ctx.static[path] = node;
    }
}
function _getParamMatcher(segment) {
    if (!segment.includes(":", 1)) {
        return segment.slice(1);
    }
    const regex = segment.replace(/:(\w+)/g, (_, id)=>`(?<${id}>\\w+)`);
    return new RegExp(`^${regex}$`);
}
function findRoute(ctx, method = "", path, opts) {
    if (path[path.length - 1] === "/") {
        path = path.slice(0, -1);
    }
    const staticNode = ctx.static[path];
    if (staticNode && staticNode.methods) {
        const staticMatch = staticNode.methods[method] || staticNode.methods[""];
        if (staticMatch !== void 0) {
            return staticMatch[0];
        }
    }
    const segments = splitPath(path);
    const match = _lookupTree(ctx, ctx.root, method, segments, 0)?.[0];
    if (match === void 0) {
        return;
    }
    if (opts?.params === false) {
        return match;
    }
    return {
        data: match.data,
        params: match.paramsMap ? getMatchParams(segments, match.paramsMap) : void 0
    };
}
function _lookupTree(ctx, node, method, segments, index) {
    if (index === segments.length) {
        if (node.methods) {
            const match = node.methods[method] || node.methods[""];
            if (match) {
                return match;
            }
        }
        if (node.param && node.param.methods) {
            const match = node.param.methods[method] || node.param.methods[""];
            if (match) {
                const pMap = match[0].paramsMap;
                if (pMap?.[pMap?.length - 1]?.[2]) {
                    return match;
                }
            }
        }
        if (node.wildcard && node.wildcard.methods) {
            const match = node.wildcard.methods[method] || node.wildcard.methods[""];
            if (match) {
                const pMap = match[0].paramsMap;
                if (pMap?.[pMap?.length - 1]?.[2]) {
                    return match;
                }
            }
        }
        return void 0;
    }
    const segment = segments[index];
    if (node.static) {
        const staticChild = node.static[segment];
        if (staticChild) {
            const match = _lookupTree(ctx, staticChild, method, segments, index + 1);
            if (match) {
                return match;
            }
        }
    }
    if (node.param) {
        const match = _lookupTree(ctx, node.param, method, segments, index + 1);
        if (match) {
            return match;
        }
    }
    if (node.wildcard && node.wildcard.methods) {
        return node.wildcard.methods[method] || node.wildcard.methods[""];
    }
    return;
}
function removeRoute(ctx, method, path) {
    const segments = splitPath(path);
    return _remove(ctx.root, method || "", segments, 0);
}
function _remove(node, method, segments, index) {
    if (index === segments.length) {
        if (node.methods && method in node.methods) {
            delete node.methods[method];
            if (Object.keys(node.methods).length === 0) {
                node.methods = void 0;
            }
        }
        return;
    }
    const segment = segments[index];
    if (segment === "*") {
        if (node.param) {
            _remove(node.param, method, segments, index + 1);
            if (_isEmptyNode(node.param)) {
                node.param = void 0;
            }
        }
        return;
    }
    if (segment === "**") {
        if (node.wildcard) {
            _remove(node.wildcard, method, segments, index + 1);
            if (_isEmptyNode(node.wildcard)) {
                node.wildcard = void 0;
            }
        }
        return;
    }
    const childNode = node.static?.[segment];
    if (childNode) {
        _remove(childNode, method, segments, index + 1);
        if (_isEmptyNode(childNode)) {
            delete node.static[segment];
            if (Object.keys(node.static).length === 0) {
                node.static = void 0;
            }
        }
    }
}
function _isEmptyNode(node) {
    return node.methods === void 0 && node.static === void 0 && node.param === void 0 && node.wildcard === void 0;
}
function findAllRoutes(ctx, method = "", path, opts) {
    if (path[path.length - 1] === "/") {
        path = path.slice(0, -1);
    }
    const segments = splitPath(path);
    const matches = _findAll(ctx, ctx.root, method, segments, 0);
    if (opts?.params === false) {
        return matches;
    }
    return matches.map((m)=>{
        return {
            data: m.data,
            params: m.paramsMap ? getMatchParams(segments, m.paramsMap) : void 0
        };
    });
}
function _findAll(ctx, node, method, segments, index, matches = []) {
    const segment = segments[index];
    if (node.wildcard && node.wildcard.methods) {
        const match = node.wildcard.methods[method] || node.wildcard.methods[""];
        if (match) {
            matches.push(...match);
        }
    }
    if (node.param) {
        _findAll(ctx, node.param, method, segments, index + 1, matches);
        if (index === segments.length && node.param.methods) {
            const match = node.param.methods[method] || node.param.methods[""];
            if (match) {
                matches.push(...match);
            }
        }
    }
    const staticChild = node.static?.[segment];
    if (staticChild) {
        _findAll(ctx, staticChild, method, segments, index + 1, matches);
    }
    if (index === segments.length && node.methods) {
        const match = node.methods[method] || node.methods[""];
        if (match) {
            matches.push(...match);
        }
    }
    return matches;
}
;
}}),
"[project]/node_modules/@better-auth/utils/dist/base64.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "base64": (()=>base64),
    "base64Url": (()=>base64Url)
});
function getAlphabet(urlSafe) {
    return urlSafe ? "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_" : "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
}
function base64Encode(data, alphabet, padding) {
    let result = "";
    let buffer = 0;
    let shift = 0;
    for (const byte of data){
        buffer = buffer << 8 | byte;
        shift += 8;
        while(shift >= 6){
            shift -= 6;
            result += alphabet[buffer >> shift & 63];
        }
    }
    if (shift > 0) {
        result += alphabet[buffer << 6 - shift & 63];
    }
    if (padding) {
        const padCount = (4 - result.length % 4) % 4;
        result += "=".repeat(padCount);
    }
    return result;
}
function base64Decode(data, alphabet) {
    const decodeMap = /* @__PURE__ */ new Map();
    for(let i = 0; i < alphabet.length; i++){
        decodeMap.set(alphabet[i], i);
    }
    const result = [];
    let buffer = 0;
    let bitsCollected = 0;
    for (const char of data){
        if (char === "=") break;
        const value = decodeMap.get(char);
        if (value === void 0) {
            throw new Error(`Invalid Base64 character: ${char}`);
        }
        buffer = buffer << 6 | value;
        bitsCollected += 6;
        if (bitsCollected >= 8) {
            bitsCollected -= 8;
            result.push(buffer >> bitsCollected & 255);
        }
    }
    return Uint8Array.from(result);
}
const base64 = {
    encode (data, options = {}) {
        const alphabet = getAlphabet(false);
        const buffer = typeof data === "string" ? new TextEncoder().encode(data) : new Uint8Array(data);
        return base64Encode(buffer, alphabet, options.padding ?? true);
    },
    decode (data) {
        if (typeof data !== "string") {
            data = new TextDecoder().decode(data);
        }
        const urlSafe = data.includes("-") || data.includes("_");
        const alphabet = getAlphabet(urlSafe);
        return base64Decode(data, alphabet);
    }
};
const base64Url = {
    encode (data, options = {}) {
        const alphabet = getAlphabet(true);
        const buffer = typeof data === "string" ? new TextEncoder().encode(data) : new Uint8Array(data);
        return base64Encode(buffer, alphabet, options.padding ?? true);
    },
    decode (data) {
        const urlSafe = data.includes("-") || data.includes("_");
        const alphabet = getAlphabet(urlSafe);
        return base64Decode(data, alphabet);
    }
};
;
}}),
"[project]/node_modules/@better-auth/utils/dist/hex.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "hex": (()=>hex)
});
const hexadecimal = "0123456789abcdef";
const hex = {
    encode: (data)=>{
        if (typeof data === "string") {
            data = new TextEncoder().encode(data);
        }
        if (data.byteLength === 0) {
            return "";
        }
        const buffer = new Uint8Array(data);
        let result = "";
        for (const byte of buffer){
            result += byte.toString(16).padStart(2, "0");
        }
        return result;
    },
    decode: (data)=>{
        if (!data) {
            return "";
        }
        if (typeof data === "string") {
            if (data.length % 2 !== 0) {
                throw new Error("Invalid hexadecimal string");
            }
            if (!new RegExp(`^[${hexadecimal}]+$`).test(data)) {
                throw new Error("Invalid hexadecimal string");
            }
            const result = new Uint8Array(data.length / 2);
            for(let i = 0; i < data.length; i += 2){
                result[i / 2] = parseInt(data.slice(i, i + 2), 16);
            }
            return new TextDecoder().decode(result);
        }
        return new TextDecoder().decode(data);
    }
};
;
}}),
"[project]/node_modules/@better-auth/utils/dist/hmac.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createHMAC": (()=>createHMAC)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uncrypto$2f$dist$2f$crypto$2e$web$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uncrypto/dist/crypto.web.mjs [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$hex$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@better-auth/utils/dist/hex.mjs [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$base64$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@better-auth/utils/dist/base64.mjs [middleware-edge] (ecmascript)");
;
;
;
const createHMAC = (algorithm = "SHA-256", encoding = "none")=>{
    const hmac = {
        importKey: async (key, keyUsage)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uncrypto$2f$dist$2f$crypto$2e$web$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["subtle"].importKey("raw", typeof key === "string" ? new TextEncoder().encode(key) : key, {
                name: "HMAC",
                hash: {
                    name: algorithm
                }
            }, false, [
                keyUsage
            ]);
        },
        sign: async (hmacKey, data)=>{
            if (typeof hmacKey === "string") {
                hmacKey = await hmac.importKey(hmacKey, "sign");
            }
            const signature = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uncrypto$2f$dist$2f$crypto$2e$web$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["subtle"].sign("HMAC", hmacKey, typeof data === "string" ? new TextEncoder().encode(data) : data);
            if (encoding === "hex") {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$hex$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["hex"].encode(signature);
            }
            if (encoding === "base64" || encoding === "base64url" || encoding === "base64urlnopad") {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$base64$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["base64Url"].encode(signature, {
                    padding: encoding !== "base64urlnopad"
                });
            }
            return signature;
        },
        verify: async (hmacKey, data, signature)=>{
            if (typeof hmacKey === "string") {
                hmacKey = await hmac.importKey(hmacKey, "verify");
            }
            if (encoding === "hex") {
                signature = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$hex$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["hex"].decode(signature);
            }
            if (encoding === "base64" || encoding === "base64url" || encoding === "base64urlnopad") {
                signature = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$base64$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["base64"].decode(signature);
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uncrypto$2f$dist$2f$crypto$2e$web$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["subtle"].verify("HMAC", hmacKey, typeof signature === "string" ? new TextEncoder().encode(signature) : signature, typeof data === "string" ? new TextEncoder().encode(data) : data);
        }
    };
    return hmac;
};
;
}}),
"[project]/node_modules/@better-auth/utils/dist/binary.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "binary": (()=>binary)
});
const decoders = /* @__PURE__ */ new Map();
const encoder = new TextEncoder();
const binary = {
    decode: (data, encoding = "utf-8")=>{
        if (!decoders.has(encoding)) {
            decoders.set(encoding, new TextDecoder(encoding));
        }
        const decoder = decoders.get(encoding);
        return decoder.decode(data);
    },
    encode: encoder.encode
};
;
}}),
"[project]/node_modules/@better-auth/utils/dist/random.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createRandomStringGenerator": (()=>createRandomStringGenerator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uncrypto$2f$dist$2f$crypto$2e$web$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uncrypto/dist/crypto.web.mjs [middleware-edge] (ecmascript)");
;
function expandAlphabet(alphabet) {
    switch(alphabet){
        case "a-z":
            return "abcdefghijklmnopqrstuvwxyz";
        case "A-Z":
            return "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        case "0-9":
            return "0123456789";
        case "-_":
            return "-_";
        default:
            throw new Error(`Unsupported alphabet: ${alphabet}`);
    }
}
function createRandomStringGenerator(...baseAlphabets) {
    const baseCharSet = baseAlphabets.map(expandAlphabet).join("");
    if (baseCharSet.length === 0) {
        throw new Error("No valid characters provided for random string generation.");
    }
    const baseCharSetLength = baseCharSet.length;
    return (length, ...alphabets)=>{
        if (length <= 0) {
            throw new Error("Length must be a positive integer.");
        }
        let charSet = baseCharSet;
        let charSetLength = baseCharSetLength;
        if (alphabets.length > 0) {
            charSet = alphabets.map(expandAlphabet).join("");
            charSetLength = charSet.length;
        }
        const maxValid = Math.floor(256 / charSetLength) * charSetLength;
        const buf = new Uint8Array(length * 2);
        const bufLength = buf.length;
        let result = "";
        let bufIndex = bufLength;
        let rand;
        while(result.length < length){
            if (bufIndex >= bufLength) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uncrypto$2f$dist$2f$crypto$2e$web$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["getRandomValues"])(buf);
                bufIndex = 0;
            }
            rand = buf[bufIndex++];
            if (rand < maxValid) {
                result += charSet[rand % charSetLength];
            }
        }
        return result;
    };
}
;
}}),
"[project]/node_modules/@better-auth/utils/dist/hash.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createHash": (()=>createHash)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uncrypto$2f$dist$2f$crypto$2e$web$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uncrypto/dist/crypto.web.mjs [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$base64$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@better-auth/utils/dist/base64.mjs [middleware-edge] (ecmascript)");
;
;
function createHash(algorithm, encoding) {
    return {
        digest: async (input)=>{
            const encoder = new TextEncoder();
            const data = typeof input === "string" ? encoder.encode(input) : input;
            const hashBuffer = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uncrypto$2f$dist$2f$crypto$2e$web$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["subtle"].digest(algorithm, data);
            if (encoding === "hex") {
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                const hashHex = hashArray.map((b)=>b.toString(16).padStart(2, "0")).join("");
                return hashHex;
            }
            if (encoding === "base64" || encoding === "base64url" || encoding === "base64urlnopad") {
                if (encoding.includes("url")) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$base64$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["base64Url"].encode(hashBuffer, {
                        padding: encoding !== "base64urlnopad"
                    });
                }
                const hashBase64 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$base64$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["base64"].encode(hashBuffer);
                return hashBase64;
            }
            return hashBuffer;
        }
    };
}
;
}}),
"[project]/node_modules/@better-auth/utils/dist/index.mjs [middleware-edge] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/@better-auth/utils/dist/index.mjs [middleware-edge] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$better$2d$auth$2f$utils$2f$dist$2f$index$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@better-auth/utils/dist/index.mjs [middleware-edge] (ecmascript) <locals>");
}}),
"[project]/node_modules/@better-fetch/fetch/dist/index.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BetterFetchError": (()=>BetterFetchError),
    "ValidationError": (()=>ValidationError),
    "applySchemaPlugin": (()=>applySchemaPlugin),
    "betterFetch": (()=>betterFetch),
    "bodyParser": (()=>bodyParser),
    "createFetch": (()=>createFetch),
    "createRetryStrategy": (()=>createRetryStrategy),
    "createSchema": (()=>createSchema),
    "detectContentType": (()=>detectContentType),
    "detectResponseType": (()=>detectResponseType),
    "getBody": (()=>getBody),
    "getFetch": (()=>getFetch),
    "getHeaders": (()=>getHeaders),
    "getMethod": (()=>getMethod),
    "getTimeout": (()=>getTimeout),
    "getURL": (()=>getURL),
    "initializePlugins": (()=>initializePlugins),
    "isFunction": (()=>isFunction),
    "isJSONParsable": (()=>isJSONParsable),
    "isJSONSerializable": (()=>isJSONSerializable),
    "isPayloadMethod": (()=>isPayloadMethod),
    "isRouteMethod": (()=>isRouteMethod),
    "jsonParse": (()=>jsonParse),
    "methods": (()=>methods),
    "parseStandardSchema": (()=>parseStandardSchema)
});
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __spreadValues = (a, b)=>{
    for(var prop in b || (b = {}))if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)){
        if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);
    }
    return a;
};
var __spreadProps = (a, b)=>__defProps(a, __getOwnPropDescs(b));
// src/error.ts
var BetterFetchError = class extends Error {
    constructor(status, statusText, error){
        super(statusText || status.toString(), {
            cause: error
        });
        this.status = status;
        this.statusText = statusText;
        this.error = error;
    }
};
// src/plugins.ts
var initializePlugins = async (url, options)=>{
    var _a, _b, _c, _d, _e, _f;
    let opts = options || {};
    const hooks = {
        onRequest: [
            options == null ? void 0 : options.onRequest
        ],
        onResponse: [
            options == null ? void 0 : options.onResponse
        ],
        onSuccess: [
            options == null ? void 0 : options.onSuccess
        ],
        onError: [
            options == null ? void 0 : options.onError
        ],
        onRetry: [
            options == null ? void 0 : options.onRetry
        ]
    };
    if (!options || !(options == null ? void 0 : options.plugins)) {
        return {
            url,
            options: opts,
            hooks
        };
    }
    for (const plugin of (options == null ? void 0 : options.plugins) || []){
        if (plugin.init) {
            const pluginRes = await ((_a = plugin.init) == null ? void 0 : _a.call(plugin, url.toString(), options));
            opts = pluginRes.options || opts;
            url = pluginRes.url;
        }
        hooks.onRequest.push((_b = plugin.hooks) == null ? void 0 : _b.onRequest);
        hooks.onResponse.push((_c = plugin.hooks) == null ? void 0 : _c.onResponse);
        hooks.onSuccess.push((_d = plugin.hooks) == null ? void 0 : _d.onSuccess);
        hooks.onError.push((_e = plugin.hooks) == null ? void 0 : _e.onError);
        hooks.onRetry.push((_f = plugin.hooks) == null ? void 0 : _f.onRetry);
    }
    return {
        url,
        options: opts,
        hooks
    };
};
// src/retry.ts
var LinearRetryStrategy = class {
    constructor(options){
        this.options = options;
    }
    shouldAttemptRetry(attempt, response) {
        if (this.options.shouldRetry) {
            return Promise.resolve(attempt < this.options.attempts && this.options.shouldRetry(response));
        }
        return Promise.resolve(attempt < this.options.attempts);
    }
    getDelay() {
        return this.options.delay;
    }
};
var ExponentialRetryStrategy = class {
    constructor(options){
        this.options = options;
    }
    shouldAttemptRetry(attempt, response) {
        if (this.options.shouldRetry) {
            return Promise.resolve(attempt < this.options.attempts && this.options.shouldRetry(response));
        }
        return Promise.resolve(attempt < this.options.attempts);
    }
    getDelay(attempt) {
        const delay = Math.min(this.options.maxDelay, this.options.baseDelay * 2 ** attempt);
        return delay;
    }
};
function createRetryStrategy(options) {
    if (typeof options === "number") {
        return new LinearRetryStrategy({
            type: "linear",
            attempts: options,
            delay: 1e3
        });
    }
    switch(options.type){
        case "linear":
            return new LinearRetryStrategy(options);
        case "exponential":
            return new ExponentialRetryStrategy(options);
        default:
            throw new Error("Invalid retry strategy");
    }
}
// src/auth.ts
var getAuthHeader = async (options)=>{
    const headers = {};
    const getValue = async (value)=>typeof value === "function" ? await value() : value;
    if (options == null ? void 0 : options.auth) {
        if (options.auth.type === "Bearer") {
            const token = await getValue(options.auth.token);
            if (!token) {
                return headers;
            }
            headers["authorization"] = `Bearer ${token}`;
        } else if (options.auth.type === "Basic") {
            const username = getValue(options.auth.username);
            const password = getValue(options.auth.password);
            if (!username || !password) {
                return headers;
            }
            headers["authorization"] = `Basic ${btoa(`${username}:${password}`)}`;
        } else if (options.auth.type === "Custom") {
            const value = getValue(options.auth.value);
            if (!value) {
                return headers;
            }
            headers["authorization"] = `${getValue(options.auth.prefix)} ${value}`;
        }
    }
    return headers;
};
// src/utils.ts
var JSON_RE = /^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;
function detectResponseType(request) {
    const _contentType = request.headers.get("content-type");
    const textTypes = /* @__PURE__ */ new Set([
        "image/svg",
        "application/xml",
        "application/xhtml",
        "application/html"
    ]);
    if (!_contentType) {
        return "json";
    }
    const contentType = _contentType.split(";").shift() || "";
    if (JSON_RE.test(contentType)) {
        return "json";
    }
    if (textTypes.has(contentType) || contentType.startsWith("text/")) {
        return "text";
    }
    return "blob";
}
function isJSONParsable(value) {
    try {
        JSON.parse(value);
        return true;
    } catch (error) {
        return false;
    }
}
function isJSONSerializable(value) {
    if (value === void 0) {
        return false;
    }
    const t = typeof value;
    if (t === "string" || t === "number" || t === "boolean" || t === null) {
        return true;
    }
    if (t !== "object") {
        return false;
    }
    if (Array.isArray(value)) {
        return true;
    }
    if (value.buffer) {
        return false;
    }
    return value.constructor && value.constructor.name === "Object" || typeof value.toJSON === "function";
}
function jsonParse(text) {
    try {
        return JSON.parse(text);
    } catch (error) {
        return text;
    }
}
function isFunction(value) {
    return typeof value === "function";
}
function getFetch(options) {
    if (options == null ? void 0 : options.customFetchImpl) {
        return options.customFetchImpl;
    }
    if (typeof globalThis !== "undefined" && isFunction(globalThis.fetch)) {
        return globalThis.fetch;
    }
    if ("undefined" !== "undefined" && isFunction(window.fetch)) {
        "TURBOPACK unreachable";
    }
    throw new Error("No fetch implementation found");
}
function isPayloadMethod(method) {
    if (!method) {
        return false;
    }
    const payloadMethod = [
        "POST",
        "PUT",
        "PATCH",
        "DELETE"
    ];
    return payloadMethod.includes(method.toUpperCase());
}
function isRouteMethod(method) {
    const routeMethod = [
        "GET",
        "POST",
        "PUT",
        "PATCH",
        "DELETE"
    ];
    if (!method) {
        return false;
    }
    return routeMethod.includes(method.toUpperCase());
}
async function getHeaders(opts) {
    const headers = new Headers(opts == null ? void 0 : opts.headers);
    const authHeader = await getAuthHeader(opts);
    for (const [key, value] of Object.entries(authHeader || {})){
        headers.set(key, value);
    }
    if (!headers.has("content-type")) {
        const t = detectContentType(opts == null ? void 0 : opts.body);
        if (t) {
            headers.set("content-type", t);
        }
    }
    return headers;
}
function getURL(url, options) {
    if (url.startsWith("@")) {
        const m = url.toString().split("@")[1].split("/")[0];
        if (methods.includes(m)) {
            url = url.replace(`@${m}/`, "/");
        }
    }
    let _url;
    try {
        if (url.startsWith("http")) {
            _url = url;
        } else {
            let baseURL = options == null ? void 0 : options.baseURL;
            if (baseURL && !(baseURL == null ? void 0 : baseURL.endsWith("/"))) {
                baseURL = baseURL + "/";
            }
            if (url.startsWith("/")) {
                _url = new URL(url.substring(1), baseURL);
            } else {
                _url = new URL(url, options == null ? void 0 : options.baseURL);
            }
        }
    } catch (e) {
        if (e instanceof TypeError) {
            if (!(options == null ? void 0 : options.baseURL)) {
                throw TypeError(`Invalid URL ${url}. Are you passing in a relative url but not setting the baseURL?`);
            }
            throw TypeError(`Invalid URL ${url}. Please validate that you are passing the correct input.`);
        }
        throw e;
    }
    if (options == null ? void 0 : options.params) {
        if (Array.isArray(options == null ? void 0 : options.params)) {
            const params = (options == null ? void 0 : options.params) ? Array.isArray(options.params) ? `/${options.params.join("/")}` : `/${Object.values(options.params).join("/")}` : "";
            _url = _url.toString().split("/:")[0];
            _url = `${_url.toString()}${params}`;
        } else {
            for (const [key, value] of Object.entries(options == null ? void 0 : options.params)){
                _url = _url.toString().replace(`:${key}`, String(value));
            }
        }
    }
    const __url = new URL(_url);
    const queryParams = options == null ? void 0 : options.query;
    if (queryParams) {
        for (const [key, value] of Object.entries(queryParams)){
            __url.searchParams.append(key, String(value));
        }
    }
    return __url;
}
function detectContentType(body) {
    if (isJSONSerializable(body)) {
        return "application/json";
    }
    return null;
}
function getBody(options) {
    if (!(options == null ? void 0 : options.body)) {
        return null;
    }
    const headers = new Headers(options == null ? void 0 : options.headers);
    if (isJSONSerializable(options.body) && !headers.has("content-type")) {
        for (const [key, value] of Object.entries(options == null ? void 0 : options.body)){
            if (value instanceof Date) {
                options.body[key] = value.toISOString();
            }
        }
        return JSON.stringify(options.body);
    }
    return options.body;
}
function getMethod(url, options) {
    var _a;
    if (options == null ? void 0 : options.method) {
        return options.method.toUpperCase();
    }
    if (url.startsWith("@")) {
        const pMethod = (_a = url.split("@")[1]) == null ? void 0 : _a.split("/")[0];
        if (!methods.includes(pMethod)) {
            return (options == null ? void 0 : options.body) ? "POST" : "GET";
        }
        return pMethod.toUpperCase();
    }
    return (options == null ? void 0 : options.body) ? "POST" : "GET";
}
function getTimeout(options, controller) {
    let abortTimeout;
    if (!(options == null ? void 0 : options.signal) && (options == null ? void 0 : options.timeout)) {
        abortTimeout = setTimeout(()=>controller == null ? void 0 : controller.abort(), options == null ? void 0 : options.timeout);
    }
    return {
        abortTimeout,
        clearTimeout: ()=>{
            if (abortTimeout) {
                clearTimeout(abortTimeout);
            }
        }
    };
}
function bodyParser(data, responseType) {
    if (responseType === "json") {
        return JSON.parse(data);
    }
    return data;
}
var ValidationError = class _ValidationError extends Error {
    constructor(issues, message){
        super(message || JSON.stringify(issues, null, 2));
        this.issues = issues;
        Object.setPrototypeOf(this, _ValidationError.prototype);
    }
};
async function parseStandardSchema(schema, input) {
    let result = await schema["~standard"].validate(input);
    if (result.issues) {
        throw new ValidationError(result.issues);
    }
    return result.value;
}
// src/create-fetch/schema.ts
var methods = [
    "get",
    "post",
    "put",
    "patch",
    "delete"
];
var createSchema = (schema, config)=>{
    return {
        schema,
        config
    };
};
// src/create-fetch/index.ts
var applySchemaPlugin = (config)=>({
        id: "apply-schema",
        name: "Apply Schema",
        version: "1.0.0",
        async init (url, options) {
            var _a, _b, _c, _d;
            const schema = ((_b = (_a = config.plugins) == null ? void 0 : _a.find((plugin)=>{
                var _a2;
                return ((_a2 = plugin.schema) == null ? void 0 : _a2.config) ? url.startsWith(plugin.schema.config.baseURL || "") || url.startsWith(plugin.schema.config.prefix || "") : false;
            })) == null ? void 0 : _b.schema) || config.schema;
            if (schema) {
                let urlKey = url;
                if ((_c = schema.config) == null ? void 0 : _c.prefix) {
                    if (urlKey.startsWith(schema.config.prefix)) {
                        urlKey = urlKey.replace(schema.config.prefix, "");
                        if (schema.config.baseURL) {
                            url = url.replace(schema.config.prefix, schema.config.baseURL);
                        }
                    }
                }
                if ((_d = schema.config) == null ? void 0 : _d.baseURL) {
                    if (urlKey.startsWith(schema.config.baseURL)) {
                        urlKey = urlKey.replace(schema.config.baseURL, "");
                    }
                }
                const keySchema = schema.schema[urlKey];
                if (keySchema) {
                    let opts = __spreadProps(__spreadValues({}, options), {
                        method: keySchema.method,
                        output: keySchema.output
                    });
                    if (!(options == null ? void 0 : options.disableValidation)) {
                        opts = __spreadProps(__spreadValues({}, opts), {
                            body: keySchema.input ? await parseStandardSchema(keySchema.input, options == null ? void 0 : options.body) : options == null ? void 0 : options.body,
                            params: keySchema.params ? await parseStandardSchema(keySchema.params, options == null ? void 0 : options.params) : options == null ? void 0 : options.params,
                            query: keySchema.query ? await parseStandardSchema(keySchema.query, options == null ? void 0 : options.query) : options == null ? void 0 : options.query
                        });
                    }
                    return {
                        url,
                        options: opts
                    };
                }
            }
            return {
                url,
                options
            };
        }
    });
var createFetch = (config)=>{
    async function $fetch(url, options) {
        const opts = __spreadProps(__spreadValues(__spreadValues({}, config), options), {
            plugins: [
                ...(config == null ? void 0 : config.plugins) || [],
                applySchemaPlugin(config || {})
            ]
        });
        if (config == null ? void 0 : config.catchAllError) {
            try {
                return await betterFetch(url, opts);
            } catch (error) {
                return {
                    data: null,
                    error: {
                        status: 500,
                        statusText: "Fetch Error",
                        message: "Fetch related error. Captured by catchAllError option. See error property for more details.",
                        error
                    }
                };
            }
        }
        return await betterFetch(url, opts);
    }
    return $fetch;
};
// src/url.ts
function getURL2(url, option) {
    let { baseURL, params, query } = option || {
        query: {},
        params: {},
        baseURL: ""
    };
    let basePath = url.startsWith("http") ? url.split("/").slice(0, 3).join("/") : baseURL || "";
    if (url.startsWith("@")) {
        const m = url.toString().split("@")[1].split("/")[0];
        if (methods.includes(m)) {
            url = url.replace(`@${m}/`, "/");
        }
    }
    if (!basePath.endsWith("/")) basePath += "/";
    let [path, urlQuery] = url.replace(basePath, "").split("?");
    const queryParams = new URLSearchParams(urlQuery);
    for (const [key, value] of Object.entries(query || {})){
        if (value == null) continue;
        queryParams.set(key, String(value));
    }
    if (params) {
        if (Array.isArray(params)) {
            const paramPaths = path.split("/").filter((p)=>p.startsWith(":"));
            for (const [index, key] of paramPaths.entries()){
                const value = params[index];
                path = path.replace(key, value);
            }
        } else {
            for (const [key, value] of Object.entries(params)){
                path = path.replace(`:${key}`, String(value));
            }
        }
    }
    path = path.split("/").map(encodeURIComponent).join("/");
    if (path.startsWith("/")) path = path.slice(1);
    let queryParamString = queryParams.toString();
    queryParamString = queryParamString.length > 0 ? `?${queryParamString}`.replace(/\+/g, "%20") : "";
    if (!basePath.startsWith("http")) {
        return `${basePath}${path}${queryParamString}`;
    }
    const _url = new URL(`${path}${queryParamString}`, basePath);
    return _url;
}
// src/fetch.ts
var betterFetch = async (url, options)=>{
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const { hooks, url: __url, options: opts } = await initializePlugins(url, options);
    const fetch = getFetch(opts);
    const controller = new AbortController();
    const signal = (_a = opts.signal) != null ? _a : controller.signal;
    const _url = getURL2(__url, opts);
    const body = getBody(opts);
    const headers = await getHeaders(opts);
    const method = getMethod(__url, opts);
    let context = __spreadProps(__spreadValues({}, opts), {
        url: _url,
        headers,
        body,
        method,
        signal
    });
    for (const onRequest of hooks.onRequest){
        if (onRequest) {
            const res = await onRequest(context);
            if (res instanceof Object) {
                context = res;
            }
        }
    }
    if ("pipeTo" in context && typeof context.pipeTo === "function" || typeof ((_b = options == null ? void 0 : options.body) == null ? void 0 : _b.pipe) === "function") {
        if (!("duplex" in context)) {
            context.duplex = "half";
        }
    }
    const { clearTimeout: clearTimeout2 } = getTimeout(opts, controller);
    let response = await fetch(context.url, context);
    clearTimeout2();
    const responseContext = {
        response,
        request: context
    };
    for (const onResponse of hooks.onResponse){
        if (onResponse) {
            const r = await onResponse(__spreadProps(__spreadValues({}, responseContext), {
                response: ((_c = options == null ? void 0 : options.hookOptions) == null ? void 0 : _c.cloneResponse) ? response.clone() : response
            }));
            if (r instanceof Response) {
                response = r;
            } else if (r instanceof Object) {
                response = r.response;
            }
        }
    }
    if (response.ok) {
        const hasBody = context.method !== "HEAD";
        if (!hasBody) {
            return {
                data: "",
                error: null
            };
        }
        const responseType = detectResponseType(response);
        const successContext = {
            data: "",
            response,
            request: context
        };
        if (responseType === "json" || responseType === "text") {
            const text = await response.text();
            const parser2 = (_d = context.jsonParser) != null ? _d : jsonParse;
            const data = await parser2(text);
            successContext.data = data;
        } else {
            successContext.data = await response[responseType]();
        }
        if (context == null ? void 0 : context.output) {
            if (context.output && !context.disableValidation) {
                successContext.data = await parseStandardSchema(context.output, successContext.data);
            }
        }
        for (const onSuccess of hooks.onSuccess){
            if (onSuccess) {
                await onSuccess(__spreadProps(__spreadValues({}, successContext), {
                    response: ((_e = options == null ? void 0 : options.hookOptions) == null ? void 0 : _e.cloneResponse) ? response.clone() : response
                }));
            }
        }
        if (options == null ? void 0 : options.throw) {
            return successContext.data;
        }
        return {
            data: successContext.data,
            error: null
        };
    }
    const parser = (_f = options == null ? void 0 : options.jsonParser) != null ? _f : jsonParse;
    const responseText = await response.text();
    const isJSONResponse = isJSONParsable(responseText);
    const errorObject = isJSONResponse ? await parser(responseText) : null;
    const errorContext = {
        response,
        responseText,
        request: context,
        error: __spreadProps(__spreadValues({}, errorObject), {
            status: response.status,
            statusText: response.statusText
        })
    };
    for (const onError of hooks.onError){
        if (onError) {
            await onError(__spreadProps(__spreadValues({}, errorContext), {
                response: ((_g = options == null ? void 0 : options.hookOptions) == null ? void 0 : _g.cloneResponse) ? response.clone() : response
            }));
        }
    }
    if (options == null ? void 0 : options.retry) {
        const retryStrategy = createRetryStrategy(options.retry);
        const _retryAttempt = (_h = options.retryAttempt) != null ? _h : 0;
        if (await retryStrategy.shouldAttemptRetry(_retryAttempt, response)) {
            for (const onRetry of hooks.onRetry){
                if (onRetry) {
                    await onRetry(responseContext);
                }
            }
            const delay = retryStrategy.getDelay(_retryAttempt);
            await new Promise((resolve)=>setTimeout(resolve, delay));
            return await betterFetch(url, __spreadProps(__spreadValues({}, options), {
                retryAttempt: _retryAttempt + 1
            }));
        }
    }
    if (options == null ? void 0 : options.throw) {
        throw new BetterFetchError(response.status, response.statusText, isJSONResponse ? errorObject : responseText);
    }
    return {
        data: null,
        error: __spreadProps(__spreadValues({}, errorObject), {
            status: response.status,
            statusText: response.statusText
        })
    };
};
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/defu/dist/defu.mjs [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createDefu": (()=>createDefu),
    "default": (()=>defu),
    "defu": (()=>defu),
    "defuArrayFn": (()=>defuArrayFn),
    "defuFn": (()=>defuFn)
});
function isPlainObject(value) {
    if (value === null || typeof value !== "object") {
        return false;
    }
    const prototype = Object.getPrototypeOf(value);
    if (prototype !== null && prototype !== Object.prototype && Object.getPrototypeOf(prototype) !== null) {
        return false;
    }
    if (Symbol.iterator in value) {
        return false;
    }
    if (Symbol.toStringTag in value) {
        return Object.prototype.toString.call(value) === "[object Module]";
    }
    return true;
}
function _defu(baseObject, defaults, namespace = ".", merger) {
    if (!isPlainObject(defaults)) {
        return _defu(baseObject, {}, namespace, merger);
    }
    const object = Object.assign({}, defaults);
    for(const key in baseObject){
        if (key === "__proto__" || key === "constructor") {
            continue;
        }
        const value = baseObject[key];
        if (value === null || value === void 0) {
            continue;
        }
        if (merger && merger(object, key, value, namespace)) {
            continue;
        }
        if (Array.isArray(value) && Array.isArray(object[key])) {
            object[key] = [
                ...value,
                ...object[key]
            ];
        } else if (isPlainObject(value) && isPlainObject(object[key])) {
            object[key] = _defu(value, object[key], (namespace ? `${namespace}.` : "") + key.toString(), merger);
        } else {
            object[key] = value;
        }
    }
    return object;
}
function createDefu(merger) {
    return (...arguments_)=>// eslint-disable-next-line unicorn/no-array-reduce
        arguments_.reduce((p, c)=>_defu(p, c, "", merger), {});
}
const defu = createDefu();
const defuFn = createDefu((object, key, currentValue)=>{
    if (object[key] !== void 0 && typeof currentValue === "function") {
        object[key] = currentValue(object[key]);
        return true;
    }
});
const defuArrayFn = createDefu((object, key, currentValue)=>{
    if (Array.isArray(object[key]) && typeof currentValue === "function") {
        object[key] = currentValue(object[key]);
        return true;
    }
});
;
}}),
"[project]/node_modules/@noble/hashes/esm/crypto.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "crypto": (()=>crypto)
});
const crypto = typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined; //# sourceMappingURL=crypto.js.map
}}),
"[project]/node_modules/@noble/hashes/esm/utils.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utilities for hex, bytes, CSPRNG.
 * @module
 */ /*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */ // We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.
// node.js versions earlier than v19 don't declare it in global scope.
// For node.js, package.json#exports field mapping rewrites import
// from `crypto` to `cryptoNode`, which imports native module.
// Makes the utils un-importable in browsers without a bundler.
// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.
__turbopack_context__.s({
    "Hash": (()=>Hash),
    "abytes": (()=>abytes),
    "aexists": (()=>aexists),
    "ahash": (()=>ahash),
    "anumber": (()=>anumber),
    "aoutput": (()=>aoutput),
    "asyncLoop": (()=>asyncLoop),
    "byteSwap": (()=>byteSwap),
    "byteSwap32": (()=>byteSwap32),
    "byteSwapIfBE": (()=>byteSwapIfBE),
    "bytesToHex": (()=>bytesToHex),
    "bytesToUtf8": (()=>bytesToUtf8),
    "checkOpts": (()=>checkOpts),
    "clean": (()=>clean),
    "concatBytes": (()=>concatBytes),
    "createHasher": (()=>createHasher),
    "createOptHasher": (()=>createOptHasher),
    "createView": (()=>createView),
    "createXOFer": (()=>createXOFer),
    "hexToBytes": (()=>hexToBytes),
    "isBytes": (()=>isBytes),
    "isLE": (()=>isLE),
    "kdfInputToBytes": (()=>kdfInputToBytes),
    "nextTick": (()=>nextTick),
    "randomBytes": (()=>randomBytes),
    "rotl": (()=>rotl),
    "rotr": (()=>rotr),
    "swap32IfBE": (()=>swap32IfBE),
    "swap8IfBE": (()=>swap8IfBE),
    "toBytes": (()=>toBytes),
    "u32": (()=>u32),
    "u8": (()=>u8),
    "utf8ToBytes": (()=>utf8ToBytes),
    "wrapConstructor": (()=>wrapConstructor),
    "wrapConstructorWithOpts": (()=>wrapConstructorWithOpts),
    "wrapXOFConstructorWithOpts": (()=>wrapXOFConstructorWithOpts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$crypto$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/crypto.js [middleware-edge] (ecmascript)");
;
function isBytes(a) {
    return a instanceof Uint8Array || ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array';
}
function anumber(n) {
    if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);
}
function abytes(b, ...lengths) {
    if (!isBytes(b)) throw new Error('Uint8Array expected');
    if (lengths.length > 0 && !lengths.includes(b.length)) throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);
}
function ahash(h) {
    if (typeof h !== 'function' || typeof h.create !== 'function') throw new Error('Hash should be wrapped by utils.createHasher');
    anumber(h.outputLen);
    anumber(h.blockLen);
}
function aexists(instance, checkFinished = true) {
    if (instance.destroyed) throw new Error('Hash instance has been destroyed');
    if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');
}
function aoutput(out, instance) {
    abytes(out);
    const min = instance.outputLen;
    if (out.length < min) {
        throw new Error('digestInto() expects output buffer of length at least ' + min);
    }
}
function u8(arr) {
    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);
}
function u32(arr) {
    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));
}
function clean(...arrays) {
    for(let i = 0; i < arrays.length; i++){
        arrays[i].fill(0);
    }
}
function createView(arr) {
    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);
}
function rotr(word, shift) {
    return word << 32 - shift | word >>> shift;
}
function rotl(word, shift) {
    return word << shift | word >>> 32 - shift >>> 0;
}
const isLE = /* @__PURE__ */ (()=>new Uint8Array(new Uint32Array([
        0x11223344
    ]).buffer)[0] === 0x44)();
function byteSwap(word) {
    return word << 24 & 0xff000000 | word << 8 & 0xff0000 | word >>> 8 & 0xff00 | word >>> 24 & 0xff;
}
const swap8IfBE = isLE ? (n)=>n : (n)=>byteSwap(n);
const byteSwapIfBE = swap8IfBE;
function byteSwap32(arr) {
    for(let i = 0; i < arr.length; i++){
        arr[i] = byteSwap(arr[i]);
    }
    return arr;
}
const swap32IfBE = isLE ? (u)=>u : byteSwap32;
// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex
const hasHexBuiltin = /* @__PURE__ */ (()=>// @ts-ignore
    typeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();
// Array where index 0xf0 (240) is mapped to string 'f0'
const hexes = /* @__PURE__ */ Array.from({
    length: 256
}, (_, i)=>i.toString(16).padStart(2, '0'));
function bytesToHex(bytes) {
    abytes(bytes);
    // @ts-ignore
    if (hasHexBuiltin) return bytes.toHex();
    // pre-caching improves the speed 6x
    let hex = '';
    for(let i = 0; i < bytes.length; i++){
        hex += hexes[bytes[i]];
    }
    return hex;
}
// We use optimized technique to convert hex string to byte array
const asciis = {
    _0: 48,
    _9: 57,
    A: 65,
    F: 70,
    a: 97,
    f: 102
};
function asciiToBase16(ch) {
    if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48
    if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)
    if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)
    return;
}
function hexToBytes(hex) {
    if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);
    // @ts-ignore
    if (hasHexBuiltin) return Uint8Array.fromHex(hex);
    const hl = hex.length;
    const al = hl / 2;
    if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);
    const array = new Uint8Array(al);
    for(let ai = 0, hi = 0; ai < al; ai++, hi += 2){
        const n1 = asciiToBase16(hex.charCodeAt(hi));
        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));
        if (n1 === undefined || n2 === undefined) {
            const char = hex[hi] + hex[hi + 1];
            throw new Error('hex string expected, got non-hex character "' + char + '" at index ' + hi);
        }
        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163
    }
    return array;
}
const nextTick = async ()=>{};
async function asyncLoop(iters, tick, cb) {
    let ts = Date.now();
    for(let i = 0; i < iters; i++){
        cb(i);
        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too
        const diff = Date.now() - ts;
        if (diff >= 0 && diff < tick) continue;
        await nextTick();
        ts += diff;
    }
}
function utf8ToBytes(str) {
    if (typeof str !== 'string') throw new Error('string expected');
    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809
}
function bytesToUtf8(bytes) {
    return new TextDecoder().decode(bytes);
}
function toBytes(data) {
    if (typeof data === 'string') data = utf8ToBytes(data);
    abytes(data);
    return data;
}
function kdfInputToBytes(data) {
    if (typeof data === 'string') data = utf8ToBytes(data);
    abytes(data);
    return data;
}
function concatBytes(...arrays) {
    let sum = 0;
    for(let i = 0; i < arrays.length; i++){
        const a = arrays[i];
        abytes(a);
        sum += a.length;
    }
    const res = new Uint8Array(sum);
    for(let i = 0, pad = 0; i < arrays.length; i++){
        const a = arrays[i];
        res.set(a, pad);
        pad += a.length;
    }
    return res;
}
function checkOpts(defaults, opts) {
    if (opts !== undefined && ({}).toString.call(opts) !== '[object Object]') throw new Error('options should be object or undefined');
    const merged = Object.assign(defaults, opts);
    return merged;
}
class Hash {
}
function createHasher(hashCons) {
    const hashC = (msg)=>hashCons().update(toBytes(msg)).digest();
    const tmp = hashCons();
    hashC.outputLen = tmp.outputLen;
    hashC.blockLen = tmp.blockLen;
    hashC.create = ()=>hashCons();
    return hashC;
}
function createOptHasher(hashCons) {
    const hashC = (msg, opts)=>hashCons(opts).update(toBytes(msg)).digest();
    const tmp = hashCons({});
    hashC.outputLen = tmp.outputLen;
    hashC.blockLen = tmp.blockLen;
    hashC.create = (opts)=>hashCons(opts);
    return hashC;
}
function createXOFer(hashCons) {
    const hashC = (msg, opts)=>hashCons(opts).update(toBytes(msg)).digest();
    const tmp = hashCons({});
    hashC.outputLen = tmp.outputLen;
    hashC.blockLen = tmp.blockLen;
    hashC.create = (opts)=>hashCons(opts);
    return hashC;
}
const wrapConstructor = createHasher;
const wrapConstructorWithOpts = createOptHasher;
const wrapXOFConstructorWithOpts = createXOFer;
function randomBytes(bytesLength = 32) {
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$crypto$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["crypto"] && typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$crypto$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["crypto"].getRandomValues === 'function') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$crypto$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["crypto"].getRandomValues(new Uint8Array(bytesLength));
    }
    // Legacy Node.js compatibility
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$crypto$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["crypto"] && typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$crypto$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["crypto"].randomBytes === 'function') {
        return Uint8Array.from(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$crypto$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["crypto"].randomBytes(bytesLength));
    }
    throw new Error('crypto.getRandomValues must be defined');
} //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@noble/hashes/esm/hmac.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * HMAC: RFC2104 message authentication code.
 * @module
 */ __turbopack_context__.s({
    "HMAC": (()=>HMAC),
    "hmac": (()=>hmac)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/utils.js [middleware-edge] (ecmascript)");
;
class HMAC extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Hash"] {
    constructor(hash, _key){
        super();
        this.finished = false;
        this.destroyed = false;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["ahash"])(hash);
        const key = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["toBytes"])(_key);
        this.iHash = hash.create();
        if (typeof this.iHash.update !== 'function') throw new Error('Expected instance of class which extends utils.Hash');
        this.blockLen = this.iHash.blockLen;
        this.outputLen = this.iHash.outputLen;
        const blockLen = this.blockLen;
        const pad = new Uint8Array(blockLen);
        // blockLen can be bigger than outputLen
        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);
        for(let i = 0; i < pad.length; i++)pad[i] ^= 0x36;
        this.iHash.update(pad);
        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone
        this.oHash = hash.create();
        // Undo internal XOR && apply outer XOR
        for(let i = 0; i < pad.length; i++)pad[i] ^= 0x36 ^ 0x5c;
        this.oHash.update(pad);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["clean"])(pad);
    }
    update(buf) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["aexists"])(this);
        this.iHash.update(buf);
        return this;
    }
    digestInto(out) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["aexists"])(this);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["abytes"])(out, this.outputLen);
        this.finished = true;
        this.iHash.digestInto(out);
        this.oHash.update(out);
        this.oHash.digestInto(out);
        this.destroy();
    }
    digest() {
        const out = new Uint8Array(this.oHash.outputLen);
        this.digestInto(out);
        return out;
    }
    _cloneInto(to) {
        // Create new instance without calling constructor since key already in state and we don't know it.
        to || (to = Object.create(Object.getPrototypeOf(this), {}));
        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;
        to = to;
        to.finished = finished;
        to.destroyed = destroyed;
        to.blockLen = blockLen;
        to.outputLen = outputLen;
        to.oHash = oHash._cloneInto(to.oHash);
        to.iHash = iHash._cloneInto(to.iHash);
        return to;
    }
    clone() {
        return this._cloneInto();
    }
    destroy() {
        this.destroyed = true;
        this.oHash.destroy();
        this.iHash.destroy();
    }
}
const hmac = (hash, key, message)=>new HMAC(hash, key).update(message).digest();
hmac.create = (hash, key)=>new HMAC(hash, key); //# sourceMappingURL=hmac.js.map
}}),
"[project]/node_modules/@noble/hashes/esm/pbkdf2.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * PBKDF (RFC 2898). Can be used to create a key from password and salt.
 * @module
 */ __turbopack_context__.s({
    "pbkdf2": (()=>pbkdf2),
    "pbkdf2Async": (()=>pbkdf2Async)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$hmac$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/hmac.js [middleware-edge] (ecmascript)");
// prettier-ignore
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/utils.js [middleware-edge] (ecmascript)");
;
;
// Common prologue and epilogue for sync/async functions
function pbkdf2Init(hash, _password, _salt, _opts) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["ahash"])(hash);
    const opts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["checkOpts"])({
        dkLen: 32,
        asyncTick: 10
    }, _opts);
    const { c, dkLen, asyncTick } = opts;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(c);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(dkLen);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(asyncTick);
    if (c < 1) throw new Error('iterations (c) should be >= 1');
    const password = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["kdfInputToBytes"])(_password);
    const salt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["kdfInputToBytes"])(_salt);
    // DK = PBKDF2(PRF, Password, Salt, c, dkLen);
    const DK = new Uint8Array(dkLen);
    // U1 = PRF(Password, Salt + INT_32_BE(i))
    const PRF = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$hmac$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["hmac"].create(hash, password);
    const PRFSalt = PRF._cloneInto().update(salt);
    return {
        c,
        dkLen,
        asyncTick,
        DK,
        PRF,
        PRFSalt
    };
}
function pbkdf2Output(PRF, PRFSalt, DK, prfW, u) {
    PRF.destroy();
    PRFSalt.destroy();
    if (prfW) prfW.destroy();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["clean"])(u);
    return DK;
}
function pbkdf2(hash, password, salt, opts) {
    const { c, dkLen, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);
    let prfW; // Working copy
    const arr = new Uint8Array(4);
    const view = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createView"])(arr);
    const u = new Uint8Array(PRF.outputLen);
    // DK = T1 + T2 + ⋯ + Tdklen/hlen
    for(let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen){
        // Ti = F(Password, Salt, c, i)
        const Ti = DK.subarray(pos, pos + PRF.outputLen);
        view.setInt32(0, ti, false);
        // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc
        // U1 = PRF(Password, Salt + INT_32_BE(i))
        (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);
        Ti.set(u.subarray(0, Ti.length));
        for(let ui = 1; ui < c; ui++){
            // Uc = PRF(Password, Uc−1)
            PRF._cloneInto(prfW).update(u).digestInto(u);
            for(let i = 0; i < Ti.length; i++)Ti[i] ^= u[i];
        }
    }
    return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);
}
async function pbkdf2Async(hash, password, salt, opts) {
    const { c, dkLen, asyncTick, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);
    let prfW; // Working copy
    const arr = new Uint8Array(4);
    const view = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createView"])(arr);
    const u = new Uint8Array(PRF.outputLen);
    // DK = T1 + T2 + ⋯ + Tdklen/hlen
    for(let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen){
        // Ti = F(Password, Salt, c, i)
        const Ti = DK.subarray(pos, pos + PRF.outputLen);
        view.setInt32(0, ti, false);
        // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc
        // U1 = PRF(Password, Salt + INT_32_BE(i))
        (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);
        Ti.set(u.subarray(0, Ti.length));
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["asyncLoop"])(c - 1, asyncTick, ()=>{
            // Uc = PRF(Password, Uc−1)
            PRF._cloneInto(prfW).update(u).digestInto(u);
            for(let i = 0; i < Ti.length; i++)Ti[i] ^= u[i];
        });
    }
    return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);
} //# sourceMappingURL=pbkdf2.js.map
}}),
"[project]/node_modules/@noble/hashes/esm/_md.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Internal Merkle-Damgard hash utils.
 * @module
 */ __turbopack_context__.s({
    "Chi": (()=>Chi),
    "HashMD": (()=>HashMD),
    "Maj": (()=>Maj),
    "SHA224_IV": (()=>SHA224_IV),
    "SHA256_IV": (()=>SHA256_IV),
    "SHA384_IV": (()=>SHA384_IV),
    "SHA512_IV": (()=>SHA512_IV),
    "setBigUint64": (()=>setBigUint64)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/utils.js [middleware-edge] (ecmascript)");
;
function setBigUint64(view, byteOffset, value, isLE) {
    if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);
    const _32n = BigInt(32);
    const _u32_max = BigInt(0xffffffff);
    const wh = Number(value >> _32n & _u32_max);
    const wl = Number(value & _u32_max);
    const h = isLE ? 4 : 0;
    const l = isLE ? 0 : 4;
    view.setUint32(byteOffset + h, wh, isLE);
    view.setUint32(byteOffset + l, wl, isLE);
}
function Chi(a, b, c) {
    return a & b ^ ~a & c;
}
function Maj(a, b, c) {
    return a & b ^ a & c ^ b & c;
}
class HashMD extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Hash"] {
    constructor(blockLen, outputLen, padOffset, isLE){
        super();
        this.finished = false;
        this.length = 0;
        this.pos = 0;
        this.destroyed = false;
        this.blockLen = blockLen;
        this.outputLen = outputLen;
        this.padOffset = padOffset;
        this.isLE = isLE;
        this.buffer = new Uint8Array(blockLen);
        this.view = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createView"])(this.buffer);
    }
    update(data) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["aexists"])(this);
        data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["toBytes"])(data);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["abytes"])(data);
        const { view, buffer, blockLen } = this;
        const len = data.length;
        for(let pos = 0; pos < len;){
            const take = Math.min(blockLen - this.pos, len - pos);
            // Fast path: we have at least one block in input, cast it to view and process
            if (take === blockLen) {
                const dataView = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createView"])(data);
                for(; blockLen <= len - pos; pos += blockLen)this.process(dataView, pos);
                continue;
            }
            buffer.set(data.subarray(pos, pos + take), this.pos);
            this.pos += take;
            pos += take;
            if (this.pos === blockLen) {
                this.process(view, 0);
                this.pos = 0;
            }
        }
        this.length += data.length;
        this.roundClean();
        return this;
    }
    digestInto(out) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["aexists"])(this);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["aoutput"])(out, this);
        this.finished = true;
        // Padding
        // We can avoid allocation of buffer for padding completely if it
        // was previously not allocated here. But it won't change performance.
        const { buffer, view, blockLen, isLE } = this;
        let { pos } = this;
        // append the bit '1' to the message
        buffer[pos++] = 0b10000000;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["clean"])(this.buffer.subarray(pos));
        // we have less than padOffset left in buffer, so we cannot put length in
        // current block, need process it and pad again
        if (this.padOffset > blockLen - pos) {
            this.process(view, 0);
            pos = 0;
        }
        // Pad until full block byte with zeros
        for(let i = pos; i < blockLen; i++)buffer[i] = 0;
        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that
        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.
        // So we just write lowest 64 bits of that value.
        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);
        this.process(view, 0);
        const oview = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createView"])(out);
        const len = this.outputLen;
        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT
        if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');
        const outLen = len / 4;
        const state = this.get();
        if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');
        for(let i = 0; i < outLen; i++)oview.setUint32(4 * i, state[i], isLE);
    }
    digest() {
        const { buffer, outputLen } = this;
        this.digestInto(buffer);
        const res = buffer.slice(0, outputLen);
        this.destroy();
        return res;
    }
    _cloneInto(to) {
        to || (to = new this.constructor());
        to.set(...this.get());
        const { blockLen, buffer, length, finished, destroyed, pos } = this;
        to.destroyed = destroyed;
        to.finished = finished;
        to.length = length;
        to.pos = pos;
        if (length % blockLen) to.buffer.set(buffer);
        return to;
    }
    clone() {
        return this._cloneInto();
    }
}
const SHA256_IV = /* @__PURE__ */ Uint32Array.from([
    0x6a09e667,
    0xbb67ae85,
    0x3c6ef372,
    0xa54ff53a,
    0x510e527f,
    0x9b05688c,
    0x1f83d9ab,
    0x5be0cd19
]);
const SHA224_IV = /* @__PURE__ */ Uint32Array.from([
    0xc1059ed8,
    0x367cd507,
    0x3070dd17,
    0xf70e5939,
    0xffc00b31,
    0x68581511,
    0x64f98fa7,
    0xbefa4fa4
]);
const SHA384_IV = /* @__PURE__ */ Uint32Array.from([
    0xcbbb9d5d,
    0xc1059ed8,
    0x629a292a,
    0x367cd507,
    0x9159015a,
    0x3070dd17,
    0x152fecd8,
    0xf70e5939,
    0x67332667,
    0xffc00b31,
    0x8eb44a87,
    0x68581511,
    0xdb0c2e0d,
    0x64f98fa7,
    0x47b5481d,
    0xbefa4fa4
]);
const SHA512_IV = /* @__PURE__ */ Uint32Array.from([
    0x6a09e667,
    0xf3bcc908,
    0xbb67ae85,
    0x84caa73b,
    0x3c6ef372,
    0xfe94f82b,
    0xa54ff53a,
    0x5f1d36f1,
    0x510e527f,
    0xade682d1,
    0x9b05688c,
    0x2b3e6c1f,
    0x1f83d9ab,
    0xfb41bd6b,
    0x5be0cd19,
    0x137e2179
]); //# sourceMappingURL=_md.js.map
}}),
"[project]/node_modules/@noble/hashes/esm/_u64.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.
 * @todo re-check https://issues.chromium.org/issues/42212588
 * @module
 */ __turbopack_context__.s({
    "add": (()=>add),
    "add3H": (()=>add3H),
    "add3L": (()=>add3L),
    "add4H": (()=>add4H),
    "add4L": (()=>add4L),
    "add5H": (()=>add5H),
    "add5L": (()=>add5L),
    "default": (()=>__TURBOPACK__default__export__),
    "fromBig": (()=>fromBig),
    "rotlBH": (()=>rotlBH),
    "rotlBL": (()=>rotlBL),
    "rotlSH": (()=>rotlSH),
    "rotlSL": (()=>rotlSL),
    "rotr32H": (()=>rotr32H),
    "rotr32L": (()=>rotr32L),
    "rotrBH": (()=>rotrBH),
    "rotrBL": (()=>rotrBL),
    "rotrSH": (()=>rotrSH),
    "rotrSL": (()=>rotrSL),
    "shrSH": (()=>shrSH),
    "shrSL": (()=>shrSL),
    "split": (()=>split),
    "toBig": (()=>toBig)
});
const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);
const _32n = /* @__PURE__ */ BigInt(32);
function fromBig(n, le = false) {
    if (le) return {
        h: Number(n & U32_MASK64),
        l: Number(n >> _32n & U32_MASK64)
    };
    return {
        h: Number(n >> _32n & U32_MASK64) | 0,
        l: Number(n & U32_MASK64) | 0
    };
}
function split(lst, le = false) {
    const len = lst.length;
    let Ah = new Uint32Array(len);
    let Al = new Uint32Array(len);
    for(let i = 0; i < len; i++){
        const { h, l } = fromBig(lst[i], le);
        [Ah[i], Al[i]] = [
            h,
            l
        ];
    }
    return [
        Ah,
        Al
    ];
}
const toBig = (h, l)=>BigInt(h >>> 0) << _32n | BigInt(l >>> 0);
// for Shift in [0, 32)
const shrSH = (h, _l, s)=>h >>> s;
const shrSL = (h, l, s)=>h << 32 - s | l >>> s;
// Right rotate for Shift in [1, 32)
const rotrSH = (h, l, s)=>h >>> s | l << 32 - s;
const rotrSL = (h, l, s)=>h << 32 - s | l >>> s;
// Right rotate for Shift in (32, 64), NOTE: 32 is special case.
const rotrBH = (h, l, s)=>h << 64 - s | l >>> s - 32;
const rotrBL = (h, l, s)=>h >>> s - 32 | l << 64 - s;
// Right rotate for shift===32 (just swaps l&h)
const rotr32H = (_h, l)=>l;
const rotr32L = (h, _l)=>h;
// Left rotate for Shift in [1, 32)
const rotlSH = (h, l, s)=>h << s | l >>> 32 - s;
const rotlSL = (h, l, s)=>l << s | h >>> 32 - s;
// Left rotate for Shift in (32, 64), NOTE: 32 is special case.
const rotlBH = (h, l, s)=>l << s - 32 | h >>> 64 - s;
const rotlBL = (h, l, s)=>h << s - 32 | l >>> 64 - s;
// JS uses 32-bit signed integers for bitwise operations which means we cannot
// simple take carry out of low bit sum by shift, we need to use division.
function add(Ah, Al, Bh, Bl) {
    const l = (Al >>> 0) + (Bl >>> 0);
    return {
        h: Ah + Bh + (l / 2 ** 32 | 0) | 0,
        l: l | 0
    };
}
// Addition with more than 2 elements
const add3L = (Al, Bl, Cl)=>(Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);
const add3H = (low, Ah, Bh, Ch)=>Ah + Bh + Ch + (low / 2 ** 32 | 0) | 0;
const add4L = (Al, Bl, Cl, Dl)=>(Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);
const add4H = (low, Ah, Bh, Ch, Dh)=>Ah + Bh + Ch + Dh + (low / 2 ** 32 | 0) | 0;
const add5L = (Al, Bl, Cl, Dl, El)=>(Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);
const add5H = (low, Ah, Bh, Ch, Dh, Eh)=>Ah + Bh + Ch + Dh + Eh + (low / 2 ** 32 | 0) | 0;
;
// prettier-ignore
const u64 = {
    fromBig,
    split,
    toBig,
    shrSH,
    shrSL,
    rotrSH,
    rotrSL,
    rotrBH,
    rotrBL,
    rotr32H,
    rotr32L,
    rotlSH,
    rotlSL,
    rotlBH,
    rotlBL,
    add,
    add3L,
    add3H,
    add4L,
    add4H,
    add5H,
    add5L
};
const __TURBOPACK__default__export__ = u64;
 //# sourceMappingURL=_u64.js.map
}}),
"[project]/node_modules/@noble/hashes/esm/sha2.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.
 * SHA256 is the fastest hash implementable in JS, even faster than Blake3.
 * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and
 * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).
 * @module
 */ __turbopack_context__.s({
    "SHA224": (()=>SHA224),
    "SHA256": (()=>SHA256),
    "SHA384": (()=>SHA384),
    "SHA512": (()=>SHA512),
    "SHA512_224": (()=>SHA512_224),
    "SHA512_256": (()=>SHA512_256),
    "sha224": (()=>sha224),
    "sha256": (()=>sha256),
    "sha384": (()=>sha384),
    "sha512": (()=>sha512),
    "sha512_224": (()=>sha512_224),
    "sha512_256": (()=>sha512_256)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/_md.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/_u64.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/utils.js [middleware-edge] (ecmascript)");
;
;
;
/**
 * Round constants:
 * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)
 */ // prettier-ignore
const SHA256_K = /* @__PURE__ */ Uint32Array.from([
    0x428a2f98,
    0x71374491,
    0xb5c0fbcf,
    0xe9b5dba5,
    0x3956c25b,
    0x59f111f1,
    0x923f82a4,
    0xab1c5ed5,
    0xd807aa98,
    0x12835b01,
    0x243185be,
    0x550c7dc3,
    0x72be5d74,
    0x80deb1fe,
    0x9bdc06a7,
    0xc19bf174,
    0xe49b69c1,
    0xefbe4786,
    0x0fc19dc6,
    0x240ca1cc,
    0x2de92c6f,
    0x4a7484aa,
    0x5cb0a9dc,
    0x76f988da,
    0x983e5152,
    0xa831c66d,
    0xb00327c8,
    0xbf597fc7,
    0xc6e00bf3,
    0xd5a79147,
    0x06ca6351,
    0x14292967,
    0x27b70a85,
    0x2e1b2138,
    0x4d2c6dfc,
    0x53380d13,
    0x650a7354,
    0x766a0abb,
    0x81c2c92e,
    0x92722c85,
    0xa2bfe8a1,
    0xa81a664b,
    0xc24b8b70,
    0xc76c51a3,
    0xd192e819,
    0xd6990624,
    0xf40e3585,
    0x106aa070,
    0x19a4c116,
    0x1e376c08,
    0x2748774c,
    0x34b0bcb5,
    0x391c0cb3,
    0x4ed8aa4a,
    0x5b9cca4f,
    0x682e6ff3,
    0x748f82ee,
    0x78a5636f,
    0x84c87814,
    0x8cc70208,
    0x90befffa,
    0xa4506ceb,
    0xbef9a3f7,
    0xc67178f2
]);
/** Reusable temporary buffer. "W" comes straight from spec. */ const SHA256_W = /* @__PURE__ */ new Uint32Array(64);
class SHA256 extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["HashMD"] {
    constructor(outputLen = 32){
        super(64, outputLen, 8, false);
        // We cannot use array here since array allows indexing by variable
        // which means optimizer/compiler cannot use registers.
        this.A = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA256_IV"][0] | 0;
        this.B = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA256_IV"][1] | 0;
        this.C = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA256_IV"][2] | 0;
        this.D = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA256_IV"][3] | 0;
        this.E = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA256_IV"][4] | 0;
        this.F = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA256_IV"][5] | 0;
        this.G = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA256_IV"][6] | 0;
        this.H = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA256_IV"][7] | 0;
    }
    get() {
        const { A, B, C, D, E, F, G, H } = this;
        return [
            A,
            B,
            C,
            D,
            E,
            F,
            G,
            H
        ];
    }
    // prettier-ignore
    set(A, B, C, D, E, F, G, H) {
        this.A = A | 0;
        this.B = B | 0;
        this.C = C | 0;
        this.D = D | 0;
        this.E = E | 0;
        this.F = F | 0;
        this.G = G | 0;
        this.H = H | 0;
    }
    process(view, offset) {
        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array
        for(let i = 0; i < 16; i++, offset += 4)SHA256_W[i] = view.getUint32(offset, false);
        for(let i = 16; i < 64; i++){
            const W15 = SHA256_W[i - 15];
            const W2 = SHA256_W[i - 2];
            const s0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(W15, 7) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(W15, 18) ^ W15 >>> 3;
            const s1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(W2, 17) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(W2, 19) ^ W2 >>> 10;
            SHA256_W[i] = s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16] | 0;
        }
        // Compression function main loop, 64 rounds
        let { A, B, C, D, E, F, G, H } = this;
        for(let i = 0; i < 64; i++){
            const sigma1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(E, 6) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(E, 11) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(E, 25);
            const T1 = H + sigma1 + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Chi"])(E, F, G) + SHA256_K[i] + SHA256_W[i] | 0;
            const sigma0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(A, 2) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(A, 13) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotr"])(A, 22);
            const T2 = sigma0 + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Maj"])(A, B, C) | 0;
            H = G;
            G = F;
            F = E;
            E = D + T1 | 0;
            D = C;
            C = B;
            B = A;
            A = T1 + T2 | 0;
        }
        // Add the compressed chunk to the current hash value
        A = A + this.A | 0;
        B = B + this.B | 0;
        C = C + this.C | 0;
        D = D + this.D | 0;
        E = E + this.E | 0;
        F = F + this.F | 0;
        G = G + this.G | 0;
        H = H + this.H | 0;
        this.set(A, B, C, D, E, F, G, H);
    }
    roundClean() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["clean"])(SHA256_W);
    }
    destroy() {
        this.set(0, 0, 0, 0, 0, 0, 0, 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["clean"])(this.buffer);
    }
}
class SHA224 extends SHA256 {
    constructor(){
        super(28);
        this.A = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA224_IV"][0] | 0;
        this.B = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA224_IV"][1] | 0;
        this.C = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA224_IV"][2] | 0;
        this.D = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA224_IV"][3] | 0;
        this.E = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA224_IV"][4] | 0;
        this.F = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA224_IV"][5] | 0;
        this.G = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA224_IV"][6] | 0;
        this.H = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA224_IV"][7] | 0;
    }
}
// SHA2-512 is slower than sha256 in js because u64 operations are slow.
// Round contants
// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409
// prettier-ignore
const K512 = /* @__PURE__ */ (()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["split"])([
        '0x428a2f98d728ae22',
        '0x7137449123ef65cd',
        '0xb5c0fbcfec4d3b2f',
        '0xe9b5dba58189dbbc',
        '0x3956c25bf348b538',
        '0x59f111f1b605d019',
        '0x923f82a4af194f9b',
        '0xab1c5ed5da6d8118',
        '0xd807aa98a3030242',
        '0x12835b0145706fbe',
        '0x243185be4ee4b28c',
        '0x550c7dc3d5ffb4e2',
        '0x72be5d74f27b896f',
        '0x80deb1fe3b1696b1',
        '0x9bdc06a725c71235',
        '0xc19bf174cf692694',
        '0xe49b69c19ef14ad2',
        '0xefbe4786384f25e3',
        '0x0fc19dc68b8cd5b5',
        '0x240ca1cc77ac9c65',
        '0x2de92c6f592b0275',
        '0x4a7484aa6ea6e483',
        '0x5cb0a9dcbd41fbd4',
        '0x76f988da831153b5',
        '0x983e5152ee66dfab',
        '0xa831c66d2db43210',
        '0xb00327c898fb213f',
        '0xbf597fc7beef0ee4',
        '0xc6e00bf33da88fc2',
        '0xd5a79147930aa725',
        '0x06ca6351e003826f',
        '0x142929670a0e6e70',
        '0x27b70a8546d22ffc',
        '0x2e1b21385c26c926',
        '0x4d2c6dfc5ac42aed',
        '0x53380d139d95b3df',
        '0x650a73548baf63de',
        '0x766a0abb3c77b2a8',
        '0x81c2c92e47edaee6',
        '0x92722c851482353b',
        '0xa2bfe8a14cf10364',
        '0xa81a664bbc423001',
        '0xc24b8b70d0f89791',
        '0xc76c51a30654be30',
        '0xd192e819d6ef5218',
        '0xd69906245565a910',
        '0xf40e35855771202a',
        '0x106aa07032bbd1b8',
        '0x19a4c116b8d2d0c8',
        '0x1e376c085141ab53',
        '0x2748774cdf8eeb99',
        '0x34b0bcb5e19b48a8',
        '0x391c0cb3c5c95a63',
        '0x4ed8aa4ae3418acb',
        '0x5b9cca4f7763e373',
        '0x682e6ff3d6b2b8a3',
        '0x748f82ee5defb2fc',
        '0x78a5636f43172f60',
        '0x84c87814a1f0ab72',
        '0x8cc702081a6439ec',
        '0x90befffa23631e28',
        '0xa4506cebde82bde9',
        '0xbef9a3f7b2c67915',
        '0xc67178f2e372532b',
        '0xca273eceea26619c',
        '0xd186b8c721c0c207',
        '0xeada7dd6cde0eb1e',
        '0xf57d4f7fee6ed178',
        '0x06f067aa72176fba',
        '0x0a637dc5a2c898a6',
        '0x113f9804bef90dae',
        '0x1b710b35131c471b',
        '0x28db77f523047d84',
        '0x32caab7b40c72493',
        '0x3c9ebe0a15c9bebc',
        '0x431d67c49c100d4c',
        '0x4cc5d4becb3e42b6',
        '0x597f299cfc657e2a',
        '0x5fcb6fab3ad6faec',
        '0x6c44198c4a475817'
    ].map((n)=>BigInt(n))))();
const SHA512_Kh = /* @__PURE__ */ (()=>K512[0])();
const SHA512_Kl = /* @__PURE__ */ (()=>K512[1])();
// Reusable temporary buffers
const SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);
const SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);
class SHA512 extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["HashMD"] {
    constructor(outputLen = 64){
        super(128, outputLen, 16, false);
        // We cannot use array here since array allows indexing by variable
        // which means optimizer/compiler cannot use registers.
        // h -- high 32 bits, l -- low 32 bits
        this.Ah = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][0] | 0;
        this.Al = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][1] | 0;
        this.Bh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][2] | 0;
        this.Bl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][3] | 0;
        this.Ch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][4] | 0;
        this.Cl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][5] | 0;
        this.Dh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][6] | 0;
        this.Dl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][7] | 0;
        this.Eh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][8] | 0;
        this.El = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][9] | 0;
        this.Fh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][10] | 0;
        this.Fl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][11] | 0;
        this.Gh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][12] | 0;
        this.Gl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][13] | 0;
        this.Hh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][14] | 0;
        this.Hl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA512_IV"][15] | 0;
    }
    // prettier-ignore
    get() {
        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;
        return [
            Ah,
            Al,
            Bh,
            Bl,
            Ch,
            Cl,
            Dh,
            Dl,
            Eh,
            El,
            Fh,
            Fl,
            Gh,
            Gl,
            Hh,
            Hl
        ];
    }
    // prettier-ignore
    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {
        this.Ah = Ah | 0;
        this.Al = Al | 0;
        this.Bh = Bh | 0;
        this.Bl = Bl | 0;
        this.Ch = Ch | 0;
        this.Cl = Cl | 0;
        this.Dh = Dh | 0;
        this.Dl = Dl | 0;
        this.Eh = Eh | 0;
        this.El = El | 0;
        this.Fh = Fh | 0;
        this.Fl = Fl | 0;
        this.Gh = Gh | 0;
        this.Gl = Gl | 0;
        this.Hh = Hh | 0;
        this.Hl = Hl | 0;
    }
    process(view, offset) {
        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array
        for(let i = 0; i < 16; i++, offset += 4){
            SHA512_W_H[i] = view.getUint32(offset);
            SHA512_W_L[i] = view.getUint32(offset += 4);
        }
        for(let i = 16; i < 80; i++){
            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)
            const W15h = SHA512_W_H[i - 15] | 0;
            const W15l = SHA512_W_L[i - 15] | 0;
            const s0h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSH"])(W15h, W15l, 1) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSH"])(W15h, W15l, 8) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["shrSH"])(W15h, W15l, 7);
            const s0l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSL"])(W15h, W15l, 1) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSL"])(W15h, W15l, 8) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["shrSL"])(W15h, W15l, 7);
            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)
            const W2h = SHA512_W_H[i - 2] | 0;
            const W2l = SHA512_W_L[i - 2] | 0;
            const s1h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSH"])(W2h, W2l, 19) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrBH"])(W2h, W2l, 61) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["shrSH"])(W2h, W2l, 6);
            const s1l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSL"])(W2h, W2l, 19) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrBL"])(W2h, W2l, 61) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["shrSL"])(W2h, W2l, 6);
            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];
            const SUMl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add4L"])(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);
            const SUMh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add4H"])(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);
            SHA512_W_H[i] = SUMh | 0;
            SHA512_W_L[i] = SUMl | 0;
        }
        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;
        // Compression function main loop, 80 rounds
        for(let i = 0; i < 80; i++){
            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)
            const sigma1h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSH"])(Eh, El, 14) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSH"])(Eh, El, 18) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrBH"])(Eh, El, 41);
            const sigma1l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSL"])(Eh, El, 14) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSL"])(Eh, El, 18) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrBL"])(Eh, El, 41);
            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;
            const CHIh = Eh & Fh ^ ~Eh & Gh;
            const CHIl = El & Fl ^ ~El & Gl;
            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]
            // prettier-ignore
            const T1ll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add5L"])(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);
            const T1h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add5H"])(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);
            const T1l = T1ll | 0;
            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)
            const sigma0h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSH"])(Ah, Al, 28) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrBH"])(Ah, Al, 34) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrBH"])(Ah, Al, 39);
            const sigma0l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrSL"])(Ah, Al, 28) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrBL"])(Ah, Al, 34) ^ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotrBL"])(Ah, Al, 39);
            const MAJh = Ah & Bh ^ Ah & Ch ^ Bh & Ch;
            const MAJl = Al & Bl ^ Al & Cl ^ Bl & Cl;
            Hh = Gh | 0;
            Hl = Gl | 0;
            Gh = Fh | 0;
            Gl = Fl | 0;
            Fh = Eh | 0;
            Fl = El | 0;
            ({ h: Eh, l: El } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(Dh | 0, Dl | 0, T1h | 0, T1l | 0));
            Dh = Ch | 0;
            Dl = Cl | 0;
            Ch = Bh | 0;
            Cl = Bl | 0;
            Bh = Ah | 0;
            Bl = Al | 0;
            const All = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add3L"])(T1l, sigma0l, MAJl);
            Ah = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add3H"])(All, T1h, sigma0h, MAJh);
            Al = All | 0;
        }
        // Add the compressed chunk to the current hash value
        ({ h: Ah, l: Al } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));
        ({ h: Bh, l: Bl } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));
        ({ h: Ch, l: Cl } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));
        ({ h: Dh, l: Dl } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));
        ({ h: Eh, l: El } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(this.Eh | 0, this.El | 0, Eh | 0, El | 0));
        ({ h: Fh, l: Fl } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));
        ({ h: Gh, l: Gl } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));
        ({ h: Hh, l: Hl } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_u64$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["add"])(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));
        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);
    }
    roundClean() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["clean"])(SHA512_W_H, SHA512_W_L);
    }
    destroy() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["clean"])(this.buffer);
        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
    }
}
class SHA384 extends SHA512 {
    constructor(){
        super(48);
        this.Ah = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][0] | 0;
        this.Al = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][1] | 0;
        this.Bh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][2] | 0;
        this.Bl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][3] | 0;
        this.Ch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][4] | 0;
        this.Cl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][5] | 0;
        this.Dh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][6] | 0;
        this.Dl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][7] | 0;
        this.Eh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][8] | 0;
        this.El = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][9] | 0;
        this.Fh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][10] | 0;
        this.Fl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][11] | 0;
        this.Gh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][12] | 0;
        this.Gl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][13] | 0;
        this.Hh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][14] | 0;
        this.Hl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$_md$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["SHA384_IV"][15] | 0;
    }
}
/**
 * Truncated SHA512/256 and SHA512/224.
 * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as "intermediary" IV of SHA512/t.
 * Then t hashes string to produce result IV.
 * See `test/misc/sha2-gen-iv.js`.
 */ /** SHA512/224 IV */ const T224_IV = /* @__PURE__ */ Uint32Array.from([
    0x8c3d37c8,
    0x19544da2,
    0x73e19966,
    0x89dcd4d6,
    0x1dfab7ae,
    0x32ff9c82,
    0x679dd514,
    0x582f9fcf,
    0x0f6d2b69,
    0x7bd44da8,
    0x77e36f73,
    0x04c48942,
    0x3f9d85a8,
    0x6a1d36c8,
    0x1112e6ad,
    0x91d692a1
]);
/** SHA512/256 IV */ const T256_IV = /* @__PURE__ */ Uint32Array.from([
    0x22312194,
    0xfc2bf72c,
    0x9f555fa3,
    0xc84c64c2,
    0x2393b86b,
    0x6f53b151,
    0x96387719,
    0x5940eabd,
    0x96283ee2,
    0xa88effe3,
    0xbe5e1e25,
    0x53863992,
    0x2b0199fc,
    0x2c85b8aa,
    0x0eb72ddc,
    0x81c52ca2
]);
class SHA512_224 extends SHA512 {
    constructor(){
        super(28);
        this.Ah = T224_IV[0] | 0;
        this.Al = T224_IV[1] | 0;
        this.Bh = T224_IV[2] | 0;
        this.Bl = T224_IV[3] | 0;
        this.Ch = T224_IV[4] | 0;
        this.Cl = T224_IV[5] | 0;
        this.Dh = T224_IV[6] | 0;
        this.Dl = T224_IV[7] | 0;
        this.Eh = T224_IV[8] | 0;
        this.El = T224_IV[9] | 0;
        this.Fh = T224_IV[10] | 0;
        this.Fl = T224_IV[11] | 0;
        this.Gh = T224_IV[12] | 0;
        this.Gl = T224_IV[13] | 0;
        this.Hh = T224_IV[14] | 0;
        this.Hl = T224_IV[15] | 0;
    }
}
class SHA512_256 extends SHA512 {
    constructor(){
        super(32);
        this.Ah = T256_IV[0] | 0;
        this.Al = T256_IV[1] | 0;
        this.Bh = T256_IV[2] | 0;
        this.Bl = T256_IV[3] | 0;
        this.Ch = T256_IV[4] | 0;
        this.Cl = T256_IV[5] | 0;
        this.Dh = T256_IV[6] | 0;
        this.Dl = T256_IV[7] | 0;
        this.Eh = T256_IV[8] | 0;
        this.El = T256_IV[9] | 0;
        this.Fh = T256_IV[10] | 0;
        this.Fl = T256_IV[11] | 0;
        this.Gh = T256_IV[12] | 0;
        this.Gl = T256_IV[13] | 0;
        this.Hh = T256_IV[14] | 0;
        this.Hl = T256_IV[15] | 0;
    }
}
const sha256 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createHasher"])(()=>new SHA256());
const sha224 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createHasher"])(()=>new SHA224());
const sha512 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createHasher"])(()=>new SHA512());
const sha384 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createHasher"])(()=>new SHA384());
const sha512_256 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createHasher"])(()=>new SHA512_256());
const sha512_224 = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createHasher"])(()=>new SHA512_224()); //# sourceMappingURL=sha2.js.map
}}),
"[project]/node_modules/@noble/hashes/esm/scrypt.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * RFC 7914 Scrypt KDF. Can be used to create a key from password and salt.
 * @module
 */ __turbopack_context__.s({
    "scrypt": (()=>scrypt),
    "scryptAsync": (()=>scryptAsync)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$pbkdf2$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/pbkdf2.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$sha2$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/sha2.js [middleware-edge] (ecmascript)");
// prettier-ignore
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@noble/hashes/esm/utils.js [middleware-edge] (ecmascript)");
;
;
;
// The main Scrypt loop: uses Salsa extensively.
// Six versions of the function were tried, this is the fastest one.
// prettier-ignore
function XorAndSalsa(prev, pi, input, ii, out, oi) {
    // Based on https://cr.yp.to/salsa20.html
    // Xor blocks
    let y00 = prev[pi++] ^ input[ii++], y01 = prev[pi++] ^ input[ii++];
    let y02 = prev[pi++] ^ input[ii++], y03 = prev[pi++] ^ input[ii++];
    let y04 = prev[pi++] ^ input[ii++], y05 = prev[pi++] ^ input[ii++];
    let y06 = prev[pi++] ^ input[ii++], y07 = prev[pi++] ^ input[ii++];
    let y08 = prev[pi++] ^ input[ii++], y09 = prev[pi++] ^ input[ii++];
    let y10 = prev[pi++] ^ input[ii++], y11 = prev[pi++] ^ input[ii++];
    let y12 = prev[pi++] ^ input[ii++], y13 = prev[pi++] ^ input[ii++];
    let y14 = prev[pi++] ^ input[ii++], y15 = prev[pi++] ^ input[ii++];
    // Save state to temporary variables (salsa)
    let x00 = y00, x01 = y01, x02 = y02, x03 = y03, x04 = y04, x05 = y05, x06 = y06, x07 = y07, x08 = y08, x09 = y09, x10 = y10, x11 = y11, x12 = y12, x13 = y13, x14 = y14, x15 = y15;
    // Main loop (salsa)
    for(let i = 0; i < 8; i += 2){
        x04 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x00 + x12 | 0, 7);
        x08 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x04 + x00 | 0, 9);
        x12 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x08 + x04 | 0, 13);
        x00 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x12 + x08 | 0, 18);
        x09 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x05 + x01 | 0, 7);
        x13 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x09 + x05 | 0, 9);
        x01 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x13 + x09 | 0, 13);
        x05 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x01 + x13 | 0, 18);
        x14 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x10 + x06 | 0, 7);
        x02 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x14 + x10 | 0, 9);
        x06 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x02 + x14 | 0, 13);
        x10 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x06 + x02 | 0, 18);
        x03 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x15 + x11 | 0, 7);
        x07 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x03 + x15 | 0, 9);
        x11 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x07 + x03 | 0, 13);
        x15 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x11 + x07 | 0, 18);
        x01 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x00 + x03 | 0, 7);
        x02 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x01 + x00 | 0, 9);
        x03 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x02 + x01 | 0, 13);
        x00 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x03 + x02 | 0, 18);
        x06 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x05 + x04 | 0, 7);
        x07 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x06 + x05 | 0, 9);
        x04 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x07 + x06 | 0, 13);
        x05 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x04 + x07 | 0, 18);
        x11 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x10 + x09 | 0, 7);
        x08 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x11 + x10 | 0, 9);
        x09 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x08 + x11 | 0, 13);
        x10 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x09 + x08 | 0, 18);
        x12 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x15 + x14 | 0, 7);
        x13 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x12 + x15 | 0, 9);
        x14 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x13 + x12 | 0, 13);
        x15 ^= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["rotl"])(x14 + x13 | 0, 18);
    }
    // Write output (salsa)
    out[oi++] = y00 + x00 | 0;
    out[oi++] = y01 + x01 | 0;
    out[oi++] = y02 + x02 | 0;
    out[oi++] = y03 + x03 | 0;
    out[oi++] = y04 + x04 | 0;
    out[oi++] = y05 + x05 | 0;
    out[oi++] = y06 + x06 | 0;
    out[oi++] = y07 + x07 | 0;
    out[oi++] = y08 + x08 | 0;
    out[oi++] = y09 + x09 | 0;
    out[oi++] = y10 + x10 | 0;
    out[oi++] = y11 + x11 | 0;
    out[oi++] = y12 + x12 | 0;
    out[oi++] = y13 + x13 | 0;
    out[oi++] = y14 + x14 | 0;
    out[oi++] = y15 + x15 | 0;
}
function BlockMix(input, ii, out, oi, r) {
    // The block B is r 128-byte chunks (which is equivalent of 2r 64-byte chunks)
    let head = oi + 0;
    let tail = oi + 16 * r;
    for(let i = 0; i < 16; i++)out[tail + i] = input[ii + (2 * r - 1) * 16 + i]; // X ← B[2r−1]
    for(let i = 0; i < r; i++, head += 16, ii += 16){
        // We write odd & even Yi at same time. Even: 0bXXXXX0 Odd:  0bXXXXX1
        XorAndSalsa(out, tail, input, ii, out, head); // head[i] = Salsa(blockIn[2*i] ^ tail[i-1])
        if (i > 0) tail += 16; // First iteration overwrites tmp value in tail
        XorAndSalsa(out, head, input, ii += 16, out, tail); // tail[i] = Salsa(blockIn[2*i+1] ^ head[i])
    }
}
// Common prologue and epilogue for sync/async functions
function scryptInit(password, salt, _opts) {
    // Maxmem - 1GB+1KB by default
    const opts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["checkOpts"])({
        dkLen: 32,
        asyncTick: 10,
        maxmem: 1024 ** 3 + 1024
    }, _opts);
    const { N, r, p, dkLen, asyncTick, maxmem, onProgress } = opts;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(N);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(r);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(p);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(dkLen);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(asyncTick);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["anumber"])(maxmem);
    if (onProgress !== undefined && typeof onProgress !== 'function') throw new Error('progressCb should be function');
    const blockSize = 128 * r;
    const blockSize32 = blockSize / 4;
    // Max N is 2^32 (Integrify is 32-bit). Real limit is 2^22: JS engines Uint8Array limit is 4GB in 2024.
    // Spec check `N >= 2^(blockSize / 8)` is not done for compat with popular libs,
    // which used incorrect r: 1, p: 8. Also, the check seems to be a spec error:
    // https://www.rfc-editor.org/errata_search.php?rfc=7914
    const pow32 = Math.pow(2, 32);
    if (N <= 1 || (N & N - 1) !== 0 || N > pow32) {
        throw new Error('Scrypt: N must be larger than 1, a power of 2, and less than 2^32');
    }
    if (p < 0 || p > (pow32 - 1) * 32 / blockSize) {
        throw new Error('Scrypt: p must be a positive integer less than or equal to ((2^32 - 1) * 32) / (128 * r)');
    }
    if (dkLen < 0 || dkLen > (pow32 - 1) * 32) {
        throw new Error('Scrypt: dkLen should be positive integer less than or equal to (2^32 - 1) * 32');
    }
    const memUsed = blockSize * (N + p);
    if (memUsed > maxmem) {
        throw new Error('Scrypt: memused is bigger than maxMem. Expected 128 * r * (N + p) > maxmem of ' + maxmem);
    }
    // [B0...Bp−1] ← PBKDF2HMAC-SHA256(Passphrase, Salt, 1, blockSize*ParallelizationFactor)
    // Since it has only one iteration there is no reason to use async variant
    const B = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$pbkdf2$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["pbkdf2"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$sha2$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["sha256"], password, salt, {
        c: 1,
        dkLen: blockSize * p
    });
    const B32 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["u32"])(B);
    // Re-used between parallel iterations. Array(iterations) of B
    const V = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["u32"])(new Uint8Array(blockSize * N));
    const tmp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["u32"])(new Uint8Array(blockSize));
    let blockMixCb = ()=>{};
    if (onProgress) {
        const totalBlockMix = 2 * N * p;
        // Invoke callback if progress changes from 10.01 to 10.02
        // Allows to draw smooth progress bar on up to 8K screen
        const callbackPer = Math.max(Math.floor(totalBlockMix / 10000), 1);
        let blockMixCnt = 0;
        blockMixCb = ()=>{
            blockMixCnt++;
            if (onProgress && (!(blockMixCnt % callbackPer) || blockMixCnt === totalBlockMix)) onProgress(blockMixCnt / totalBlockMix);
        };
    }
    return {
        N,
        r,
        p,
        dkLen,
        blockSize32,
        V,
        B32,
        B,
        tmp,
        blockMixCb,
        asyncTick
    };
}
function scryptOutput(password, dkLen, B, V, tmp) {
    const res = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$pbkdf2$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["pbkdf2"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$sha2$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["sha256"], password, B, {
        c: 1,
        dkLen
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["clean"])(B, V, tmp);
    return res;
}
function scrypt(password, salt, opts) {
    const { N, r, p, dkLen, blockSize32, V, B32, B, tmp, blockMixCb } = scryptInit(password, salt, opts);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["swap32IfBE"])(B32);
    for(let pi = 0; pi < p; pi++){
        const Pi = blockSize32 * pi;
        for(let i = 0; i < blockSize32; i++)V[i] = B32[Pi + i]; // V[0] = B[i]
        for(let i = 0, pos = 0; i < N - 1; i++){
            BlockMix(V, pos, V, pos += blockSize32, r); // V[i] = BlockMix(V[i-1]);
            blockMixCb();
        }
        BlockMix(V, (N - 1) * blockSize32, B32, Pi, r); // Process last element
        blockMixCb();
        for(let i = 0; i < N; i++){
            // First u32 of the last 64-byte block (u32 is LE)
            const j = B32[Pi + blockSize32 - 16] % N; // j = Integrify(X) % iterations
            for(let k = 0; k < blockSize32; k++)tmp[k] = B32[Pi + k] ^ V[j * blockSize32 + k]; // tmp = B ^ V[j]
            BlockMix(tmp, 0, B32, Pi, r); // B = BlockMix(B ^ V[j])
            blockMixCb();
        }
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["swap32IfBE"])(B32);
    return scryptOutput(password, dkLen, B, V, tmp);
}
async function scryptAsync(password, salt, opts) {
    const { N, r, p, dkLen, blockSize32, V, B32, B, tmp, blockMixCb, asyncTick } = scryptInit(password, salt, opts);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["swap32IfBE"])(B32);
    for(let pi = 0; pi < p; pi++){
        const Pi = blockSize32 * pi;
        for(let i = 0; i < blockSize32; i++)V[i] = B32[Pi + i]; // V[0] = B[i]
        let pos = 0;
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["asyncLoop"])(N - 1, asyncTick, ()=>{
            BlockMix(V, pos, V, pos += blockSize32, r); // V[i] = BlockMix(V[i-1]);
            blockMixCb();
        });
        BlockMix(V, (N - 1) * blockSize32, B32, Pi, r); // Process last element
        blockMixCb();
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["asyncLoop"])(N, asyncTick, ()=>{
            // First u32 of the last 64-byte block (u32 is LE)
            const j = B32[Pi + blockSize32 - 16] % N; // j = Integrify(X) % iterations
            for(let k = 0; k < blockSize32; k++)tmp[k] = B32[Pi + k] ^ V[j * blockSize32 + k]; // tmp = B ^ V[j]
            BlockMix(tmp, 0, B32, Pi, r); // B = BlockMix(B ^ V[j])
            blockMixCb();
        });
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$noble$2f$hashes$2f$esm$2f$utils$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["swap32IfBE"])(B32);
    return scryptOutput(password, dkLen, B, V, tmp);
} //# sourceMappingURL=scrypt.js.map
}}),
"[project]/node_modules/postgres/src [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`os`));
}}),
"[project]/node_modules/postgres/src [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`fs`));
}}),
"[project]/node_modules/postgres/src/query.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CLOSE": (()=>CLOSE),
    "Query": (()=>Query)
});
const originCache = new Map(), originStackCache = new Map(), originError = Symbol('OriginError');
const CLOSE = {};
class Query extends Promise {
    constructor(strings, args, handler, canceller, options = {}){
        let resolve, reject;
        super((a, b)=>{
            resolve = a;
            reject = b;
        });
        this.tagged = Array.isArray(strings.raw);
        this.strings = strings;
        this.args = args;
        this.handler = handler;
        this.canceller = canceller;
        this.options = options;
        this.state = null;
        this.statement = null;
        this.resolve = (x)=>(this.active = false, resolve(x));
        this.reject = (x)=>(this.active = false, reject(x));
        this.active = false;
        this.cancelled = null;
        this.executed = false;
        this.signature = '';
        this[originError] = this.handler.debug ? new Error() : this.tagged && cachedError(this.strings);
    }
    get origin() {
        return (this.handler.debug ? this[originError].stack : this.tagged && originStackCache.has(this.strings) ? originStackCache.get(this.strings) : originStackCache.set(this.strings, this[originError].stack).get(this.strings)) || '';
    }
    static get [Symbol.species]() {
        return Promise;
    }
    cancel() {
        return this.canceller && (this.canceller(this), this.canceller = null);
    }
    simple() {
        this.options.simple = true;
        this.options.prepare = false;
        return this;
    }
    async readable() {
        this.simple();
        this.streaming = true;
        return this;
    }
    async writable() {
        this.simple();
        this.streaming = true;
        return this;
    }
    cursor(rows = 1, fn) {
        this.options.simple = false;
        if (typeof rows === 'function') {
            fn = rows;
            rows = 1;
        }
        this.cursorRows = rows;
        if (typeof fn === 'function') return this.cursorFn = fn, this;
        let prev;
        return {
            [Symbol.asyncIterator]: ()=>({
                    next: ()=>{
                        if (this.executed && !this.active) return {
                            done: true
                        };
                        prev && prev();
                        const promise = new Promise((resolve, reject)=>{
                            this.cursorFn = (value)=>{
                                resolve({
                                    value,
                                    done: false
                                });
                                return new Promise((r)=>prev = r);
                            };
                            this.resolve = ()=>(this.active = false, resolve({
                                    done: true
                                }));
                            this.reject = (x)=>(this.active = false, reject(x));
                        });
                        this.execute();
                        return promise;
                    },
                    return () {
                        prev && prev(CLOSE);
                        return {
                            done: true
                        };
                    }
                })
        };
    }
    describe() {
        this.options.simple = false;
        this.onlyDescribe = this.options.prepare = true;
        return this;
    }
    stream() {
        throw new Error('.stream has been renamed to .forEach');
    }
    forEach(fn) {
        this.forEachFn = fn;
        this.handle();
        return this;
    }
    raw() {
        this.isRaw = true;
        return this;
    }
    values() {
        this.isRaw = 'values';
        return this;
    }
    async handle() {
        !this.executed && (this.executed = true) && await 1 && this.handler(this);
    }
    execute() {
        this.handle();
        return this;
    }
    then() {
        this.handle();
        return super.then.apply(this, arguments);
    }
    catch() {
        this.handle();
        return super.catch.apply(this, arguments);
    }
    finally() {
        this.handle();
        return super.finally.apply(this, arguments);
    }
}
function cachedError(xs) {
    if (originCache.has(xs)) return originCache.get(xs);
    const x = Error.stackTraceLimit;
    Error.stackTraceLimit = 4;
    originCache.set(xs, new Error());
    Error.stackTraceLimit = x;
    return originCache.get(xs);
}
}}),
"[project]/node_modules/postgres/src/errors.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Errors": (()=>Errors),
    "PostgresError": (()=>PostgresError)
});
class PostgresError extends Error {
    constructor(x){
        super(x.message);
        this.name = this.constructor.name;
        Object.assign(this, x);
    }
}
const Errors = {
    connection,
    postgres,
    generic,
    notSupported
};
function connection(x, options, socket) {
    const { host, port } = socket || options;
    const error = Object.assign(new Error('write ' + x + ' ' + (options.path || host + ':' + port)), {
        code: x,
        errno: x,
        address: options.path || host
    }, options.path ? {} : {
        port: port
    });
    Error.captureStackTrace(error, connection);
    return error;
}
function postgres(x) {
    const error = new PostgresError(x);
    Error.captureStackTrace(error, postgres);
    return error;
}
function generic(code, message) {
    const error = Object.assign(new Error(code + ': ' + message), {
        code
    });
    Error.captureStackTrace(error, generic);
    return error;
}
/* c8 ignore next 10 */ function notSupported(x) {
    const error = Object.assign(new Error(x + ' (B) is not supported'), {
        code: 'MESSAGE_NOT_SUPPORTED',
        name: x
    });
    Error.captureStackTrace(error, notSupported);
    return error;
}
}}),
"[project]/node_modules/postgres/src/types.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Builder": (()=>Builder),
    "END": (()=>END),
    "Identifier": (()=>Identifier),
    "Parameter": (()=>Parameter),
    "arrayParser": (()=>arrayParser),
    "arraySerializer": (()=>arraySerializer),
    "camel": (()=>camel),
    "escapeIdentifier": (()=>escapeIdentifier),
    "fromCamel": (()=>fromCamel),
    "fromKebab": (()=>fromKebab),
    "fromPascal": (()=>fromPascal),
    "handleValue": (()=>handleValue),
    "inferType": (()=>inferType),
    "kebab": (()=>kebab),
    "mergeUserTypes": (()=>mergeUserTypes),
    "parsers": (()=>parsers),
    "pascal": (()=>pascal),
    "serializers": (()=>serializers),
    "stringify": (()=>stringify),
    "toCamel": (()=>toCamel),
    "toKebab": (()=>toKebab),
    "toPascal": (()=>toPascal),
    "types": (()=>types)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:buffer [external] (node:buffer, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/query.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/errors.js [middleware-edge] (ecmascript)");
;
;
const types = {
    string: {
        to: 25,
        from: null,
        serialize: (x)=>'' + x
    },
    number: {
        to: 0,
        from: [
            21,
            23,
            26,
            700,
            701
        ],
        serialize: (x)=>'' + x,
        parse: (x)=>+x
    },
    json: {
        to: 114,
        from: [
            114,
            3802
        ],
        serialize: (x)=>JSON.stringify(x),
        parse: (x)=>JSON.parse(x)
    },
    boolean: {
        to: 16,
        from: 16,
        serialize: (x)=>x === true ? 't' : 'f',
        parse: (x)=>x === 't'
    },
    date: {
        to: 1184,
        from: [
            1082,
            1114,
            1184
        ],
        serialize: (x)=>(x instanceof Date ? x : new Date(x)).toISOString(),
        parse: (x)=>new Date(x)
    },
    bytea: {
        to: 17,
        from: 17,
        serialize: (x)=>'\\x' + __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(x).toString('hex'),
        parse: (x)=>__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(x.slice(2), 'hex')
    }
};
class NotTagged {
    then() {
        notTagged();
    }
    catch() {
        notTagged();
    }
    finally() {
        notTagged();
    }
}
class Identifier extends NotTagged {
    constructor(value){
        super();
        this.value = escapeIdentifier(value);
    }
}
class Parameter extends NotTagged {
    constructor(value, type, array){
        super();
        this.value = value;
        this.type = type;
        this.array = array;
    }
}
class Builder extends NotTagged {
    constructor(first, rest){
        super();
        this.first = first;
        this.rest = rest;
    }
    build(before, parameters, types, options) {
        const keyword = builders.map(([x, fn])=>({
                fn,
                i: before.search(x)
            })).sort((a, b)=>a.i - b.i).pop();
        return keyword.i === -1 ? escapeIdentifiers(this.first, options) : keyword.fn(this.first, this.rest, parameters, types, options);
    }
}
function handleValue(x, parameters, types, options) {
    let value = x instanceof Parameter ? x.value : x;
    if (value === undefined) {
        x instanceof Parameter ? x.value = options.transform.undefined : value = x = options.transform.undefined;
        if (value === undefined) throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].generic('UNDEFINED_VALUE', 'Undefined values are not allowed');
    }
    return '$' + types.push(x instanceof Parameter ? (parameters.push(x.value), x.array ? x.array[x.type || inferType(x.value)] || x.type || firstIsString(x.value) : x.type) : (parameters.push(x), inferType(x)));
}
const defaultHandlers = typeHandlers(types);
function stringify(q, string, value, parameters, types, options) {
    for(let i = 1; i < q.strings.length; i++){
        string += stringifyValue(string, value, parameters, types, options) + q.strings[i];
        value = q.args[i];
    }
    return string;
}
function stringifyValue(string, value, parameters, types, o) {
    return value instanceof Builder ? value.build(string, parameters, types, o) : value instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Query"] ? fragment(value, parameters, types, o) : value instanceof Identifier ? value.value : value && value[0] instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Query"] ? value.reduce((acc, x)=>acc + ' ' + fragment(x, parameters, types, o), '') : handleValue(value, parameters, types, o);
}
function fragment(q, parameters, types, options) {
    q.fragment = true;
    return stringify(q, q.strings[0], q.args[0], parameters, types, options);
}
function valuesBuilder(first, parameters, types, columns, options) {
    return first.map((row)=>'(' + columns.map((column)=>stringifyValue('values', row[column], parameters, types, options)).join(',') + ')').join(',');
}
function values(first, rest, parameters, types, options) {
    const multi = Array.isArray(first[0]);
    const columns = rest.length ? rest.flat() : Object.keys(multi ? first[0] : first);
    return valuesBuilder(multi ? first : [
        first
    ], parameters, types, columns, options);
}
function select(first, rest, parameters, types, options) {
    typeof first === 'string' && (first = [
        first
    ].concat(rest));
    if (Array.isArray(first)) return escapeIdentifiers(first, options);
    let value;
    const columns = rest.length ? rest.flat() : Object.keys(first);
    return columns.map((x)=>{
        value = first[x];
        return (value instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Query"] ? fragment(value, parameters, types, options) : value instanceof Identifier ? value.value : handleValue(value, parameters, types, options)) + ' as ' + escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x);
    }).join(',');
}
const builders = Object.entries({
    values,
    in: (...xs)=>{
        const x = values(...xs);
        return x === '()' ? '(null)' : x;
    },
    select,
    as: select,
    returning: select,
    '\\(': select,
    update (first, rest, parameters, types, options) {
        return (rest.length ? rest.flat() : Object.keys(first)).map((x)=>escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x) + '=' + stringifyValue('values', first[x], parameters, types, options));
    },
    insert (first, rest, parameters, types, options) {
        const columns = rest.length ? rest.flat() : Object.keys(Array.isArray(first) ? first[0] : first);
        return '(' + escapeIdentifiers(columns, options) + ')values' + valuesBuilder(Array.isArray(first) ? first : [
            first
        ], parameters, types, columns, options);
    }
}).map(([x, fn])=>[
        new RegExp('((?:^|[\\s(])' + x + '(?:$|[\\s(]))(?![\\s\\S]*\\1)', 'i'),
        fn
    ]);
function notTagged() {
    throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].generic('NOT_TAGGED_CALL', 'Query not called as a tagged template literal');
}
const serializers = defaultHandlers.serializers;
const parsers = defaultHandlers.parsers;
const END = {};
function firstIsString(x) {
    if (Array.isArray(x)) return firstIsString(x[0]);
    return typeof x === 'string' ? 1009 : 0;
}
const mergeUserTypes = function(types) {
    const user = typeHandlers(types || {});
    return {
        serializers: Object.assign({}, serializers, user.serializers),
        parsers: Object.assign({}, parsers, user.parsers)
    };
};
function typeHandlers(types) {
    return Object.keys(types).reduce((acc, k)=>{
        types[k].from && [].concat(types[k].from).forEach((x)=>acc.parsers[x] = types[k].parse);
        if (types[k].serialize) {
            acc.serializers[types[k].to] = types[k].serialize;
            types[k].from && [].concat(types[k].from).forEach((x)=>acc.serializers[x] = types[k].serialize);
        }
        return acc;
    }, {
        parsers: {},
        serializers: {}
    });
}
function escapeIdentifiers(xs, { transform: { column } }) {
    return xs.map((x)=>escapeIdentifier(column.to ? column.to(x) : x)).join(',');
}
const escapeIdentifier = function escape(str) {
    return '"' + str.replace(/"/g, '""').replace(/\./g, '"."') + '"';
};
const inferType = function inferType(x) {
    return x instanceof Parameter ? x.type : x instanceof Date ? 1184 : x instanceof Uint8Array ? 17 : x === true || x === false ? 16 : typeof x === 'bigint' ? 20 : Array.isArray(x) ? inferType(x[0]) : 0;
};
const escapeBackslash = /\\/g;
const escapeQuote = /"/g;
function arrayEscape(x) {
    return x.replace(escapeBackslash, '\\\\').replace(escapeQuote, '\\"');
}
const arraySerializer = function arraySerializer(xs, serializer, options, typarray) {
    if (Array.isArray(xs) === false) return xs;
    if (!xs.length) return '{}';
    const first = xs[0];
    // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter
    const delimiter = typarray === 1020 ? ';' : ',';
    if (Array.isArray(first) && !first.type) return '{' + xs.map((x)=>arraySerializer(x, serializer, options, typarray)).join(delimiter) + '}';
    return '{' + xs.map((x)=>{
        if (x === undefined) {
            x = options.transform.undefined;
            if (x === undefined) throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].generic('UNDEFINED_VALUE', 'Undefined values are not allowed');
        }
        return x === null ? 'null' : '"' + arrayEscape(serializer ? serializer(x.type ? x.value : x) : '' + x) + '"';
    }).join(delimiter) + '}';
};
const arrayParserState = {
    i: 0,
    char: null,
    str: '',
    quoted: false,
    last: 0
};
const arrayParser = function arrayParser(x, parser, typarray) {
    arrayParserState.i = arrayParserState.last = 0;
    return arrayParserLoop(arrayParserState, x, parser, typarray);
};
function arrayParserLoop(s, x, parser, typarray) {
    const xs = [];
    // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter
    const delimiter = typarray === 1020 ? ';' : ',';
    for(; s.i < x.length; s.i++){
        s.char = x[s.i];
        if (s.quoted) {
            if (s.char === '\\') {
                s.str += x[++s.i];
            } else if (s.char === '"') {
                xs.push(parser ? parser(s.str) : s.str);
                s.str = '';
                s.quoted = x[s.i + 1] === '"';
                s.last = s.i + 2;
            } else {
                s.str += s.char;
            }
        } else if (s.char === '"') {
            s.quoted = true;
        } else if (s.char === '{') {
            s.last = ++s.i;
            xs.push(arrayParserLoop(s, x, parser, typarray));
        } else if (s.char === '}') {
            s.quoted = false;
            s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i));
            s.last = s.i + 1;
            break;
        } else if (s.char === delimiter && s.p !== '}' && s.p !== '"') {
            xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i));
            s.last = s.i + 1;
        }
        s.p = s.char;
    }
    s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i + 1)) : x.slice(s.last, s.i + 1));
    return xs;
}
const toCamel = (x)=>{
    let str = x[0];
    for(let i = 1; i < x.length; i++)str += x[i] === '_' ? x[++i].toUpperCase() : x[i];
    return str;
};
const toPascal = (x)=>{
    let str = x[0].toUpperCase();
    for(let i = 1; i < x.length; i++)str += x[i] === '_' ? x[++i].toUpperCase() : x[i];
    return str;
};
const toKebab = (x)=>x.replace(/_/g, '-');
const fromCamel = (x)=>x.replace(/([A-Z])/g, '_$1').toLowerCase();
const fromPascal = (x)=>(x.slice(0, 1) + x.slice(1).replace(/([A-Z])/g, '_$1')).toLowerCase();
const fromKebab = (x)=>x.replace(/-/g, '_');
function createJsonTransform(fn) {
    return function jsonTransform(x, column) {
        return typeof x === 'object' && x !== null && (column.type === 114 || column.type === 3802) ? Array.isArray(x) ? x.map((x)=>jsonTransform(x, column)) : Object.entries(x).reduce((acc, [k, v])=>Object.assign(acc, {
                [fn(k)]: jsonTransform(v, column)
            }), {}) : x;
    };
}
toCamel.column = {
    from: toCamel
};
toCamel.value = {
    from: createJsonTransform(toCamel)
};
fromCamel.column = {
    to: fromCamel
};
const camel = {
    ...toCamel
};
camel.column.to = fromCamel;
toPascal.column = {
    from: toPascal
};
toPascal.value = {
    from: createJsonTransform(toPascal)
};
fromPascal.column = {
    to: fromPascal
};
const pascal = {
    ...toPascal
};
pascal.column.to = fromPascal;
toKebab.column = {
    from: toKebab
};
toKebab.value = {
    from: createJsonTransform(toKebab)
};
fromKebab.column = {
    to: fromKebab
};
const kebab = {
    ...toKebab
};
kebab.column.to = fromKebab;
}}),
"[project]/node_modules/postgres/src [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`net`));
}}),
"[project]/node_modules/postgres/src [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`tls`));
}}),
"[project]/node_modules/postgres/src [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`crypto`));
}}),
"[project]/node_modules/postgres/src [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`stream`));
}}),
"[project]/node_modules/postgres/src [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`perf_hooks`));
}}),
"[project]/node_modules/postgres/src/result.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Result)
});
class Result extends Array {
    constructor(){
        super();
        Object.defineProperties(this, {
            count: {
                value: null,
                writable: true
            },
            state: {
                value: null,
                writable: true
            },
            command: {
                value: null,
                writable: true
            },
            columns: {
                value: null,
                writable: true
            },
            statement: {
                value: null,
                writable: true
            }
        });
    }
    static get [Symbol.species]() {
        return Array;
    }
}
}}),
"[project]/node_modules/postgres/src/queue.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = Queue;
function Queue(initial = []) {
    let xs = initial.slice();
    let index = 0;
    return {
        get length () {
            return xs.length - index;
        },
        remove: (x)=>{
            const index = xs.indexOf(x);
            return index === -1 ? null : (xs.splice(index, 1), x);
        },
        push: (x)=>(xs.push(x), x),
        shift: ()=>{
            const out = xs[index++];
            if (index === xs.length) {
                index = 0;
                xs = [];
            } else {
                xs[index - 1] = undefined;
            }
            return out;
        }
    };
}
}}),
"[project]/node_modules/postgres/src/bytes.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:buffer [external] (node:buffer, cjs)");
const size = 256;
let buffer = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].allocUnsafe(size);
const messages = 'BCcDdEFfHPpQSX'.split('').reduce((acc, x)=>{
    const v = x.charCodeAt(0);
    acc[x] = ()=>{
        buffer[0] = v;
        b.i = 5;
        return b;
    };
    return acc;
}, {});
const b = Object.assign(reset, messages, {
    N: String.fromCharCode(0),
    i: 0,
    inc (x) {
        b.i += x;
        return b;
    },
    str (x) {
        const length = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].byteLength(x);
        fit(length);
        b.i += buffer.write(x, b.i, length, 'utf8');
        return b;
    },
    i16 (x) {
        fit(2);
        buffer.writeUInt16BE(x, b.i);
        b.i += 2;
        return b;
    },
    i32 (x, i) {
        if (i || i === 0) {
            buffer.writeUInt32BE(x, i);
            return b;
        }
        fit(4);
        buffer.writeUInt32BE(x, b.i);
        b.i += 4;
        return b;
    },
    z (x) {
        fit(x);
        buffer.fill(0, b.i, b.i + x);
        b.i += x;
        return b;
    },
    raw (x) {
        buffer = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            buffer.subarray(0, b.i),
            x
        ]);
        b.i = buffer.length;
        return b;
    },
    end (at = 1) {
        buffer.writeUInt32BE(b.i - at, at);
        const out = buffer.subarray(0, b.i);
        b.i = 0;
        buffer = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].allocUnsafe(size);
        return out;
    }
});
const __TURBOPACK__default__export__ = b;
function fit(x) {
    if (buffer.length - b.i < x) {
        const prev = buffer, length = prev.length;
        buffer = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].allocUnsafe(length + (length >> 1) + x);
        prev.copy(buffer);
    }
}
function reset() {
    b.i = 0;
    return b;
}
}}),
"[project]/node_modules/postgres/src/connection.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:buffer [external] (node:buffer, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/types.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/errors.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$result$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/result.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/queue.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/query.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/bytes.js [middleware-edge] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const __TURBOPACK__default__export__ = Connection;
let uid = 1;
const Sync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().S().end(), Flush = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().H().end(), SSLRequest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().i32(8).i32(80877103).end(8), ExecuteUnnamed = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().E().str(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).i32(0).end(),
    Sync
]), DescribeUnnamed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().D().str('S').str(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).end(), noop = ()=>{};
const retryRoutines = new Set([
    'FetchPreparedStatement',
    'RevalidateCachedQuery',
    'transformAssignedExpr'
]);
const errorFields = {
    83: 'severity_local',
    86: 'severity',
    67: 'code',
    77: 'message',
    68: 'detail',
    72: 'hint',
    80: 'position',
    112: 'internal_position',
    113: 'internal_query',
    87: 'where',
    115: 'schema_name',
    116: 'table_name',
    99: 'column_name',
    100: 'data type_name',
    110: 'constraint_name',
    70: 'file',
    76: 'line',
    82: 'routine' // R
};
function Connection(options, queues = {}, { onopen = noop, onend = noop, onclose = noop } = {}) {
    const { ssl, max, user, host, port, database, parsers, transform, onnotice, onnotify, onparameter, max_pipeline, keep_alive, backoff, target_session_attrs } = options;
    const sent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), id = uid++, backend = {
        pid: null,
        secret: null
    }, idleTimer = timer(end, options.idle_timeout), lifeTimer = timer(end, options.max_lifetime), connectTimer = timer(connectTimedOut, options.connect_timeout);
    let socket = null, cancelMessage, result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$result$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"](), incoming = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].alloc(0), needsTypes = options.fetch_types, backendParameters = {}, statements = {}, statementId = Math.random().toString(36).slice(2), statementCount = 1, closedDate = 0, remaining = 0, hostIndex = 0, retries = 0, length = 0, delay = 0, rows = 0, serverSignature = null, nextWriteTimer = null, terminated = false, incomings = null, results = null, initial = null, ending = null, stream = null, chunk = null, ended = null, nonce = null, query = null, final = null;
    const connection = {
        queue: queues.closed,
        idleTimer,
        connect (query) {
            initial = query;
            reconnect();
        },
        terminate,
        execute,
        cancel,
        end,
        count: 0,
        id
    };
    queues.closed && queues.closed.push(connection);
    return connection;
    "TURBOPACK unreachable";
    async function createSocket() {
        let x;
        try {
            x = options.socket ? await Promise.resolve(options.socket(options)) : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].Socket();
        } catch (e) {
            error(e);
            return;
        }
        x.on('error', error);
        x.on('close', closed);
        x.on('drain', drain);
        return x;
    }
    async function cancel({ pid, secret }, resolve, reject) {
        try {
            cancelMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().i32(16).i32(80877102).i32(pid).i32(secret).end(16);
            await connect();
            socket.once('error', reject);
            socket.once('close', resolve);
        } catch (error) {
            reject(error);
        }
    }
    function execute(q) {
        if (terminated) return queryError(q, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].connection('CONNECTION_DESTROYED', options));
        if (q.cancelled) return;
        try {
            q.state = backend;
            query ? sent.push(q) : (query = q, query.active = true);
            build(q);
            return write(toBuffer(q)) && !q.describeFirst && !q.cursorFn && sent.length < max_pipeline && (!q.options.onexecute || q.options.onexecute(connection));
        } catch (error) {
            sent.length === 0 && write(Sync);
            errored(error);
            return true;
        }
    }
    function toBuffer(q) {
        if (q.parameters.length >= 65534) throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].generic('MAX_PARAMETERS_EXCEEDED', 'Max number of parameters (65534) exceeded');
        return q.options.simple ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().Q().str(q.statement.string + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).end() : q.describeFirst ? __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            describe(q),
            Flush
        ]) : q.prepare ? q.prepared ? prepared(q) : __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            describe(q),
            prepared(q)
        ]) : unnamed(q);
    }
    function describe(q) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            Parse(q.statement.string, q.parameters, q.statement.types, q.statement.name),
            Describe('S', q.statement.name)
        ]);
    }
    function prepared(q) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            Bind(q.parameters, q.statement.types, q.statement.name, q.cursorName),
            q.cursorFn ? Execute('', q.cursorRows) : ExecuteUnnamed
        ]);
    }
    function unnamed(q) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            Parse(q.statement.string, q.parameters, q.statement.types),
            DescribeUnnamed,
            prepared(q)
        ]);
    }
    function build(q) {
        const parameters = [], types = [];
        const string = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["stringify"])(q, q.strings[0], q.args[0], parameters, types, options);
        !q.tagged && q.args.forEach((x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["handleValue"])(x, parameters, types, options));
        q.prepare = options.prepare && ('prepare' in q.options ? q.options.prepare : true);
        q.string = string;
        q.signature = q.prepare && types + string;
        q.onlyDescribe && delete statements[q.signature];
        q.parameters = q.parameters || parameters;
        q.prepared = q.prepare && q.signature in statements;
        q.describeFirst = q.onlyDescribe || parameters.length && !q.prepared;
        q.statement = q.prepared ? statements[q.signature] : {
            string,
            types,
            name: q.prepare ? statementId + statementCount++ : ''
        };
        typeof options.debug === 'function' && options.debug(id, string, parameters, types);
    }
    function write(x, fn) {
        chunk = chunk ? __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            chunk,
            x
        ]) : __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(x);
        if (fn || chunk.length >= 1024) return nextWrite(fn);
        nextWriteTimer === null && (nextWriteTimer = setImmediate(nextWrite));
        return true;
    }
    function nextWrite(fn) {
        const x = socket.write(chunk, fn);
        nextWriteTimer !== null && clearImmediate(nextWriteTimer);
        chunk = nextWriteTimer = null;
        return x;
    }
    function connectTimedOut() {
        errored(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].connection('CONNECT_TIMEOUT', options, socket));
        socket.destroy();
    }
    async function secure() {
        write(SSLRequest);
        const canSSL = await new Promise((r)=>socket.once('data', (x)=>r(x[0] === 83))) // S
        ;
        if (!canSSL && ssl === 'prefer') return connected();
        socket.removeAllListeners();
        socket = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].connect({
            socket,
            servername: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].isIP(socket.host) ? undefined : socket.host,
            ...ssl === 'require' || ssl === 'allow' || ssl === 'prefer' ? {
                rejectUnauthorized: false
            } : ssl === 'verify-full' ? {} : typeof ssl === 'object' ? ssl : {}
        });
        socket.on('secureConnect', connected);
        socket.on('error', error);
        socket.on('close', closed);
        socket.on('drain', drain);
    }
    /* c8 ignore next 3 */ function drain() {
        !query && onopen(connection);
    }
    function data(x) {
        if (incomings) {
            incomings.push(x);
            remaining -= x.length;
            if (remaining > 0) return;
        }
        incoming = incomings ? __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat(incomings, length - remaining) : incoming.length === 0 ? x : __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            incoming,
            x
        ], incoming.length + x.length);
        while(incoming.length > 4){
            length = incoming.readUInt32BE(1);
            if (length >= incoming.length) {
                remaining = length - incoming.length;
                incomings = [
                    incoming
                ];
                break;
            }
            try {
                handle(incoming.subarray(0, length + 1));
            } catch (e) {
                query && (query.cursorFn || query.describeFirst) && write(Sync);
                errored(e);
            }
            incoming = incoming.subarray(length + 1);
            remaining = 0;
            incomings = null;
        }
    }
    async function connect() {
        terminated = false;
        backendParameters = {};
        socket || (socket = await createSocket());
        if (!socket) return;
        connectTimer.start();
        if (options.socket) return ssl ? secure() : connected();
        socket.on('connect', ssl ? secure : connected);
        if (options.path) return socket.connect(options.path);
        socket.ssl = ssl;
        socket.connect(port[hostIndex], host[hostIndex]);
        socket.host = host[hostIndex];
        socket.port = port[hostIndex];
        hostIndex = (hostIndex + 1) % port.length;
    }
    function reconnect() {
        setTimeout(connect, closedDate ? closedDate + delay - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["performance"].now() : 0);
    }
    function connected() {
        try {
            statements = {};
            needsTypes = options.fetch_types;
            statementId = Math.random().toString(36).slice(2);
            statementCount = 1;
            lifeTimer.start();
            socket.on('data', data);
            keep_alive && socket.setKeepAlive && socket.setKeepAlive(true, 1000 * keep_alive);
            const s = StartupMessage();
            write(s);
        } catch (err) {
            error(err);
        }
    }
    function error(err) {
        if (connection.queue === queues.connecting && options.host[retries + 1]) return;
        errored(err);
        while(sent.length)queryError(sent.shift(), err);
    }
    function errored(err) {
        stream && (stream.destroy(err), stream = null);
        query && queryError(query, err);
        initial && (queryError(initial, err), initial = null);
    }
    function queryError(query, err) {
        if (query.reserve) return query.reject(err);
        if (!err || typeof err !== 'object') err = new Error(err);
        'query' in err || 'parameters' in err || Object.defineProperties(err, {
            stack: {
                value: err.stack + query.origin.replace(/.*\n/, '\n'),
                enumerable: options.debug
            },
            query: {
                value: query.string,
                enumerable: options.debug
            },
            parameters: {
                value: query.parameters,
                enumerable: options.debug
            },
            args: {
                value: query.args,
                enumerable: options.debug
            },
            types: {
                value: query.statement && query.statement.types,
                enumerable: options.debug
            }
        });
        query.reject(err);
    }
    function end() {
        return ending || (!connection.reserved && onend(connection), !connection.reserved && !initial && !query && sent.length === 0 ? (terminate(), new Promise((r)=>socket && socket.readyState !== 'closed' ? socket.once('close', r) : r())) : ending = new Promise((r)=>ended = r));
    }
    function terminate() {
        terminated = true;
        if (stream || query || initial || sent.length) error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].connection('CONNECTION_DESTROYED', options));
        clearImmediate(nextWriteTimer);
        if (socket) {
            socket.removeListener('data', data);
            socket.removeListener('connect', connected);
            socket.readyState === 'open' && socket.end((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().X().end());
        }
        ended && (ended(), ending = ended = null);
    }
    async function closed(hadError) {
        incoming = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].alloc(0);
        remaining = 0;
        incomings = null;
        clearImmediate(nextWriteTimer);
        socket.removeListener('data', data);
        socket.removeListener('connect', connected);
        idleTimer.cancel();
        lifeTimer.cancel();
        connectTimer.cancel();
        socket.removeAllListeners();
        socket = null;
        if (initial) return reconnect();
        !hadError && (query || sent.length) && error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].connection('CONNECTION_CLOSED', options, socket));
        closedDate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["performance"].now();
        hadError && options.shared.retries++;
        delay = (typeof backoff === 'function' ? backoff(options.shared.retries) : backoff) * 1000;
        onclose(connection, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].connection('CONNECTION_CLOSED', options, socket));
    }
    /* Handlers */ function handle(xs, x = xs[0]) {
        (x === 68 ? DataRow : x === 100 ? CopyData : x === 65 ? NotificationResponse : x === 83 ? ParameterStatus : x === 90 ? ReadyForQuery : x === 67 ? CommandComplete : x === 50 ? BindComplete : x === 49 ? ParseComplete : x === 116 ? ParameterDescription : x === 84 ? RowDescription : x === 82 ? Authentication : x === 110 ? NoData : x === 75 ? BackendKeyData : x === 69 ? ErrorResponse : x === 115 ? PortalSuspended : x === 51 ? CloseComplete : x === 71 ? CopyInResponse : x === 78 ? NoticeResponse : x === 72 ? CopyOutResponse : x === 99 ? CopyDone : x === 73 ? EmptyQueryResponse : x === 86 ? FunctionCallResponse : x === 118 ? NegotiateProtocolVersion : x === 87 ? CopyBothResponse : /* c8 ignore next */ UnknownMessage)(xs);
    }
    function DataRow(x) {
        let index = 7;
        let length;
        let column;
        let value;
        const row = query.isRaw ? new Array(query.statement.columns.length) : {};
        for(let i = 0; i < query.statement.columns.length; i++){
            column = query.statement.columns[i];
            length = x.readInt32BE(index);
            index += 4;
            value = length === -1 ? null : query.isRaw === true ? x.subarray(index, index += length) : column.parser === undefined ? x.toString('utf8', index, index += length) : column.parser.array === true ? column.parser(x.toString('utf8', index + 1, index += length)) : column.parser(x.toString('utf8', index, index += length));
            query.isRaw ? row[i] = query.isRaw === true ? value : transform.value.from ? transform.value.from(value, column) : value : row[column.name] = transform.value.from ? transform.value.from(value, column) : value;
        }
        query.forEachFn ? query.forEachFn(transform.row.from ? transform.row.from(row) : row, result) : result[rows++] = transform.row.from ? transform.row.from(row) : row;
    }
    function ParameterStatus(x) {
        const [k, v] = x.toString('utf8', 5, x.length - 1).split(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N);
        backendParameters[k] = v;
        if (options.parameters[k] !== v) {
            options.parameters[k] = v;
            onparameter && onparameter(k, v);
        }
    }
    function ReadyForQuery(x) {
        query && query.options.simple && query.resolve(results || result);
        query = results = null;
        result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$result$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"]();
        connectTimer.cancel();
        if (initial) {
            if (target_session_attrs) {
                if (!backendParameters.in_hot_standby || !backendParameters.default_transaction_read_only) return fetchState();
                else if (tryNext(target_session_attrs, backendParameters)) return terminate();
            }
            if (needsTypes) {
                initial.reserve && (initial = null);
                return fetchArrayTypes();
            }
            initial && !initial.reserve && execute(initial);
            options.shared.retries = retries = 0;
            initial = null;
            return;
        }
        while(sent.length && (query = sent.shift()) && (query.active = true, query.cancelled))Connection(options).cancel(query.state, query.cancelled.resolve, query.cancelled.reject);
        if (query) return; // Consider opening if able and sent.length < 50
        connection.reserved ? !connection.reserved.release && x[5] === 73 // I
         ? ending ? terminate() : (connection.reserved = null, onopen(connection)) : connection.reserved() : ending ? terminate() : onopen(connection);
    }
    function CommandComplete(x) {
        rows = 0;
        for(let i = x.length - 1; i > 0; i--){
            if (x[i] === 32 && x[i + 1] < 58 && result.count === null) result.count = +x.toString('utf8', i + 1, x.length - 1);
            if (x[i - 1] >= 65) {
                result.command = x.toString('utf8', 5, i);
                result.state = backend;
                break;
            }
        }
        final && (final(), final = null);
        if (result.command === 'BEGIN' && max !== 1 && !connection.reserved) return errored(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].generic('UNSAFE_TRANSACTION', 'Only use sql.begin, sql.reserved or max: 1'));
        if (query.options.simple) return BindComplete();
        if (query.cursorFn) {
            result.count && query.cursorFn(result);
            write(Sync);
        }
        query.resolve(result);
    }
    function ParseComplete() {
        query.parsing = false;
    }
    function BindComplete() {
        !result.statement && (result.statement = query.statement);
        result.columns = query.statement.columns;
    }
    function ParameterDescription(x) {
        const length = x.readUInt16BE(5);
        for(let i = 0; i < length; ++i)!query.statement.types[i] && (query.statement.types[i] = x.readUInt32BE(7 + i * 4));
        query.prepare && (statements[query.signature] = query.statement);
        query.describeFirst && !query.onlyDescribe && (write(prepared(query)), query.describeFirst = false);
    }
    function RowDescription(x) {
        if (result.command) {
            results = results || [
                result
            ];
            results.push(result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$result$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"]());
            result.count = null;
            query.statement.columns = null;
        }
        const length = x.readUInt16BE(5);
        let index = 7;
        let start;
        query.statement.columns = Array(length);
        for(let i = 0; i < length; ++i){
            start = index;
            while(x[index++] !== 0);
            const table = x.readUInt32BE(index);
            const number = x.readUInt16BE(index + 4);
            const type = x.readUInt32BE(index + 6);
            query.statement.columns[i] = {
                name: transform.column.from ? transform.column.from(x.toString('utf8', start, index - 1)) : x.toString('utf8', start, index - 1),
                parser: parsers[type],
                table,
                number,
                type
            };
            index += 18;
        }
        result.statement = query.statement;
        if (query.onlyDescribe) return query.resolve(query.statement), write(Sync);
    }
    async function Authentication(x, type = x.readUInt32BE(5)) {
        (type === 3 ? AuthenticationCleartextPassword : type === 5 ? AuthenticationMD5Password : type === 10 ? SASL : type === 11 ? SASLContinue : type === 12 ? SASLFinal : type !== 0 ? UnknownAuth : noop)(x, type);
    }
    /* c8 ignore next 5 */ async function AuthenticationCleartextPassword() {
        const payload = await Pass();
        write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().p().str(payload).z(1).end());
    }
    async function AuthenticationMD5Password(x) {
        const payload = 'md5' + await md5(__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(await md5(await Pass() + user)),
            x.subarray(9)
        ]));
        write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().p().str(payload).z(1).end());
    }
    async function SASL() {
        nonce = (await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].randomBytes(18)).toString('base64');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().p().str('SCRAM-SHA-256' + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N);
        const i = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].i;
        write(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].inc(4).str('n,,n=*,r=' + nonce).i32(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].i - i - 4, i).end());
    }
    async function SASLContinue(x) {
        const res = x.toString('utf8', 9).split(',').reduce((acc, x)=>(acc[x[0]] = x.slice(2), acc), {});
        const saltedPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].pbkdf2Sync(await Pass(), __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(res.s, 'base64'), parseInt(res.i), 32, 'sha256');
        const clientKey = await hmac(saltedPassword, 'Client Key');
        const auth = 'n=*,r=' + nonce + ',' + 'r=' + res.r + ',s=' + res.s + ',i=' + res.i + ',c=biws,r=' + res.r;
        serverSignature = (await hmac(await hmac(saltedPassword, 'Server Key'), auth)).toString('base64');
        const payload = 'c=biws,r=' + res.r + ',p=' + xor(clientKey, __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(await hmac(await sha256(clientKey), auth))).toString('base64');
        write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().p().str(payload).end());
    }
    function SASLFinal(x) {
        if (x.toString('utf8', 9).split(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N, 1)[0].slice(2) === serverSignature) return;
        /* c8 ignore next 5 */ errored(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].generic('SASL_SIGNATURE_MISMATCH', 'The server did not return the correct signature'));
        socket.destroy();
    }
    function Pass() {
        return Promise.resolve(typeof options.pass === 'function' ? options.pass() : options.pass);
    }
    function NoData() {
        result.statement = query.statement;
        result.statement.columns = [];
        if (query.onlyDescribe) return query.resolve(query.statement), write(Sync);
    }
    function BackendKeyData(x) {
        backend.pid = x.readUInt32BE(5);
        backend.secret = x.readUInt32BE(9);
    }
    async function fetchArrayTypes() {
        needsTypes = false;
        const types = await new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Query"]([
            `
      select b.oid, b.typarray
      from pg_catalog.pg_type a
      left join pg_catalog.pg_type b on b.oid = a.typelem
      where a.typcategory = 'A'
      group by b.oid, b.typarray
      order by b.oid
    `
        ], [], execute);
        types.forEach(({ oid, typarray })=>addArrayType(oid, typarray));
    }
    function addArrayType(oid, typarray) {
        if (!!options.parsers[typarray] && !!options.serializers[typarray]) return;
        const parser = options.parsers[oid];
        options.shared.typeArrayMap[oid] = typarray;
        options.parsers[typarray] = (xs)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["arrayParser"])(xs, parser, typarray);
        options.parsers[typarray].array = true;
        options.serializers[typarray] = (xs)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["arraySerializer"])(xs, options.serializers[oid], options, typarray);
    }
    function tryNext(x, xs) {
        return x === 'read-write' && xs.default_transaction_read_only === 'on' || x === 'read-only' && xs.default_transaction_read_only === 'off' || x === 'primary' && xs.in_hot_standby === 'on' || x === 'standby' && xs.in_hot_standby === 'off' || x === 'prefer-standby' && xs.in_hot_standby === 'off' && options.host[retries];
    }
    function fetchState() {
        const query = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Query"]([
            `
      show transaction_read_only;
      select pg_catalog.pg_is_in_recovery()
    `
        ], [], execute, null, {
            simple: true
        });
        query.resolve = ([[a], [b]])=>{
            backendParameters.default_transaction_read_only = a.transaction_read_only;
            backendParameters.in_hot_standby = b.pg_is_in_recovery ? 'on' : 'off';
        };
        query.execute();
    }
    function ErrorResponse(x) {
        query && (query.cursorFn || query.describeFirst) && write(Sync);
        const error = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].postgres(parseError(x));
        query && query.retried ? errored(query.retried) : query && query.prepared && retryRoutines.has(error.routine) ? retry(query, error) : errored(error);
    }
    function retry(q, error) {
        delete statements[q.signature];
        q.retried = error;
        execute(q);
    }
    function NotificationResponse(x) {
        if (!onnotify) return;
        let index = 9;
        while(x[index++] !== 0);
        onnotify(x.toString('utf8', 9, index - 1), x.toString('utf8', index, x.length - 1));
    }
    async function PortalSuspended() {
        try {
            const x = await Promise.resolve(query.cursorFn(result));
            rows = 0;
            x === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["CLOSE"] ? write(Close(query.portal)) : (result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$result$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"](), write(Execute('', query.cursorRows)));
        } catch (err) {
            write(Sync);
            query.reject(err);
        }
    }
    function CloseComplete() {
        result.count && query.cursorFn(result);
        query.resolve(result);
    }
    function CopyInResponse() {
        stream = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].Writable({
            autoDestroy: true,
            write (chunk, encoding, callback) {
                socket.write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().d().raw(chunk).end(), callback);
            },
            destroy (error, callback) {
                callback(error);
                socket.write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().f().str(error + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).end());
                stream = null;
            },
            final (callback) {
                socket.write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().c().end());
                final = callback;
            }
        });
        query.resolve(stream);
    }
    function CopyOutResponse() {
        stream = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].Readable({
            read () {
                socket.resume();
            }
        });
        query.resolve(stream);
    }
    /* c8 ignore next 3 */ function CopyBothResponse() {
        stream = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].Duplex({
            autoDestroy: true,
            read () {
                socket.resume();
            },
            /* c8 ignore next 11 */ write (chunk, encoding, callback) {
                socket.write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().d().raw(chunk).end(), callback);
            },
            destroy (error, callback) {
                callback(error);
                socket.write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().f().str(error + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).end());
                stream = null;
            },
            final (callback) {
                socket.write((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().c().end());
                final = callback;
            }
        });
        query.resolve(stream);
    }
    function CopyData(x) {
        stream && (stream.push(x.subarray(5)) || socket.pause());
    }
    function CopyDone() {
        stream && stream.push(null);
        stream = null;
    }
    function NoticeResponse(x) {
        onnotice ? onnotice(parseError(x)) : console.log(parseError(x)) // eslint-disable-line
        ;
    }
    /* c8 ignore next 3 */ function EmptyQueryResponse() {
    /* noop */ }
    /* c8 ignore next 3 */ function FunctionCallResponse() {
        errored(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].notSupported('FunctionCallResponse'));
    }
    /* c8 ignore next 3 */ function NegotiateProtocolVersion() {
        errored(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].notSupported('NegotiateProtocolVersion'));
    }
    /* c8 ignore next 3 */ function UnknownMessage(x) {
        console.error('Postgres.js : Unknown Message:', x[0]) // eslint-disable-line
        ;
    }
    /* c8 ignore next 3 */ function UnknownAuth(x, type) {
        console.error('Postgres.js : Unknown Auth:', type) // eslint-disable-line
        ;
    }
    /* Messages */ function Bind(parameters, types, statement = '', portal = '') {
        let prev, type;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().B().str(portal + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).str(statement + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).i16(0).i16(parameters.length);
        parameters.forEach((x, i)=>{
            if (x === null) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].i32(0xFFFFFFFF);
            type = types[i];
            parameters[i] = x = type in options.serializers ? options.serializers[type](x) : '' + x;
            prev = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].i;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].inc(4).str(x).i32(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].i - prev - 4, prev);
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].i16(0);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].end();
    }
    function Parse(str, parameters, types, name = '') {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().P().str(name + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).str(str + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).i16(parameters.length);
        parameters.forEach((x, i)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].i32(types[i] || 0));
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].end();
    }
    function Describe(x, name = '') {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().D().str(x).str(name + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).end();
    }
    function Execute(portal = '', rows = 0) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().E().str(portal + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).i32(rows).end(),
            Flush
        ]);
    }
    function Close(portal = '') {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().C().str('P').str(portal + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N).end(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().S().end()
        ]);
    }
    function StartupMessage() {
        return cancelMessage || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])().inc(4).i16(3).z(2).str(Object.entries(Object.assign({
            user,
            database,
            client_encoding: 'UTF8'
        }, options.connection)).filter(([, v])=>v).map(([k, v])=>k + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N + v).join(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$bytes$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].N)).z(2).end(0);
    }
}
function parseError(x) {
    const error = {};
    let start = 5;
    for(let i = 5; i < x.length - 1; i++){
        if (x[i] === 0) {
            error[errorFields[x[start]]] = x.toString('utf8', start + 1, i);
            start = i + 1;
        }
    }
    return error;
}
function md5(x) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].createHash('md5').update(x).digest('hex');
}
function hmac(key, x) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].createHmac('sha256', key).update(x).digest();
}
function sha256(x) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].createHash('sha256').update(x).digest();
}
function xor(a, b) {
    const length = Math.max(a.length, b.length);
    const buffer = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].allocUnsafe(length);
    for(let i = 0; i < length; i++)buffer[i] = a[i] ^ b[i];
    return buffer;
}
function timer(fn, seconds) {
    seconds = typeof seconds === 'function' ? seconds() : seconds;
    if (!seconds) return {
        cancel: noop,
        start: noop
    };
    let timer;
    return {
        cancel () {
            timer && (clearTimeout(timer), timer = null);
        },
        start () {
            timer && clearTimeout(timer);
            timer = setTimeout(done, seconds * 1000, arguments);
        }
    };
    "TURBOPACK unreachable";
    function done(args) {
        fn.apply(null, args);
        timer = null;
    }
}
}}),
"[project]/node_modules/postgres/src/subscribe.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Subscribe)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:buffer [external] (node:buffer, cjs)");
const noop = ()=>{};
function Subscribe(postgres, options) {
    const subscribers = new Map(), slot = 'postgresjs_' + Math.random().toString(36).slice(2), state = {};
    let connection, stream, ended = false;
    const sql = subscribe.sql = postgres({
        ...options,
        transform: {
            column: {},
            value: {},
            row: {}
        },
        max: 1,
        fetch_types: false,
        idle_timeout: null,
        max_lifetime: null,
        connection: {
            ...options.connection,
            replication: 'database'
        },
        onclose: async function() {
            if (ended) return;
            stream = null;
            state.pid = state.secret = undefined;
            connected(await init(sql, slot, options.publications));
            subscribers.forEach((event)=>event.forEach(({ onsubscribe })=>onsubscribe()));
        },
        no_subscribe: true
    });
    const end = sql.end, close = sql.close;
    sql.end = async ()=>{
        ended = true;
        stream && await new Promise((r)=>(stream.once('close', r), stream.end()));
        return end();
    };
    sql.close = async ()=>{
        stream && await new Promise((r)=>(stream.once('close', r), stream.end()));
        return close();
    };
    return subscribe;
    "TURBOPACK unreachable";
    async function subscribe(event, fn, onsubscribe = noop, onerror = noop) {
        event = parseEvent(event);
        if (!connection) connection = init(sql, slot, options.publications);
        const subscriber = {
            fn,
            onsubscribe
        };
        const fns = subscribers.has(event) ? subscribers.get(event).add(subscriber) : subscribers.set(event, new Set([
            subscriber
        ])).get(event);
        const unsubscribe = ()=>{
            fns.delete(subscriber);
            fns.size === 0 && subscribers.delete(event);
        };
        return connection.then((x)=>{
            connected(x);
            onsubscribe();
            stream && stream.on('error', onerror);
            return {
                unsubscribe,
                state,
                sql
            };
        });
    }
    function connected(x) {
        stream = x.stream;
        state.pid = x.state.pid;
        state.secret = x.state.secret;
    }
    async function init(sql, slot, publications) {
        if (!publications) throw new Error('Missing publication names');
        const xs = await sql.unsafe(`CREATE_REPLICATION_SLOT ${slot} TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`);
        const [x] = xs;
        const stream = await sql.unsafe(`START_REPLICATION SLOT ${slot} LOGICAL ${x.consistent_point} (proto_version '1', publication_names '${publications}')`).writable();
        const state = {
            lsn: __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].concat(x.consistent_point.split('/').map((x)=>__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(('00000000' + x).slice(-8), 'hex')))
        };
        stream.on('data', data);
        stream.on('error', error);
        stream.on('close', sql.close);
        return {
            stream,
            state: xs.state
        };
        "TURBOPACK unreachable";
        function error(e) {
            console.error('Unexpected error during logical streaming - reconnecting', e) // eslint-disable-line
            ;
        }
        function data(x) {
            if (x[0] === 0x77) {
                parse(x.subarray(25), state, sql.options.parsers, handle, options.transform);
            } else if (x[0] === 0x6b && x[17]) {
                state.lsn = x.subarray(1, 9);
                pong();
            }
        }
        function handle(a, b) {
            const path = b.relation.schema + '.' + b.relation.table;
            call('*', a, b);
            call('*:' + path, a, b);
            b.relation.keys.length && call('*:' + path + '=' + b.relation.keys.map((x)=>a[x.name]), a, b);
            call(b.command, a, b);
            call(b.command + ':' + path, a, b);
            b.relation.keys.length && call(b.command + ':' + path + '=' + b.relation.keys.map((x)=>a[x.name]), a, b);
        }
        function pong() {
            const x = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].alloc(34);
            x[0] = 'r'.charCodeAt(0);
            x.fill(state.lsn, 1);
            x.writeBigInt64BE(BigInt(Date.now() - Date.UTC(2000, 0, 1)) * BigInt(1000), 25);
            stream.write(x);
        }
    }
    function call(x, a, b) {
        subscribers.has(x) && subscribers.get(x).forEach(({ fn })=>fn(a, b, x));
    }
}
function Time(x) {
    return new Date(Date.UTC(2000, 0, 1) + Number(x / BigInt(1000)));
}
function parse(x, state, parsers, handle, transform) {
    const char = (acc, [k, v])=>(acc[k.charCodeAt(0)] = v, acc);
    Object.entries({
        R: (x)=>{
            let i = 1;
            const r = state[x.readUInt32BE(i)] = {
                schema: x.toString('utf8', i += 4, i = x.indexOf(0, i)) || 'pg_catalog',
                table: x.toString('utf8', i + 1, i = x.indexOf(0, i + 1)),
                columns: Array(x.readUInt16BE(i += 2)),
                keys: []
            };
            i += 2;
            let columnIndex = 0, column;
            while(i < x.length){
                column = r.columns[columnIndex++] = {
                    key: x[i++],
                    name: transform.column.from ? transform.column.from(x.toString('utf8', i, i = x.indexOf(0, i))) : x.toString('utf8', i, i = x.indexOf(0, i)),
                    type: x.readUInt32BE(i += 1),
                    parser: parsers[x.readUInt32BE(i)],
                    atttypmod: x.readUInt32BE(i += 4)
                };
                column.key && r.keys.push(column);
                i += 4;
            }
        },
        Y: ()=>{},
        O: ()=>{},
        B: (x)=>{
            state.date = Time(x.readBigInt64BE(9));
            state.lsn = x.subarray(1, 9);
        },
        I: (x)=>{
            let i = 1;
            const relation = state[x.readUInt32BE(i)];
            const { row } = tuples(x, relation.columns, i += 7, transform);
            handle(row, {
                command: 'insert',
                relation
            });
        },
        D: (x)=>{
            let i = 1;
            const relation = state[x.readUInt32BE(i)];
            i += 4;
            const key = x[i] === 75;
            handle(key || x[i] === 79 ? tuples(x, relation.columns, i += 3, transform).row : null, {
                command: 'delete',
                relation,
                key
            });
        },
        U: (x)=>{
            let i = 1;
            const relation = state[x.readUInt32BE(i)];
            i += 4;
            const key = x[i] === 75;
            const xs = key || x[i] === 79 ? tuples(x, relation.columns, i += 3, transform) : null;
            xs && (i = xs.i);
            const { row } = tuples(x, relation.columns, i + 3, transform);
            handle(row, {
                command: 'update',
                relation,
                key,
                old: xs && xs.row
            });
        },
        T: ()=>{},
        C: ()=>{} // Commit
    }).reduce(char, {})[x[0]](x);
}
function tuples(x, columns, xi, transform) {
    let type, column, value;
    const row = transform.raw ? new Array(columns.length) : {};
    for(let i = 0; i < columns.length; i++){
        type = x[xi++];
        column = columns[i];
        value = type === 110 // n
         ? null : type === 117 // u
         ? undefined : column.parser === undefined ? x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi)) : column.parser.array === true ? column.parser(x.toString('utf8', xi + 5, xi += 4 + x.readUInt32BE(xi))) : column.parser(x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi)));
        transform.raw ? row[i] = transform.raw === true ? value : transform.value.from ? transform.value.from(value, column) : value : row[column.name] = transform.value.from ? transform.value.from(value, column) : value;
    }
    return {
        i: xi,
        row: transform.row.from ? transform.row.from(row) : row
    };
}
function parseEvent(x) {
    const xs = x.match(/^(\*|insert|update|delete)?:?([^.]+?\.?[^=]+)?=?(.+)?/i) || [];
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const [, command, path, key] = xs;
    return (command || '*') + (path ? ':' + (path.indexOf('.') === -1 ? 'public.' + path : path) : '') + (key ? '=' + key : '');
}
}}),
"[project]/node_modules/postgres/src/large.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>largeObject)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src [middleware-edge] (ecmascript)");
;
function largeObject(sql, oid, mode = 0x00020000 | 0x00040000) {
    return new Promise(async (resolve, reject)=>{
        await sql.begin(async (sql)=>{
            let finish;
            !oid && ([{ oid }] = await sql`select lo_creat(-1) as oid`);
            const [{ fd }] = await sql`select lo_open(${oid}, ${mode}) as fd`;
            const lo = {
                writable,
                readable,
                close: ()=>sql`select lo_close(${fd})`.then(finish),
                tell: ()=>sql`select lo_tell64(${fd})`,
                read: (x)=>sql`select loread(${fd}, ${x}) as data`,
                write: (x)=>sql`select lowrite(${fd}, ${x})`,
                truncate: (x)=>sql`select lo_truncate64(${fd}, ${x})`,
                seek: (x, whence = 0)=>sql`select lo_lseek64(${fd}, ${x}, ${whence})`,
                size: ()=>sql`
          select
            lo_lseek64(${fd}, location, 0) as position,
            seek.size
          from (
            select
              lo_lseek64($1, 0, 2) as size,
              tell.location
            from (select lo_tell64($1) as location) tell
          ) seek
        `
            };
            resolve(lo);
            return new Promise(async (r)=>finish = r);
            "TURBOPACK unreachable";
            async function readable({ highWaterMark = 2048 * 8, start = 0, end = Infinity } = {}) {
                let max = end - start;
                start && await lo.seek(start);
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].Readable({
                    highWaterMark,
                    async read (size) {
                        const l = size > max ? size - max : size;
                        max -= size;
                        const [{ data }] = await lo.read(l);
                        this.push(data);
                        if (data.length < size) this.push(null);
                    }
                });
            }
            async function writable({ highWaterMark = 2048 * 8, start = 0 } = {}) {
                start && await lo.seek(start);
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].Writable({
                    highWaterMark,
                    write (chunk, encoding, callback) {
                        lo.write(chunk).then(()=>callback(), callback);
                    }
                });
            }
        }).catch(reject);
    });
}
}}),
"[project]/node_modules/postgres/src/index.js [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/types.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$connection$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/connection.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/query.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/queue.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/errors.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$subscribe$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/subscribe.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$large$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postgres/src/large.js [middleware-edge] (ecmascript)");
;
;
;
;
;
;
;
;
;
Object.assign(Postgres, {
    PostgresError: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PostgresError"],
    toPascal: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["toPascal"],
    pascal: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["pascal"],
    toCamel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["toCamel"],
    camel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["camel"],
    toKebab: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["toKebab"],
    kebab: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["kebab"],
    fromPascal: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["fromPascal"],
    fromCamel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["fromCamel"],
    fromKebab: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["fromKebab"],
    BigInt: {
        to: 20,
        from: [
            20
        ],
        parse: (x)=>BigInt(x),
        serialize: (x)=>x.toString()
    }
});
const __TURBOPACK__default__export__ = Postgres;
function Postgres(a, b) {
    const options = parseOptions(a, b), subscribe = options.no_subscribe || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$subscribe$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(Postgres, {
        ...options
    });
    let ending = false;
    const queries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), connecting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), reserved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), closed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), ended = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), open = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), busy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), full = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(), queues = {
        connecting,
        reserved,
        closed,
        ended,
        open,
        busy,
        full
    };
    const connections = [
        ...Array(options.max)
    ].map(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$connection$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(options, queues, {
            onopen,
            onend,
            onclose
        }));
    const sql = Sql(handler);
    Object.assign(sql, {
        get parameters () {
            return options.parameters;
        },
        largeObject: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$large$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].bind(null, sql),
        subscribe,
        CLOSE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["CLOSE"],
        END: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["CLOSE"],
        PostgresError: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PostgresError"],
        options,
        reserve,
        listen,
        begin,
        close,
        end
    });
    return sql;
    "TURBOPACK unreachable";
    function Sql(handler) {
        handler.debug = options.debug;
        Object.entries(options.types).reduce((acc, [name, type])=>{
            acc[name] = (x)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Parameter"](x, type.to);
            return acc;
        }, typed);
        Object.assign(sql, {
            types: typed,
            typed,
            unsafe,
            notify,
            array,
            json,
            file
        });
        return sql;
        "TURBOPACK unreachable";
        function typed(value, type) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Parameter"](value, type);
        }
        function sql(strings, ...args) {
            const query = strings && Array.isArray(strings.raw) ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Query"](strings, args, handler, cancel) : typeof strings === 'string' && !args.length ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Identifier"](options.transform.column.to ? options.transform.column.to(strings) : strings) : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Builder"](strings, args);
            return query;
        }
        function unsafe(string, args = [], options = {}) {
            arguments.length === 2 && !Array.isArray(args) && (options = args, args = []);
            const query = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Query"]([
                string
            ], args, handler, cancel, {
                prepare: false,
                ...options,
                simple: 'simple' in options ? options.simple : args.length === 0
            });
            return query;
        }
        function file(path, args = [], options = {}) {
            arguments.length === 2 && !Array.isArray(args) && (options = args, args = []);
            const query = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$query$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Query"]([], args, (query)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].readFile(path, 'utf8', (err, string)=>{
                    if (err) return query.reject(err);
                    query.strings = [
                        string
                    ];
                    handler(query);
                });
            }, cancel, {
                ...options,
                simple: 'simple' in options ? options.simple : args.length === 0
            });
            return query;
        }
    }
    async function listen(name, fn, onlisten) {
        const listener = {
            fn,
            onlisten
        };
        const sql = listen.sql || (listen.sql = Postgres({
            ...options,
            max: 1,
            idle_timeout: null,
            max_lifetime: null,
            fetch_types: false,
            onclose () {
                Object.entries(listen.channels).forEach(([name, { listeners }])=>{
                    delete listen.channels[name];
                    Promise.all(listeners.map((l)=>listen(name, l.fn, l.onlisten).catch(()=>{})));
                });
            },
            onnotify (c, x) {
                c in listen.channels && listen.channels[c].listeners.forEach((l)=>l.fn(x));
            }
        }));
        const channels = listen.channels || (listen.channels = {}), exists = name in channels;
        if (exists) {
            channels[name].listeners.push(listener);
            const result = await channels[name].result;
            listener.onlisten && listener.onlisten();
            return {
                state: result.state,
                unlisten
            };
        }
        channels[name] = {
            result: sql`listen ${sql.unsafe('"' + name.replace(/"/g, '""') + '"')}`,
            listeners: [
                listener
            ]
        };
        const result = await channels[name].result;
        listener.onlisten && listener.onlisten();
        return {
            state: result.state,
            unlisten
        };
        "TURBOPACK unreachable";
        async function unlisten() {
            if (name in channels === false) return;
            channels[name].listeners = channels[name].listeners.filter((x)=>x !== listener);
            if (channels[name].listeners.length) return;
            delete channels[name];
            return sql`unlisten ${sql.unsafe('"' + name.replace(/"/g, '""') + '"')}`;
        }
    }
    async function notify(channel, payload) {
        return await sql`select pg_notify(${channel}, ${'' + payload})`;
    }
    async function reserve() {
        const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])();
        const c = open.length ? open.shift() : await new Promise((resolve, reject)=>{
            const query = {
                reserve: resolve,
                reject
            };
            queries.push(query);
            closed.length && connect(closed.shift(), query);
        });
        move(c, reserved);
        c.reserved = ()=>queue.length ? c.execute(queue.shift()) : move(c, reserved);
        c.reserved.release = true;
        const sql = Sql(handler);
        sql.release = ()=>{
            c.reserved = null;
            onopen(c);
        };
        return sql;
        "TURBOPACK unreachable";
        function handler(q) {
            c.queue === full ? queue.push(q) : c.execute(q) || move(c, full);
        }
    }
    async function begin(options, fn) {
        !fn && (fn = options, options = '');
        const queries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$queue$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])();
        let savepoints = 0, connection, prepare = null;
        try {
            await sql.unsafe('begin ' + options.replace(/[^a-z ]/ig, ''), [], {
                onexecute
            }).execute();
            return await Promise.race([
                scope(connection, fn),
                new Promise((_, reject)=>connection.onclose = reject)
            ]);
        } catch (error) {
            throw error;
        }
        async function scope(c, fn, name) {
            const sql = Sql(handler);
            sql.savepoint = savepoint;
            sql.prepare = (x)=>prepare = x.replace(/[^a-z0-9$-_. ]/gi);
            let uncaughtError, result;
            name && await sql`savepoint ${sql(name)}`;
            try {
                result = await new Promise((resolve, reject)=>{
                    const x = fn(sql);
                    Promise.resolve(Array.isArray(x) ? Promise.all(x) : x).then(resolve, reject);
                });
                if (uncaughtError) throw uncaughtError;
            } catch (e) {
                await (name ? sql`rollback to ${sql(name)}` : sql`rollback`);
                throw e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["PostgresError"] && e.code === '25P02' && uncaughtError || e;
            }
            if (!name) {
                prepare ? await sql`prepare transaction '${sql.unsafe(prepare)}'` : await sql`commit`;
            }
            return result;
            "TURBOPACK unreachable";
            function savepoint(name, fn) {
                if (name && Array.isArray(name.raw)) return savepoint((sql)=>sql.apply(sql, arguments));
                arguments.length === 1 && (fn = name, name = null);
                return scope(c, fn, 's' + savepoints++ + (name ? '_' + name : ''));
            }
            function handler(q) {
                q.catch((e)=>uncaughtError || (uncaughtError = e));
                c.queue === full ? queries.push(q) : c.execute(q) || move(c, full);
            }
        }
        function onexecute(c) {
            connection = c;
            move(c, reserved);
            c.reserved = ()=>queries.length ? c.execute(queries.shift()) : move(c, reserved);
        }
    }
    function move(c, queue) {
        c.queue.remove(c);
        queue.push(c);
        c.queue = queue;
        queue === open ? c.idleTimer.start() : c.idleTimer.cancel();
        return c;
    }
    function json(x) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Parameter"](x, 3802);
    }
    function array(x, type) {
        if (!Array.isArray(x)) return array(Array.from(arguments));
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Parameter"](x, type || (x.length ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["inferType"])(x) || 25 : 0), options.shared.typeArrayMap);
    }
    function handler(query) {
        if (ending) return query.reject(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].connection('CONNECTION_ENDED', options, options));
        if (open.length) return go(open.shift(), query);
        if (closed.length) return connect(closed.shift(), query);
        busy.length ? go(busy.shift(), query) : queries.push(query);
    }
    function go(c, query) {
        return c.execute(query) ? move(c, busy) : move(c, full);
    }
    function cancel(query) {
        return new Promise((resolve, reject)=>{
            query.state ? query.active ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$connection$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(options).cancel(query.state, resolve, reject) : query.cancelled = {
                resolve,
                reject
            } : (queries.remove(query), query.cancelled = true, query.reject(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].generic('57014', 'canceling statement due to user request')), resolve());
        });
    }
    async function end({ timeout = null } = {}) {
        if (ending) return ending;
        await 1;
        let timer;
        return ending = Promise.race([
            new Promise((r)=>timeout !== null && (timer = setTimeout(destroy, timeout * 1000, r))),
            Promise.all(connections.map((c)=>c.end()).concat(listen.sql ? listen.sql.end({
                timeout: 0
            }) : [], subscribe.sql ? subscribe.sql.end({
                timeout: 0
            }) : []))
        ]).then(()=>clearTimeout(timer));
    }
    async function close() {
        await Promise.all(connections.map((c)=>c.end()));
    }
    async function destroy(resolve) {
        await Promise.all(connections.map((c)=>c.terminate()));
        while(queries.length)queries.shift().reject(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$errors$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Errors"].connection('CONNECTION_DESTROYED', options));
        resolve();
    }
    function connect(c, query) {
        move(c, connecting);
        c.connect(query);
        return c;
    }
    function onend(c) {
        move(c, ended);
    }
    function onopen(c) {
        if (queries.length === 0) return move(c, open);
        let max = Math.ceil(queries.length / (connecting.length + 1)), ready = true;
        while(ready && queries.length && max-- > 0){
            const query = queries.shift();
            if (query.reserve) return query.reserve(c);
            ready = c.execute(query);
        }
        ready ? move(c, busy) : move(c, full);
    }
    function onclose(c, e) {
        move(c, closed);
        c.reserved = null;
        c.onclose && (c.onclose(e), c.onclose = null);
        options.onclose && options.onclose(c.id);
        queries.length && connect(c, queries.shift());
    }
}
function parseOptions(a, b) {
    if (a && a.shared) return a;
    const env = process.env // eslint-disable-line
    , o = (!a || typeof a === 'string' ? b : a) || {}, { url, multihost } = parseUrl(a), query = [
        ...url.searchParams
    ].reduce((a, [b, c])=>(a[b] = c, a), {}), host = o.hostname || o.host || multihost || url.hostname || env.PGHOST || 'localhost', port = o.port || url.port || env.PGPORT || 5432, user = o.user || o.username || url.username || env.PGUSERNAME || env.PGUSER || osUsername();
    o.no_prepare && (o.prepare = false);
    query.sslmode && (query.ssl = query.sslmode, delete query.sslmode);
    'timeout' in o && (console.log('The timeout option is deprecated, use idle_timeout instead'), o.idle_timeout = o.timeout) // eslint-disable-line
    ;
    query.sslrootcert === 'system' && (query.ssl = 'verify-full');
    const ints = [
        'idle_timeout',
        'connect_timeout',
        'max_lifetime',
        'max_pipeline',
        'backoff',
        'keep_alive'
    ];
    const defaults = {
        max: 10,
        ssl: false,
        idle_timeout: null,
        connect_timeout: 30,
        max_lifetime: max_lifetime,
        max_pipeline: 100,
        backoff: backoff,
        keep_alive: 60,
        prepare: true,
        debug: false,
        fetch_types: true,
        publications: 'alltables',
        target_session_attrs: null
    };
    return {
        host: Array.isArray(host) ? host : host.split(',').map((x)=>x.split(':')[0]),
        port: Array.isArray(port) ? port : host.split(',').map((x)=>parseInt(x.split(':')[1] || port)),
        path: o.path || host.indexOf('/') > -1 && host + '/.s.PGSQL.' + port,
        database: o.database || o.db || (url.pathname || '').slice(1) || env.PGDATABASE || user,
        user: user,
        pass: o.pass || o.password || url.password || env.PGPASSWORD || '',
        ...Object.entries(defaults).reduce((acc, [k, d])=>{
            const value = k in o ? o[k] : k in query ? query[k] === 'disable' || query[k] === 'false' ? false : query[k] : env['PG' + k.toUpperCase()] || d;
            acc[k] = typeof value === 'string' && ints.includes(k) ? +value : value;
            return acc;
        }, {}),
        connection: {
            application_name: env.PGAPPNAME || 'postgres.js',
            ...o.connection,
            ...Object.entries(query).reduce((acc, [k, v])=>(k in defaults || (acc[k] = v), acc), {})
        },
        types: o.types || {},
        target_session_attrs: tsa(o, url, env),
        onnotice: o.onnotice,
        onnotify: o.onnotify,
        onclose: o.onclose,
        onparameter: o.onparameter,
        socket: o.socket,
        transform: parseTransform(o.transform || {
            undefined: undefined
        }),
        parameters: {},
        shared: {
            retries: 0,
            typeArrayMap: {}
        },
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src$2f$types$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["mergeUserTypes"])(o.types)
    };
}
function tsa(o, url, env) {
    const x = o.target_session_attrs || url.searchParams.get('target_session_attrs') || env.PGTARGETSESSIONATTRS;
    if (!x || [
        'read-write',
        'read-only',
        'primary',
        'standby',
        'prefer-standby'
    ].includes(x)) return x;
    throw new Error('target_session_attrs ' + x + ' is not supported');
}
function backoff(retries) {
    return (0.5 + Math.random() / 2) * Math.min(3 ** retries / 100, 20);
}
function max_lifetime() {
    return 60 * (30 + Math.random() * 30);
}
function parseTransform(x) {
    return {
        undefined: x.undefined,
        column: {
            from: typeof x.column === 'function' ? x.column : x.column && x.column.from,
            to: x.column && x.column.to
        },
        value: {
            from: typeof x.value === 'function' ? x.value : x.value && x.value.from,
            to: x.value && x.value.to
        },
        row: {
            from: typeof x.row === 'function' ? x.row : x.row && x.row.from,
            to: x.row && x.row.to
        }
    };
}
function parseUrl(url) {
    if (!url || typeof url !== 'string') return {
        url: {
            searchParams: new Map()
        }
    };
    let host = url;
    host = host.slice(host.indexOf('://') + 3).split(/[?/]/)[0];
    host = decodeURIComponent(host.slice(host.indexOf('@') + 1));
    const urlObj = new URL(url.replace(host, host.split(',')[0]));
    return {
        url: {
            username: decodeURIComponent(urlObj.username),
            password: decodeURIComponent(urlObj.password),
            host: urlObj.host,
            hostname: urlObj.hostname,
            port: urlObj.port,
            pathname: urlObj.pathname,
            searchParams: urlObj.searchParams
        },
        multihost: host.indexOf(',') > -1 && host
    };
}
function osUsername() {
    try {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postgres$2f$src__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].userInfo().username // eslint-disable-line
        ;
    } catch (_) {
        return process.env.USERNAME || process.env.USER || process.env.LOGNAME // eslint-disable-line
        ;
    }
}
}}),
}]);

//# sourceMappingURL=node_modules_8834b263._.js.map