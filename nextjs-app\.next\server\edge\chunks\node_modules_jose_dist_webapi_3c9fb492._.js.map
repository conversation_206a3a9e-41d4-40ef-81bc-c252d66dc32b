{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/buffer_utils.js"], "sourcesContent": ["export const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\n"], "names": [], "mappings": ";;;;;;;AAAO,MAAM,UAAU,IAAI;AACpB,MAAM,UAAU,IAAI;AAC3B,MAAM,YAAY,KAAK;AAChB,SAAS,OAAO,GAAG,OAAO;IAC7B,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,GAAK,MAAM,QAAQ;IAC/D,MAAM,MAAM,IAAI,WAAW;IAC3B,IAAI,IAAI;IACR,KAAK,MAAM,UAAU,QAAS;QAC1B,IAAI,GAAG,CAAC,QAAQ;QAChB,KAAK,OAAO,MAAM;IACtB;IACA,OAAO;AACX;AACA,SAAS,cAAc,GAAG,EAAE,KAAK,EAAE,MAAM;IACrC,IAAI,QAAQ,KAAK,SAAS,WAAW;QACjC,MAAM,IAAI,WAAW,CAAC,0BAA0B,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO;IACxF;IACA,IAAI,GAAG,CAAC;QAAC,UAAU;QAAI,UAAU;QAAI,UAAU;QAAG,QAAQ;KAAK,EAAE;AACrE;AACO,SAAS,SAAS,KAAK;IAC1B,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,MAAM,MAAM,QAAQ;IACpB,MAAM,MAAM,IAAI,WAAW;IAC3B,cAAc,KAAK,MAAM;IACzB,cAAc,KAAK,KAAK;IACxB,OAAO;AACX;AACO,SAAS,SAAS,KAAK;IAC1B,MAAM,MAAM,IAAI,WAAW;IAC3B,cAAc,KAAK;IACnB,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/base64.js"], "sourcesContent": ["export function encodeBase64(input) {\n    if (Uint8Array.prototype.toBase64) {\n        return input.toBase64();\n    }\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < input.length; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(''));\n}\nexport function decodeBase64(encoded) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(encoded);\n    }\n    const binary = atob(encoded);\n    const bytes = new Uint8Array(binary.length);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return bytes;\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,aAAa,KAAK;IAC9B,IAAI,WAAW,SAAS,CAAC,QAAQ,EAAE;QAC/B,OAAO,MAAM,QAAQ;IACzB;IACA,MAAM,aAAa;IACnB,MAAM,MAAM,EAAE;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,WAAY;QAC/C,IAAI,IAAI,CAAC,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,MAAM,QAAQ,CAAC,GAAG,IAAI;IACnE;IACA,OAAO,KAAK,IAAI,IAAI,CAAC;AACzB;AACO,SAAS,aAAa,OAAO;IAChC,IAAI,WAAW,UAAU,EAAE;QACvB,OAAO,WAAW,UAAU,CAAC;IACjC;IACA,MAAM,SAAS,KAAK;IACpB,MAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,KAAK,CAAC,EAAE,GAAG,OAAO,UAAU,CAAC;IACjC;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/util/base64url.js"], "sourcesContent": ["import { encoder, decoder } from '../lib/buffer_utils.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nexport function decode(input) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(typeof input === 'string' ? input : decoder.decode(input), {\n            alphabet: 'base64url',\n        });\n    }\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    encoded = encoded.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, '');\n    try {\n        return decodeBase64(encoded);\n    }\n    catch {\n        throw new TypeError('The input to be decoded is not correctly encoded.');\n    }\n}\nexport function encode(input) {\n    let unencoded = input;\n    if (typeof unencoded === 'string') {\n        unencoded = encoder.encode(unencoded);\n    }\n    if (Uint8Array.prototype.toBase64) {\n        return unencoded.toBase64({ alphabet: 'base64url', omitPadding: true });\n    }\n    return encodeBase64(unencoded).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,OAAO,KAAK;IACxB,IAAI,WAAW,UAAU,EAAE;QACvB,OAAO,WAAW,UAAU,CAAC,OAAO,UAAU,WAAW,QAAQ,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,QAAQ;YACpF,UAAU;QACd;IACJ;IACA,IAAI,UAAU;IACd,IAAI,mBAAmB,YAAY;QAC/B,UAAU,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IAC7B;IACA,UAAU,QAAQ,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,OAAO;IACvE,IAAI;QACA,OAAO,CAAA,GAAA,+JAAA,CAAA,eAAY,AAAD,EAAE;IACxB,EACA,OAAM;QACF,MAAM,IAAI,UAAU;IACxB;AACJ;AACO,SAAS,OAAO,KAAK;IACxB,IAAI,YAAY;IAChB,IAAI,OAAO,cAAc,UAAU;QAC/B,YAAY,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IAC/B;IACA,IAAI,WAAW,SAAS,CAAC,QAAQ,EAAE;QAC/B,OAAO,UAAU,QAAQ,CAAC;YAAE,UAAU;YAAa,aAAa;QAAK;IACzE;IACA,OAAO,CAAA,GAAA,+JAAA,CAAA,eAAY,AAAD,EAAE,WAAW,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;AACxF", "ignoreList": [0]}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/is_object.js"], "sourcesContent": ["function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default (input) => {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,aAAa,KAAK;IACvB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD;uCACe,CAAC;IACZ,IAAI,CAAC,aAAa,UAAU,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,mBAAmB;QACrF,OAAO;IACX;IACA,IAAI,OAAO,cAAc,CAAC,WAAW,MAAM;QACvC,OAAO;IACX;IACA,IAAI,QAAQ;IACZ,MAAO,OAAO,cAAc,CAAC,WAAW,KAAM;QAC1C,QAAQ,OAAO,cAAc,CAAC;IAClC;IACA,OAAO,OAAO,cAAc,CAAC,WAAW;AAC5C", "ignoreList": [0]}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/util/errors.js"], "sourcesContent": ["export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAO,MAAM,kBAAkB;IAC3B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;IAC1B,YAAY,OAAO,EAAE,OAAO,CAAE;QAC1B,KAAK,CAAC,SAAS;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,MAAM,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAAC,WAAW;IACpD;AACJ;AACO,MAAM,iCAAiC;IAC1C,OAAO,OAAO,kCAAkC;IAChD,OAAO,kCAAkC;IACzC,MAAM;IACN,OAAO;IACP,QAAQ;IACR,YAAY,OAAO,EAAE,OAAO,EAAE,QAAQ,aAAa,EAAE,SAAS,aAAa,CAAE;QACzE,KAAK,CAAC,SAAS;YAAE,OAAO;gBAAE;gBAAO;gBAAQ;YAAQ;QAAE;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;IACzB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,YAAY,OAAO,EAAE,OAAO,EAAE,QAAQ,aAAa,EAAE,SAAS,aAAa,CAAE;QACzE,KAAK,CAAC,SAAS;YAAE,OAAO;gBAAE;gBAAO;gBAAQ;YAAQ;QAAE;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,0BAA0B;IACnC,OAAO,OAAO,2BAA2B;IACzC,OAAO,2BAA2B;AACtC;AACO,MAAM,yBAAyB;IAClC,OAAO,OAAO,yBAAyB;IACvC,OAAO,yBAAyB;AACpC;AACO,MAAM,4BAA4B;IACrC,OAAO,OAAO,4BAA4B;IAC1C,OAAO,4BAA4B;IACnC,YAAY,UAAU,6BAA6B,EAAE,OAAO,CAAE;QAC1D,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,oBAAoB;IAC7B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;AAC9B;AACO,MAAM,0BAA0B;IACnC,OAAO,OAAO,2BAA2B;IACzC,OAAO,2BAA2B;IAClC,YAAY,UAAU,iDAAiD,EAAE,OAAO,CAAE;QAC9E,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,iCAAiC;IAC1C,CAAC,OAAO,aAAa,CAAC,CAAC;IACvB,OAAO,OAAO,kCAAkC;IAChD,OAAO,kCAAkC;IACzC,YAAY,UAAU,sDAAsD,EAAE,OAAO,CAAE;QACnF,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,oBAAoB;IAC7B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;IAC1B,YAAY,UAAU,mBAAmB,EAAE,OAAO,CAAE;QAChD,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,uCAAuC;IAChD,OAAO,OAAO,wCAAwC;IACtD,OAAO,wCAAwC;IAC/C,YAAY,UAAU,+BAA+B,EAAE,OAAO,CAAE;QAC5D,KAAK,CAAC,SAAS;IACnB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/util/decode_jwt.js"], "sourcesContent": ["import { decode as b64u } from './base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nimport { JWTInvalid } from './errors.js';\nexport function decodeJwt(jwt) {\n    if (typeof jwt !== 'string')\n        throw new JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');\n    const { 1: payload, length } = jwt.split('.');\n    if (length === 5)\n        throw new JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');\n    if (length !== 3)\n        throw new JWTInvalid('Invalid JWT');\n    if (!payload)\n        throw new JWTInvalid('JWTs must contain a payload');\n    let decoded;\n    try {\n        decoded = b64u(payload);\n    }\n    catch {\n        throw new JWTInvalid('Failed to base64url decode the payload');\n    }\n    let result;\n    try {\n        result = JSON.parse(decoder.decode(decoded));\n    }\n    catch {\n        throw new JWTInvalid('Failed to parse the decoded payload as JSON');\n    }\n    if (!isObject(result))\n        throw new JWTInvalid('Invalid JWT Claims Set');\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,UAAU,GAAG;IACzB,IAAI,OAAO,QAAQ,UACf,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB,MAAM,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,CAAC;IACzC,IAAI,WAAW,GACX,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB,IAAI,WAAW,GACX,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB,IAAI,CAAC,SACD,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB,IAAI;IACJ,IAAI;QACA,UAAU,CAAA,GAAA,mKAAA,CAAA,SAAI,AAAD,EAAE;IACnB,EACA,OAAM;QACF,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI;IACJ,IAAI;QACA,SAAS,KAAK,KAAK,CAAC,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACvC,EACA,OAAM;QACF,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,SACV,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/util/decode_protected_header.js"], "sourcesContent": ["import { decode as b64u } from './base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nexport function decodeProtectedHeader(token) {\n    let protectedB64u;\n    if (typeof token === 'string') {\n        const parts = token.split('.');\n        if (parts.length === 3 || parts.length === 5) {\n            ;\n            [protectedB64u] = parts;\n        }\n    }\n    else if (typeof token === 'object' && token) {\n        if ('protected' in token) {\n            protectedB64u = token.protected;\n        }\n        else {\n            throw new TypeError('Token does not contain a Protected Header');\n        }\n    }\n    try {\n        if (typeof protectedB64u !== 'string' || !protectedB64u) {\n            throw new Error();\n        }\n        const result = JSON.parse(decoder.decode(b64u(protectedB64u)));\n        if (!isObject(result)) {\n            throw new Error();\n        }\n        return result;\n    }\n    catch {\n        throw new TypeError('Invalid Token or Protected Header formatting');\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,sBAAsB,KAAK;IACvC,IAAI;IACJ,IAAI,OAAO,UAAU,UAAU;QAC3B,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,GAAG;;YAE1C,CAAC,cAAc,GAAG;QACtB;IACJ,OACK,IAAI,OAAO,UAAU,YAAY,OAAO;QACzC,IAAI,eAAe,OAAO;YACtB,gBAAgB,MAAM,SAAS;QACnC,OACK;YACD,MAAM,IAAI,UAAU;QACxB;IACJ;IACA,IAAI;QACA,IAAI,OAAO,kBAAkB,YAAY,CAAC,eAAe;YACrD,MAAM,IAAI;QACd;QACA,MAAM,SAAS,KAAK,KAAK,CAAC,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAA,GAAA,mKAAA,CAAA,SAAI,AAAD,EAAE;QAC9C,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,SAAS;YACnB,MAAM,IAAI;QACd;QACA,OAAO;IACX,EACA,OAAM;QACF,MAAM,IAAI,UAAU;IACxB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/subtle_dsa.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nexport default (alg, algorithm) => {\n    const hash = `SHA-${alg.slice(-3)}`;\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512':\n            return { hash, name: '<PERSON><PERSON>' };\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { hash, name: 'RSA-PSS', saltLength: parseInt(alg.slice(-3), 10) >> 3 };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { hash, name: 'RSASSA-PKCS1-v1_5' };\n        case 'ES256':\n        case 'ES384':\n        case 'ES512':\n            return { hash, name: 'ECDS<PERSON>', namedCurve: algorithm.namedCurve };\n        case 'Ed25519':\n        case 'EdDSA':\n            return { name: 'Ed25519' };\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n};\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAC,KAAK;IACjB,MAAM,OAAO,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI;IACnC,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;gBAAE;gBAAM,MAAM;YAAO;QAChC,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;gBAAE;gBAAM,MAAM;gBAAW,YAAY,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI,OAAO;YAAE;QACjF,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;gBAAE;gBAAM,MAAM;YAAoB;QAC7C,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;gBAAE;gBAAM,MAAM;gBAAS,YAAY,UAAU,UAAU;YAAC;QACnE,KAAK;QACL,KAAK;YACD,OAAO;gBAAE,MAAM;YAAU;QAC7B;YACI,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IAC1G;AACJ", "ignoreList": [0]}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/check_key_length.js"], "sourcesContent": ["export default (alg, key) => {\n    if (alg.startsWith('RS') || alg.startsWith('PS')) {\n        const { modulusLength } = key.algorithm;\n        if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n            throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n        }\n    }\n};\n"], "names": [], "mappings": ";;;uCAAe,CAAC,KAAK;IACjB,IAAI,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,OAAO;QAC9C,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,SAAS;QACvC,IAAI,OAAO,kBAAkB,YAAY,gBAAgB,MAAM;YAC3D,MAAM,IAAI,UAAU,GAAG,IAAI,qDAAqD,CAAC;QACrF;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/crypto_key.js"], "sourcesContent": ["function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usage) {\n    if (usage && !key.usages.includes(usage)) {\n        throw new TypeError(`CryptoKey does not support this operation, its usages must include ${usage}.`);\n    }\n}\nexport function checkSigCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\nexport function checkEncCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                    break;\n                default:\n                    throw unusable('ECDH or X25519');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,SAAS,IAAI,EAAE,OAAO,gBAAgB;IAC3C,OAAO,IAAI,UAAU,CAAC,+CAA+C,EAAE,KAAK,SAAS,EAAE,MAAM;AACjG;AACA,SAAS,YAAY,SAAS,EAAE,IAAI;IAChC,OAAO,UAAU,IAAI,KAAK;AAC9B;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;AACxC;AACA,SAAS,cAAc,GAAG;IACtB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,MAAM;IACxB;AACJ;AACA,SAAS,WAAW,GAAG,EAAE,KAAK;IAC1B,IAAI,SAAS,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ;QACtC,MAAM,IAAI,UAAU,CAAC,mEAAmE,EAAE,MAAM,CAAC,CAAC;IACtG;AACJ;AACO,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,KAAK;IAC7C,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,SAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,sBAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,UAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,cAAc;gBAC/B,MAAM,SAAS,IAAI,SAAS,CAAC,UAAU;gBACvC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,WAAW,KAAK;AACpB;AACO,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,KAAK;IAC7C,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACZ,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;gBAC3C,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM;gBACnC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,WAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;gBAC3C,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM;gBACnC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA,KAAK;YAAQ;gBACT,OAAQ,IAAI,SAAS,CAAC,IAAI;oBACtB,KAAK;oBACL,KAAK;wBACD;oBACJ;wBACI,MAAM,SAAS;gBACvB;gBACA;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,WAC5B,MAAM,SAAS;YACnB;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,aAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI,OAAO;gBAC/C,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,WAAW,KAAK;AACpB", "ignoreList": [0]}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/invalid_key_input.js"], "sourcesContent": ["function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK;IAClC,QAAQ,MAAM,MAAM,CAAC;IACrB,IAAI,MAAM,MAAM,GAAG,GAAG;QAClB,MAAM,OAAO,MAAM,GAAG;QACtB,OAAO,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,MAAM,KAAK,EAAE,KAAK,CAAC,CAAC;IACzD,OACK,IAAI,MAAM,MAAM,KAAK,GAAG;QACzB,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,OACK;QACD,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACjC;IACA,IAAI,UAAU,MAAM;QAChB,OAAO,CAAC,UAAU,EAAE,QAAQ;IAChC,OACK,IAAI,OAAO,WAAW,cAAc,OAAO,IAAI,EAAE;QAClD,OAAO,CAAC,mBAAmB,EAAE,OAAO,IAAI,EAAE;IAC9C,OACK,IAAI,OAAO,WAAW,YAAY,UAAU,MAAM;QACnD,IAAI,OAAO,WAAW,EAAE,MAAM;YAC1B,OAAO,CAAC,yBAAyB,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE;QAChE;IACJ;IACA,OAAO;AACX;uCACe,CAAC,QAAQ,GAAG;IACvB,OAAO,QAAQ,gBAAgB,WAAW;AAC9C;AACO,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK;IACzC,OAAO,QAAQ,CAAC,YAAY,EAAE,IAAI,mBAAmB,CAAC,EAAE,WAAW;AACvE", "ignoreList": [0]}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/get_sign_verify_key.js"], "sourcesContent": ["import { checkSig<PERSON>rypt<PERSON><PERSON><PERSON> } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nexport default async (alg, key, usage) => {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n        }\n        return crypto.subtle.importKey('raw', key, { hash: `SHA-${alg.slice(-3)}`, name: 'HM<PERSON>' }, false, [usage]);\n    }\n    checkSigCryptoKey(key, alg, usage);\n    return key;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCACe,OAAO,KAAK,KAAK;IAC5B,IAAI,eAAe,YAAY;QAC3B,IAAI,CAAC,IAAI,UAAU,CAAC,OAAO;YACvB,MAAM,IAAI,UAAU,CAAA,GAAA,0KAAA,CAAA,UAAe,AAAD,EAAE,KAAK,aAAa,aAAa;QACvE;QACA,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,KAAK;YAAE,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI;YAAE,MAAM;QAAO,GAAG,OAAO;YAAC;SAAM;IAC7G;IACA,CAAA,GAAA,mKAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAAK;IAC5B,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/verify.js"], "sourcesContent": ["import subtleAlgorithm from './subtle_dsa.js';\nimport check<PERSON><PERSON><PERSON>ength from './check_key_length.js';\nimport getVerifyKey from './get_sign_verify_key.js';\nexport default async (alg, key, signature, data) => {\n    const cryptoKey = await getVerifyKey(alg, key, 'verify');\n    checkKeyLength(alg, cryptoKey);\n    const algorithm = subtleAlgorithm(alg, cryptoKey.algorithm);\n    try {\n        return await crypto.subtle.verify(algorithm, cryptoKey, signature, data);\n    }\n    catch {\n        return false;\n    }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCACe,OAAO,KAAK,KAAK,WAAW;IACvC,MAAM,YAAY,MAAM,CAAA,GAAA,4KAAA,CAAA,UAAY,AAAD,EAAE,KAAK,KAAK;IAC/C,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,KAAK;IACpB,MAAM,YAAY,CAAA,GAAA,mKAAA,CAAA,UAAe,AAAD,EAAE,KAAK,UAAU,SAAS;IAC1D,IAAI;QACA,OAAO,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW,WAAW,WAAW;IACvE,EACA,OAAM;QACF,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/is_disjoint.js"], "sourcesContent": ["export default (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n"], "names": [], "mappings": ";;;uCAAe,CAAC,GAAG;IACf,MAAM,UAAU,QAAQ,MAAM,CAAC;IAC/B,IAAI,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;QAC9C,OAAO;IACX;IACA,IAAI;IACJ,KAAK,MAAM,UAAU,QAAS;QAC1B,MAAM,aAAa,OAAO,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,GAAG;YACxB,MAAM,IAAI,IAAI;YACd;QACJ;QACA,KAAK,MAAM,aAAa,WAAY;YAChC,IAAI,IAAI,GAAG,CAAC,YAAY;gBACpB,OAAO;YACX;YACA,IAAI,GAAG,CAAC;QACZ;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/is_key_like.js"], "sourcesContent": ["export function assertCrypto<PERSON><PERSON>(key) {\n    if (!isCrypto<PERSON>ey(key)) {\n        throw new Error('CryptoKey instance expected');\n    }\n}\nexport function isCrypto<PERSON>ey(key) {\n    return key?.[Symbol.toStringTag] === 'CryptoKey';\n}\nexport function isKeyObject(key) {\n    return key?.[Symbol.toStringTag] === 'KeyObject';\n}\nexport default (key) => {\n    return isCryptoKey(key) || isKeyObject(key);\n};\n"], "names": [], "mappings": ";;;;;;AAAO,SAAS,gBAAgB,GAAG;IAC/B,IAAI,CAAC,YAAY,MAAM;QACnB,MAAM,IAAI,MAAM;IACpB;AACJ;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC,KAAK;AACzC;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC,KAAK;AACzC;uCACe,CAAC;IACZ,OAAO,YAAY,QAAQ,YAAY;AAC3C", "ignoreList": [0]}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/is_jwk.js"], "sourcesContent": ["import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return key.kty === 'oct' && typeof key.k === 'string';\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,SAAS,MAAM,GAAG;IACrB,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,OAAO,IAAI,GAAG,KAAK;AAC/C;AACO,SAAS,aAAa,GAAG;IAC5B,OAAO,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK;AACjD;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK;AACjD;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK;AACjD", "ignoreList": [0]}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/check_key_type.js"], "sourcesContent": ["import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKey<PERSON>ike from './is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined) {\n        let expected;\n        switch (usage) {\n            case 'sign':\n            case 'verify':\n                expected = 'sig';\n                break;\n            case 'encrypt':\n            case 'decrypt':\n                expected = 'enc';\n                break;\n        }\n        if (key.use !== expected) {\n            throw new TypeError(`Invalid key for this operation, its \"use\" must be \"${expected}\" when present`);\n        }\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, its \"alg\" must be \"${alg}\" when present`);\n    }\n    if (Array.isArray(key.key_ops)) {\n        let expectedKeyOp;\n        switch (true) {\n            case usage === 'sign' || usage === 'verify':\n            case alg === 'dir':\n            case alg.includes('CBC-HS'):\n                expectedKeyOp = usage;\n                break;\n            case alg.startsWith('PBES2'):\n                expectedKeyOp = 'deriveBits';\n                break;\n            case /^A\\d{3}(?:GCM)?(?:KW)?$/.test(alg):\n                if (!alg.includes('GCM') && alg.endsWith('KW')) {\n                    expectedKeyOp = usage === 'encrypt' ? 'wrapKey' : 'unwrapKey';\n                }\n                else {\n                    expectedKeyOp = usage;\n                }\n                break;\n            case usage === 'encrypt' && alg.startsWith('RSA'):\n                expectedKeyOp = 'wrapKey';\n                break;\n            case usage === 'decrypt':\n                expectedKeyOp = alg.startsWith('RSA') ? 'unwrapKey' : 'deriveBits';\n                break;\n        }\n        if (expectedKeyOp && key.key_ops?.includes?.(expectedKeyOp) === false) {\n            throw new TypeError(`Invalid key for this operation, its \"key_ops\" must include \"${expectedKeyOp}\" when present`);\n        }\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key', 'Uint8Array'));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage) => {\n    if (jwk.isJWK(key)) {\n        switch (usage) {\n            case 'decrypt':\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'encrypt':\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (key.type === 'public') {\n        switch (usage) {\n            case 'sign':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n            case 'decrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n            default:\n                break;\n        }\n    }\n    if (key.type === 'private') {\n        switch (usage) {\n            case 'verify':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n            case 'encrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n            default:\n                break;\n        }\n    }\n};\nexport default (alg, key, usage) => {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(alg) ||\n        /^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage);\n    }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,MAAM,MAAM,CAAC,MAAQ,KAAK,CAAC,OAAO,WAAW,CAAC;AAC9C,MAAM,eAAe,CAAC,KAAK,KAAK;IAC5B,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,IAAI;QACJ,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,WAAW;gBACX;YACJ,KAAK;YACL,KAAK;gBACD,WAAW;gBACX;QACR;QACA,IAAI,IAAI,GAAG,KAAK,UAAU;YACtB,MAAM,IAAI,UAAU,CAAC,mDAAmD,EAAE,SAAS,cAAc,CAAC;QACtG;IACJ;IACA,IAAI,IAAI,GAAG,KAAK,aAAa,IAAI,GAAG,KAAK,KAAK;QAC1C,MAAM,IAAI,UAAU,CAAC,mDAAmD,EAAE,IAAI,cAAc,CAAC;IACjG;IACA,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO,GAAG;QAC5B,IAAI;QACJ,OAAQ;YACJ,KAAK,UAAU,UAAU,UAAU;YACnC,KAAK,QAAQ;YACb,KAAK,IAAI,QAAQ,CAAC;gBACd,gBAAgB;gBAChB;YACJ,KAAK,IAAI,UAAU,CAAC;gBAChB,gBAAgB;gBAChB;YACJ,KAAK,0BAA0B,IAAI,CAAC;gBAChC,IAAI,CAAC,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,OAAO;oBAC5C,gBAAgB,UAAU,YAAY,YAAY;gBACtD,OACK;oBACD,gBAAgB;gBACpB;gBACA;YACJ,KAAK,UAAU,aAAa,IAAI,UAAU,CAAC;gBACvC,gBAAgB;gBAChB;YACJ,KAAK,UAAU;gBACX,gBAAgB,IAAI,UAAU,CAAC,SAAS,cAAc;gBACtD;QACR;QACA,IAAI,iBAAiB,IAAI,OAAO,EAAE,WAAW,mBAAmB,OAAO;YACnE,MAAM,IAAI,UAAU,CAAC,4DAA4D,EAAE,cAAc,cAAc,CAAC;QACpH;IACJ;IACA,OAAO;AACX;AACA,MAAM,qBAAqB,CAAC,KAAK,KAAK;IAClC,IAAI,eAAe,YACf;IACJ,IAAI,CAAA,GAAA,+JAAA,CAAA,QAAS,AAAD,EAAE,MAAM;QAChB,IAAI,CAAA,GAAA,+JAAA,CAAA,cAAe,AAAD,EAAE,QAAQ,aAAa,KAAK,KAAK,QAC/C;QACJ,MAAM,IAAI,UAAU,CAAC,uHAAuH,CAAC;IACjJ;IACA,IAAI,CAAC,CAAA,GAAA,oKAAA,CAAA,UAAS,AAAD,EAAE,MAAM;QACjB,MAAM,IAAI,UAAU,CAAA,GAAA,0KAAA,CAAA,UAAe,AAAD,EAAE,KAAK,KAAK,aAAa,aAAa,gBAAgB;IAC5F;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,4DAA4D,CAAC;IACjG;AACJ;AACA,MAAM,sBAAsB,CAAC,KAAK,KAAK;IACnC,IAAI,CAAA,GAAA,+JAAA,CAAA,QAAS,AAAD,EAAE,MAAM;QAChB,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,CAAA,GAAA,+JAAA,CAAA,eAAgB,AAAD,EAAE,QAAQ,aAAa,KAAK,KAAK,QAChD;gBACJ,MAAM,IAAI,UAAU,CAAC,gDAAgD,CAAC;YAC1E,KAAK;YACL,KAAK;gBACD,IAAI,CAAA,GAAA,+JAAA,CAAA,cAAe,AAAD,EAAE,QAAQ,aAAa,KAAK,KAAK,QAC/C;gBACJ,MAAM,IAAI,UAAU,CAAC,+CAA+C,CAAC;QAC7E;IACJ;IACA,IAAI,CAAC,CAAA,GAAA,oKAAA,CAAA,UAAS,AAAD,EAAE,MAAM;QACjB,MAAM,IAAI,UAAU,CAAA,GAAA,0KAAA,CAAA,UAAe,AAAD,EAAE,KAAK,KAAK,aAAa,aAAa;IAC5E;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,iEAAiE,CAAC;IACtG;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,OAAQ;YACJ,KAAK;gBACD,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,qEAAqE,CAAC;YAC1G,KAAK;gBACD,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,wEAAwE,CAAC;YAC7G;gBACI;QACR;IACJ;IACA,IAAI,IAAI,IAAI,KAAK,WAAW;QACxB,OAAQ;YACJ,KAAK;gBACD,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,sEAAsE,CAAC;YAC3G,KAAK;gBACD,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,uEAAuE,CAAC;YAC5G;gBACI;QACR;IACJ;AACJ;uCACe,CAAC,KAAK,KAAK;IACtB,MAAM,YAAY,IAAI,UAAU,CAAC,SAC7B,QAAQ,SACR,IAAI,UAAU,CAAC,YACf,oCAAoC,IAAI,CAAC,QACzC,0CAA0C,IAAI,CAAC;IACnD,IAAI,WAAW;QACX,mBAAmB,KAAK,KAAK;IACjC,OACK;QACD,oBAAoB,KAAK,KAAK;IAClC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/validate_crit.js"], "sourcesContent": ["import { JOSENotSupported, JWEInvalid, JWSInvalid } from '../util/errors.js';\nexport default (Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) => {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n};\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAC,KAAK,mBAAmB,kBAAkB,iBAAiB;IACvE,IAAI,WAAW,IAAI,KAAK,aAAa,iBAAiB,SAAS,WAAW;QACtE,MAAM,IAAI,IAAI;IAClB;IACA,IAAI,CAAC,mBAAmB,gBAAgB,IAAI,KAAK,WAAW;QACxD,OAAO,IAAI;IACf;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,gBAAgB,IAAI,KACnC,gBAAgB,IAAI,CAAC,MAAM,KAAK,KAChC,gBAAgB,IAAI,CAAC,IAAI,CAAC,CAAC,QAAU,OAAO,UAAU,YAAY,MAAM,MAAM,KAAK,IAAI;QACvF,MAAM,IAAI,IAAI;IAClB;IACA,IAAI;IACJ,IAAI,qBAAqB,WAAW;QAChC,aAAa,IAAI,IAAI;eAAI,OAAO,OAAO,CAAC;eAAsB,kBAAkB,OAAO;SAAG;IAC9F,OACK;QACD,aAAa;IACjB;IACA,KAAK,MAAM,aAAa,gBAAgB,IAAI,CAAE;QAC1C,IAAI,CAAC,WAAW,GAAG,CAAC,YAAY;YAC5B,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC,CAAC,4BAA4B,EAAE,UAAU,mBAAmB,CAAC;QAC5F;QACA,IAAI,UAAU,CAAC,UAAU,KAAK,WAAW;YACrC,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,UAAU,YAAY,CAAC;QACxE;QACA,IAAI,WAAW,GAAG,CAAC,cAAc,eAAe,CAAC,UAAU,KAAK,WAAW;YACvE,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,UAAU,6BAA6B,CAAC;QACzF;IACJ;IACA,OAAO,IAAI,IAAI,gBAAgB,IAAI;AACvC", "ignoreList": [0]}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/validate_algorithms.js"], "sourcesContent": ["export default (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\n"], "names": [], "mappings": ";;;uCAAe,CAAC,QAAQ;IACpB,IAAI,eAAe,aACf,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,WAAW,IAAI,CAAC,CAAC,IAAM,OAAO,MAAM,SAAS,GAAG;QAC/E,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,OAAO,oCAAoC,CAAC;IACxE;IACA,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,OAAO,IAAI,IAAI;AACnB", "ignoreList": [0]}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/jwk_to_key.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nfunction subtleMapping(jwk) {\n    let algorithm;\n    let keyUsages;\n    switch (jwk.kty) {\n        case 'RSA': {\n            switch (jwk.alg) {\n                case 'PS256':\n                case 'PS384':\n                case 'PS512':\n                    algorithm = { name: 'RSA-PSS', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RS256':\n                case 'RS384':\n                case 'RS512':\n                    algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RSA-OAEP':\n                case 'RSA-OAEP-256':\n                case 'RSA-OAEP-384':\n                case 'RSA-OAEP-512':\n                    algorithm = {\n                        name: 'RSA-OAEP',\n                        hash: `SHA-${parseInt(jwk.alg.slice(-3), 10) || 1}`,\n                    };\n                    keyUsages = jwk.d ? ['decrypt', 'unwrapKey'] : ['encrypt', 'wrapKey'];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'EC': {\n            switch (jwk.alg) {\n                case 'ES256':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES384':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES512':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: 'ECDH', namedCurve: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'OKP': {\n            switch (jwk.alg) {\n                case 'Ed25519':\n                case 'EdDSA':\n                    algorithm = { name: 'Ed25519' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"kty\" (Key Type) Parameter value');\n    }\n    return { algorithm, keyUsages };\n}\nexport default async (jwk) => {\n    if (!jwk.alg) {\n        throw new TypeError('\"alg\" argument is required when \"jwk.alg\" is not present');\n    }\n    const { algorithm, keyUsages } = subtleMapping(jwk);\n    const keyData = { ...jwk };\n    delete keyData.alg;\n    delete keyData.use;\n    return crypto.subtle.importKey('jwk', keyData, algorithm, jwk.ext ?? (jwk.d ? false : true), jwk.key_ops ?? keyUsages);\n};\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,cAAc,GAAG;IACtB,IAAI;IACJ,IAAI;IACJ,OAAQ,IAAI,GAAG;QACX,KAAK;YAAO;gBACR,OAAQ,IAAI,GAAG;oBACX,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAW,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI;wBAAC;wBAChE,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAqB,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI;wBAAC;wBAC1E,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BACR,MAAM;4BACN,MAAM,CAAC,IAAI,EAAE,SAAS,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,GAAG;wBACvD;wBACA,YAAY,IAAI,CAAC,GAAG;4BAAC;4BAAW;yBAAY,GAAG;4BAAC;4BAAW;yBAAU;wBACrE;oBACJ;wBACI,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC;gBACnC;gBACA;YACJ;QACA,KAAK;YAAM;gBACP,OAAQ,IAAI,GAAG;oBACX,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAS,YAAY;wBAAQ;wBACjD,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAS,YAAY;wBAAQ;wBACjD,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAS,YAAY;wBAAQ;wBACjD,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAQ,YAAY,IAAI,GAAG;wBAAC;wBAChD,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAa,GAAG,EAAE;wBACvC;oBACJ;wBACI,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC;gBACnC;gBACA;YACJ;QACA,KAAK;YAAO;gBACR,OAAQ,IAAI,GAAG;oBACX,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM;wBAAU;wBAC9B,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM,IAAI,GAAG;wBAAC;wBAC5B,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAa,GAAG,EAAE;wBACvC;oBACJ;wBACI,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC;gBACnC;gBACA;YACJ;QACA;YACI,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC;IACnC;IACA,OAAO;QAAE;QAAW;IAAU;AAClC;uCACe,OAAO;IAClB,IAAI,CAAC,IAAI,GAAG,EAAE;QACV,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,cAAc;IAC/C,MAAM,UAAU;QAAE,GAAG,GAAG;IAAC;IACzB,OAAO,QAAQ,GAAG;IAClB,OAAO,QAAQ,GAAG;IAClB,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,SAAS,WAAW,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,GAAG,IAAI,OAAO,IAAI;AAChH", "ignoreList": [0]}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/normalize_key.js"], "sourcesContent": ["import { isJWK } from './is_jwk.js';\nimport { decode } from '../util/base64url.js';\nimport importJWK from './jwk_to_key.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nlet cache;\nconst handleJWK = async (key, jwk, alg, freeze = false) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(key);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const cryptoKey = await importJWK({ ...jwk, alg });\n    if (freeze)\n        Object.freeze(key);\n    if (!cached) {\n        cache.set(key, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nconst handleKeyObject = (keyObject, alg) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(keyObject);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const isPublic = keyObject.type === 'public';\n    const extractable = isPublic ? true : false;\n    let cryptoKey;\n    if (keyObject.asymmetricKeyType === 'x25519') {\n        switch (alg) {\n            case 'ECDH-ES':\n            case 'ECDH-ES+A128KW':\n            case 'ECDH-ES+A192KW':\n            case 'ECDH-ES+A256KW':\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, isPublic ? [] : ['deriveBits']);\n    }\n    if (keyObject.asymmetricKeyType === 'ed25519') {\n        if (alg !== 'EdDSA' && alg !== 'Ed25519') {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, [\n            isPublic ? 'verify' : 'sign',\n        ]);\n    }\n    if (keyObject.asymmetricKeyType === 'rsa') {\n        let hash;\n        switch (alg) {\n            case 'RSA-OAEP':\n                hash = 'SHA-1';\n                break;\n            case 'RS256':\n            case 'PS256':\n            case 'RSA-OAEP-256':\n                hash = 'SHA-256';\n                break;\n            case 'RS384':\n            case 'PS384':\n            case 'RSA-OAEP-384':\n                hash = 'SHA-384';\n                break;\n            case 'RS512':\n            case 'PS512':\n            case 'RSA-OAEP-512':\n                hash = 'SHA-512';\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg.startsWith('RSA-OAEP')) {\n            return keyObject.toCryptoKey({\n                name: 'RSA-OAEP',\n                hash,\n            }, extractable, isPublic ? ['encrypt'] : ['decrypt']);\n        }\n        cryptoKey = keyObject.toCryptoKey({\n            name: alg.startsWith('PS') ? 'RSA-PSS' : 'RSASSA-PKCS1-v1_5',\n            hash,\n        }, extractable, [isPublic ? 'verify' : 'sign']);\n    }\n    if (keyObject.asymmetricKeyType === 'ec') {\n        const nist = new Map([\n            ['prime256v1', 'P-256'],\n            ['secp384r1', 'P-384'],\n            ['secp521r1', 'P-521'],\n        ]);\n        const namedCurve = nist.get(keyObject.asymmetricKeyDetails?.namedCurve);\n        if (!namedCurve) {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg === 'ES256' && namedCurve === 'P-256') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES384' && namedCurve === 'P-384') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES512' && namedCurve === 'P-521') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg.startsWith('ECDH-ES')) {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDH',\n                namedCurve,\n            }, extractable, isPublic ? [] : ['deriveBits']);\n        }\n    }\n    if (!cryptoKey) {\n        throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n    }\n    if (!cached) {\n        cache.set(keyObject, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nexport default async (key, alg) => {\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        return key;\n    }\n    if (isKeyObject(key)) {\n        if (key.type === 'secret') {\n            return key.export();\n        }\n        if ('toCryptoKey' in key && typeof key.toCryptoKey === 'function') {\n            try {\n                return handleKeyObject(key, alg);\n            }\n            catch (err) {\n                if (err instanceof TypeError) {\n                    throw err;\n                }\n            }\n        }\n        let jwk = key.export({ format: 'jwk' });\n        return handleJWK(key, jwk, alg);\n    }\n    if (isJWK(key)) {\n        if (key.k) {\n            return decode(key.k);\n        }\n        return handleJWK(key, key, alg, true);\n    }\n    throw new Error('unreachable');\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI;AACJ,MAAM,YAAY,OAAO,KAAK,KAAK,KAAK,SAAS,KAAK;IAClD,UAAU,IAAI;IACd,IAAI,SAAS,MAAM,GAAG,CAAC;IACvB,IAAI,QAAQ,CAAC,IAAI,EAAE;QACf,OAAO,MAAM,CAAC,IAAI;IACtB;IACA,MAAM,YAAY,MAAM,CAAA,GAAA,mKAAA,CAAA,UAAS,AAAD,EAAE;QAAE,GAAG,GAAG;QAAE;IAAI;IAChD,IAAI,QACA,OAAO,MAAM,CAAC;IAClB,IAAI,CAAC,QAAQ;QACT,MAAM,GAAG,CAAC,KAAK;YAAE,CAAC,IAAI,EAAE;QAAU;IACtC,OACK;QACD,MAAM,CAAC,IAAI,GAAG;IAClB;IACA,OAAO;AACX;AACA,MAAM,kBAAkB,CAAC,WAAW;IAChC,UAAU,IAAI;IACd,IAAI,SAAS,MAAM,GAAG,CAAC;IACvB,IAAI,QAAQ,CAAC,IAAI,EAAE;QACf,OAAO,MAAM,CAAC,IAAI;IACtB;IACA,MAAM,WAAW,UAAU,IAAI,KAAK;IACpC,MAAM,cAAc,WAAW,OAAO;IACtC,IAAI;IACJ,IAAI,UAAU,iBAAiB,KAAK,UAAU;QAC1C,OAAQ;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ;gBACI,MAAM,IAAI,UAAU;QAC5B;QACA,YAAY,UAAU,WAAW,CAAC,UAAU,iBAAiB,EAAE,aAAa,WAAW,EAAE,GAAG;YAAC;SAAa;IAC9G;IACA,IAAI,UAAU,iBAAiB,KAAK,WAAW;QAC3C,IAAI,QAAQ,WAAW,QAAQ,WAAW;YACtC,MAAM,IAAI,UAAU;QACxB;QACA,YAAY,UAAU,WAAW,CAAC,UAAU,iBAAiB,EAAE,aAAa;YACxE,WAAW,WAAW;SACzB;IACL;IACA,IAAI,UAAU,iBAAiB,KAAK,OAAO;QACvC,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;gBACP;YACJ;gBACI,MAAM,IAAI,UAAU;QAC5B;QACA,IAAI,IAAI,UAAU,CAAC,aAAa;YAC5B,OAAO,UAAU,WAAW,CAAC;gBACzB,MAAM;gBACN;YACJ,GAAG,aAAa,WAAW;gBAAC;aAAU,GAAG;gBAAC;aAAU;QACxD;QACA,YAAY,UAAU,WAAW,CAAC;YAC9B,MAAM,IAAI,UAAU,CAAC,QAAQ,YAAY;YACzC;QACJ,GAAG,aAAa;YAAC,WAAW,WAAW;SAAO;IAClD;IACA,IAAI,UAAU,iBAAiB,KAAK,MAAM;QACtC,MAAM,OAAO,IAAI,IAAI;YACjB;gBAAC;gBAAc;aAAQ;YACvB;gBAAC;gBAAa;aAAQ;YACtB;gBAAC;gBAAa;aAAQ;SACzB;QACD,MAAM,aAAa,KAAK,GAAG,CAAC,UAAU,oBAAoB,EAAE;QAC5D,IAAI,CAAC,YAAY;YACb,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,QAAQ,WAAW,eAAe,SAAS;YAC3C,YAAY,UAAU,WAAW,CAAC;gBAC9B,MAAM;gBACN;YACJ,GAAG,aAAa;gBAAC,WAAW,WAAW;aAAO;QAClD;QACA,IAAI,QAAQ,WAAW,eAAe,SAAS;YAC3C,YAAY,UAAU,WAAW,CAAC;gBAC9B,MAAM;gBACN;YACJ,GAAG,aAAa;gBAAC,WAAW,WAAW;aAAO;QAClD;QACA,IAAI,QAAQ,WAAW,eAAe,SAAS;YAC3C,YAAY,UAAU,WAAW,CAAC;gBAC9B,MAAM;gBACN;YACJ,GAAG,aAAa;gBAAC,WAAW,WAAW;aAAO;QAClD;QACA,IAAI,IAAI,UAAU,CAAC,YAAY;YAC3B,YAAY,UAAU,WAAW,CAAC;gBAC9B,MAAM;gBACN;YACJ,GAAG,aAAa,WAAW,EAAE,GAAG;gBAAC;aAAa;QAClD;IACJ;IACA,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,UAAU;IACxB;IACA,IAAI,CAAC,QAAQ;QACT,MAAM,GAAG,CAAC,WAAW;YAAE,CAAC,IAAI,EAAE;QAAU;IAC5C,OACK;QACD,MAAM,CAAC,IAAI,GAAG;IAClB;IACA,OAAO;AACX;uCACe,OAAO,KAAK;IACvB,IAAI,eAAe,YAAY;QAC3B,OAAO;IACX;IACA,IAAI,CAAA,GAAA,oKAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,OAAO;IACX;IACA,IAAI,CAAA,GAAA,oKAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,IAAI,IAAI,IAAI,KAAK,UAAU;YACvB,OAAO,IAAI,MAAM;QACrB;QACA,IAAI,iBAAiB,OAAO,OAAO,IAAI,WAAW,KAAK,YAAY;YAC/D,IAAI;gBACA,OAAO,gBAAgB,KAAK;YAChC,EACA,OAAO,KAAK;gBACR,IAAI,eAAe,WAAW;oBAC1B,MAAM;gBACV;YACJ;QACJ;QACA,IAAI,MAAM,IAAI,MAAM,CAAC;YAAE,QAAQ;QAAM;QACrC,OAAO,UAAU,KAAK,KAAK;IAC/B;IACA,IAAI,CAAA,GAAA,+JAAA,CAAA,QAAK,AAAD,EAAE,MAAM;QACZ,IAAI,IAAI,CAAC,EAAE;YACP,OAAO,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC;QACvB;QACA,OAAO,UAAU,KAAK,KAAK,KAAK;IACpC;IACA,MAAM,IAAI,MAAM;AACpB", "ignoreList": [0]}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/jws/flattened/verify.js"], "sourcesContent": ["import { decode as b64u } from '../../util/base64url.js';\nimport verify from '../../lib/verify.js';\nimport { JOSEAlgNotAllowed, JWSInvalid, JWSSignatureVerificationFailed } from '../../util/errors.js';\nimport { concat, encoder, decoder } from '../../lib/buffer_utils.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport async function flattenedVerify(jws, key, options) {\n    if (!isObject(jws)) {\n        throw new JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !isObject(jws.header)) {\n        throw new JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = b64u(jws.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jws.header)) {\n        throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && validateAlgorithms('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n    }\n    checkKeyType(alg, key, 'verify');\n    const data = concat(encoder.encode(jws.protected ?? ''), encoder.encode('.'), typeof jws.payload === 'string' ? encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = b64u(jws.signature);\n    }\n    catch {\n        throw new JWSInvalid('Failed to base64url decode the signature');\n    }\n    const k = await normalizeKey(key, alg);\n    const verified = await verify(alg, k, signature, data);\n    if (!verified) {\n        throw new JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = b64u(jws.payload);\n        }\n        catch {\n            throw new JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key: k };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,eAAe,gBAAgB,GAAG,EAAE,GAAG,EAAE,OAAO;IACnD,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,MAAM;QAChB,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,IAAI,MAAM,KAAK,WAAW;QACzD,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,UAAU;QAClE,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,OAAO,KAAK,WAAW;QAC3B,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,OAAO,IAAI,SAAS,KAAK,UAAU;QACnC,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,IAAI,MAAM,KAAK,aAAa,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,IAAI,MAAM,GAAG;QACnD,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,aAAa,CAAC;IAClB,IAAI,IAAI,SAAS,EAAE;QACf,IAAI;YACA,MAAM,kBAAkB,CAAA,GAAA,mKAAA,CAAA,SAAI,AAAD,EAAE,IAAI,SAAS;YAC1C,aAAa,KAAK,KAAK,CAAC,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QAC3C,EACA,OAAM;YACF,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,IAAI,CAAC,CAAA,GAAA,oKAAA,CAAA,UAAU,AAAD,EAAE,YAAY,IAAI,MAAM,GAAG;QACrC,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,aAAa;QACf,GAAG,UAAU;QACb,GAAG,IAAI,MAAM;IACjB;IACA,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,UAAY,AAAD,EAAE,gKAAA,CAAA,aAAU,EAAE,IAAI,IAAI;QAAC;YAAC;YAAO;SAAK;KAAC,GAAG,SAAS,MAAM,YAAY;IACjG,IAAI,MAAM;IACV,IAAI,WAAW,GAAG,CAAC,QAAQ;QACvB,MAAM,WAAW,GAAG;QACpB,IAAI,OAAO,QAAQ,WAAW;YAC1B,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;QACzB;IACJ;IACA,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,aAAa,WAAW,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,cAAc,QAAQ,UAAU;IACjF,IAAI,cAAc,CAAC,WAAW,GAAG,CAAC,MAAM;QACpC,MAAM,IAAI,gKAAA,CAAA,oBAAiB,CAAC;IAChC;IACA,IAAI,KAAK;QACL,IAAI,OAAO,IAAI,OAAO,KAAK,UAAU;YACjC,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;QACzB;IACJ,OACK,IAAI,OAAO,IAAI,OAAO,KAAK,YAAY,CAAC,CAAC,IAAI,OAAO,YAAY,UAAU,GAAG;QAC9E,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,IAAI,cAAc;IAClB,IAAI,OAAO,QAAQ,YAAY;QAC3B,MAAM,MAAM,IAAI,YAAY;QAC5B,cAAc;IAClB;IACA,CAAA,GAAA,uKAAA,CAAA,UAAY,AAAD,EAAE,KAAK,KAAK;IACvB,MAAM,OAAO,CAAA,GAAA,qKAAA,CAAA,SAAM,AAAD,EAAE,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,SAAS,IAAI,KAAK,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,MAAM,OAAO,IAAI,OAAO,KAAK,WAAW,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,OAAO;IACzJ,IAAI;IACJ,IAAI;QACA,YAAY,CAAA,GAAA,mKAAA,CAAA,SAAI,AAAD,EAAE,IAAI,SAAS;IAClC,EACA,OAAM;QACF,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,IAAI,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAY,AAAD,EAAE,KAAK;IAClC,MAAM,WAAW,MAAM,CAAA,GAAA,+JAAA,CAAA,UAAM,AAAD,EAAE,KAAK,GAAG,WAAW;IACjD,IAAI,CAAC,UAAU;QACX,MAAM,IAAI,gKAAA,CAAA,iCAA8B;IAC5C;IACA,IAAI;IACJ,IAAI,KAAK;QACL,IAAI;YACA,UAAU,CAAA,GAAA,mKAAA,CAAA,SAAI,AAAD,EAAE,IAAI,OAAO;QAC9B,EACA,OAAM;YACF,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;QACzB;IACJ,OACK,IAAI,OAAO,IAAI,OAAO,KAAK,UAAU;QACtC,UAAU,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,OAAO;IACxC,OACK;QACD,UAAU,IAAI,OAAO;IACzB;IACA,MAAM,SAAS;QAAE;IAAQ;IACzB,IAAI,IAAI,SAAS,KAAK,WAAW;QAC7B,OAAO,eAAe,GAAG;IAC7B;IACA,IAAI,IAAI,MAAM,KAAK,WAAW;QAC1B,OAAO,iBAAiB,GAAG,IAAI,MAAM;IACzC;IACA,IAAI,aAAa;QACb,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK;QAAE;IAC/B;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1461, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/jws/compact/verify.js"], "sourcesContent": ["import { flattenedVerify } from '../flattened/verify.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await flattenedVerify({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,eAAe,cAAc,GAAG,EAAE,GAAG,EAAE,OAAO;IACjD,IAAI,eAAe,YAAY;QAC3B,MAAM,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACzB;IACA,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,EAAE,GAAG,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,CAAC;IAC3E,IAAI,WAAW,GAAG;QACd,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,WAAW,MAAM,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAS,WAAW;QAAiB;IAAU,GAAG,KAAK;IAChG,MAAM,SAAS;QAAE,SAAS,SAAS,OAAO;QAAE,iBAAiB,SAAS,eAAe;IAAC;IACtF,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,SAAS,GAAG;QAAC;IAC1C;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1504, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/epoch.js"], "sourcesContent": ["export default (date) => Math.floor(date.getTime() / 1000);\n"], "names": [], "mappings": ";;;uCAAe,CAAC,OAAS,KAAK,KAAK,CAAC,KAAK,OAAO,KAAK", "ignoreList": [0]}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/secs.js"], "sourcesContent": ["const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS;AACf,MAAM,OAAO,SAAS;AACtB,MAAM,MAAM,OAAO;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,QAAQ;uCACC,CAAC;IACZ,MAAM,UAAU,MAAM,IAAI,CAAC;IAC3B,IAAI,CAAC,WAAY,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,EAAG;QACxC,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,QAAQ,WAAW,OAAO,CAAC,EAAE;IACnC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,WAAW;IACnC,IAAI;IACJ,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC;YACzB;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ;YACI,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;IACR;IACA,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,OAAO;QAC5C,OAAO,CAAC;IACZ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1578, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/jwt_claims_set.js"], "sourcesContent": ["import { JWTClaimValidationFailed, JW<PERSON>xpired, JWTInvalid } from '../util/errors.js';\nimport { decoder } from './buffer_utils.js';\nimport epoch from './epoch.js';\nimport secs from './secs.js';\nimport isObject from './is_object.js';\nimport { encoder } from './buffer_utils.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nconst normalizeTyp = (value) => {\n    if (value.includes('/')) {\n        return value.toLowerCase();\n    }\n    return `application/${value.toLowerCase()}`;\n};\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexport function validateClaimsSet(protectedHeader, encodedPayload, options = {}) {\n    let payload;\n    try {\n        payload = JSON.parse(decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!isObject(payload)) {\n        throw new JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = secs(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = epoch(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : secs(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n}\nexport class JWTClaimsBuilder {\n    #payload;\n    constructor(payload) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this.#payload = structuredClone(payload);\n    }\n    data() {\n        return encoder.encode(JSON.stringify(this.#payload));\n    }\n    get iss() {\n        return this.#payload.iss;\n    }\n    set iss(value) {\n        this.#payload.iss = value;\n    }\n    get sub() {\n        return this.#payload.sub;\n    }\n    set sub(value) {\n        this.#payload.sub = value;\n    }\n    get aud() {\n        return this.#payload.aud;\n    }\n    set aud(value) {\n        this.#payload.aud = value;\n    }\n    set jti(value) {\n        this.#payload.jti = value;\n    }\n    set nbf(value) {\n        if (typeof value === 'number') {\n            this.#payload.nbf = validateInput('setNotBefore', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.nbf = validateInput('setNotBefore', epoch(value));\n        }\n        else {\n            this.#payload.nbf = epoch(new Date()) + secs(value);\n        }\n    }\n    set exp(value) {\n        if (typeof value === 'number') {\n            this.#payload.exp = validateInput('setExpirationTime', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.exp = validateInput('setExpirationTime', epoch(value));\n        }\n        else {\n            this.#payload.exp = epoch(new Date()) + secs(value);\n        }\n    }\n    set iat(value) {\n        if (typeof value === 'undefined') {\n            this.#payload.iat = epoch(new Date());\n        }\n        else if (value instanceof Date) {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(value));\n        }\n        else if (typeof value === 'string') {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(new Date()) + secs(value));\n        }\n        else {\n            this.#payload.iat = validateInput('setIssuedAt', value);\n        }\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,SAAS,cAAc,KAAK,EAAE,KAAK;IAC/B,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;QACzB,MAAM,IAAI,UAAU,CAAC,QAAQ,EAAE,MAAM,MAAM,CAAC;IAChD;IACA,OAAO;AACX;AACA,MAAM,eAAe,CAAC;IAClB,IAAI,MAAM,QAAQ,CAAC,MAAM;QACrB,OAAO,MAAM,WAAW;IAC5B;IACA,OAAO,CAAC,YAAY,EAAE,MAAM,WAAW,IAAI;AAC/C;AACA,MAAM,wBAAwB,CAAC,YAAY;IACvC,IAAI,OAAO,eAAe,UAAU;QAChC,OAAO,UAAU,QAAQ,CAAC;IAC9B;IACA,IAAI,MAAM,OAAO,CAAC,aAAa;QAC3B,OAAO,UAAU,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;IACzD;IACA,OAAO;AACX;AACO,SAAS,kBAAkB,eAAe,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;IAC3E,IAAI;IACJ,IAAI;QACA,UAAU,KAAK,KAAK,CAAC,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACxC,EACA,OAAM,CACN;IACA,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,UAAU;QACpB,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,IAAI,OACA,CAAC,OAAO,gBAAgB,GAAG,KAAK,YAC5B,aAAa,gBAAgB,GAAG,MAAM,aAAa,IAAI,GAAG;QAC9D,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,qCAAqC,SAAS,OAAO;IAC5F;IACA,MAAM,EAAE,iBAAiB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IACxE,MAAM,gBAAgB;WAAI;KAAe;IACzC,IAAI,gBAAgB,WAChB,cAAc,IAAI,CAAC;IACvB,IAAI,aAAa,WACb,cAAc,IAAI,CAAC;IACvB,IAAI,YAAY,WACZ,cAAc,IAAI,CAAC;IACvB,IAAI,WAAW,WACX,cAAc,IAAI,CAAC;IACvB,KAAK,MAAM,SAAS,IAAI,IAAI,cAAc,OAAO,IAAK;QAClD,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG;YACrB,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,CAAC,kBAAkB,EAAE,MAAM,OAAO,CAAC,EAAE,SAAS,OAAO;QAC5F;IACJ;IACA,IAAI,UACA,CAAC,CAAC,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;KAAO,EAAE,QAAQ,CAAC,QAAQ,GAAG,GAAG;QACpE,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,WAAW,QAAQ,GAAG,KAAK,SAAS;QACpC,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,YACA,CAAC,sBAAsB,QAAQ,GAAG,EAAE,OAAO,aAAa,WAAW;QAAC;KAAS,GAAG,WAAW;QAC3F,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI;IACJ,OAAQ,OAAO,QAAQ,cAAc;QACjC,KAAK;YACD,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,cAAc;YACvC;QACJ,KAAK;YACD,YAAY,QAAQ,cAAc;YAClC;QACJ,KAAK;YACD,YAAY;YACZ;QACJ;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,MAAM,MAAM,CAAA,GAAA,8JAAA,CAAA,UAAK,AAAD,EAAE,eAAe,IAAI;IACrC,IAAI,CAAC,QAAQ,GAAG,KAAK,aAAa,WAAW,KAAK,OAAO,QAAQ,GAAG,KAAK,UAAU;QAC/E,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,QAAQ,GAAG,KAAK,WAAW;QAC3B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;YACjC,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;QACvF;QACA,IAAI,QAAQ,GAAG,GAAG,MAAM,WAAW;YAC/B,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,sCAAsC,SAAS,OAAO;QAC7F;IACJ;IACA,IAAI,QAAQ,GAAG,KAAK,WAAW;QAC3B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;YACjC,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,gCAAgC,SAAS,OAAO;QACvF;QACA,IAAI,QAAQ,GAAG,IAAI,MAAM,WAAW;YAChC,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC,sCAAsC,SAAS,OAAO;QAC/E;IACJ;IACA,IAAI,aAAa;QACb,MAAM,MAAM,MAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,OAAO,gBAAgB,WAAW,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAI,AAAD,EAAE;QACjE,IAAI,MAAM,YAAY,KAAK;YACvB,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC,4DAA4D,SAAS,OAAO;QACrG;QACA,IAAI,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,gKAAA,CAAA,2BAAwB,CAAC,iEAAiE,SAAS,OAAO;QACxH;IACJ;IACA,OAAO;AACX;AACO,MAAM;IACT,CAAA,OAAQ,CAAC;IACT,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,UAAU;YACpB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,OAAQ,GAAG,gBAAgB;IACpC;IACA,OAAO;QACH,OAAO,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA,OAAQ;IACtD;IACA,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG;IAC5B;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG;IACxB;IACA,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG;IAC5B;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG;IACxB;IACA,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG;IAC5B;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG;IACxB;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG;IACxB;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,gBAAgB;QACtD,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,gBAAgB,CAAA,GAAA,8JAAA,CAAA,UAAK,AAAD,EAAE;QAC5D,OACK;YACD,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAK,AAAD,EAAE,IAAI,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAI,AAAD,EAAE;QACjD;IACJ;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,qBAAqB;QAC3D,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,qBAAqB,CAAA,GAAA,8JAAA,CAAA,UAAK,AAAD,EAAE;QACjE,OACK;YACD,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAK,AAAD,EAAE,IAAI,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAI,AAAD,EAAE;QACjD;IACJ;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,OAAO,UAAU,aAAa;YAC9B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAK,AAAD,EAAE,IAAI;QAClC,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,eAAe,CAAA,GAAA,8JAAA,CAAA,UAAK,AAAD,EAAE;QAC3D,OACK,IAAI,OAAO,UAAU,UAAU;YAChC,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,eAAe,CAAA,GAAA,8JAAA,CAAA,UAAK,AAAD,EAAE,IAAI,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAI,AAAD,EAAE;QAC9E,OACK;YACD,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,eAAe;QACrD;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1767, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/jwt/verify.js"], "sourcesContent": ["import { compactVerify } from '../jws/compact/verify.js';\nimport { validateClaimsSet } from '../lib/jwt_claims_set.js';\nimport { JWTInvalid } from '../util/errors.js';\nexport async function jwtVerify(jwt, key, options) {\n    const verified = await compactVerify(jwt, key, options);\n    if (verified.protectedHeader.crit?.includes('b64') && verified.protectedHeader.b64 === false) {\n        throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n    }\n    const payload = validateClaimsSet(verified.protectedHeader, verified.payload, options);\n    const result = { payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,eAAe,UAAU,GAAG,EAAE,GAAG,EAAE,OAAO;IAC7C,MAAM,WAAW,MAAM,CAAA,GAAA,0KAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK;IAC/C,IAAI,SAAS,eAAe,CAAC,IAAI,EAAE,SAAS,UAAU,SAAS,eAAe,CAAC,GAAG,KAAK,OAAO;QAC1F,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;IACzB;IACA,MAAM,UAAU,CAAA,GAAA,uKAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,eAAe,EAAE,SAAS,OAAO,EAAE;IAC9E,MAAM,SAAS;QAAE;QAAS,iBAAiB,SAAS,eAAe;IAAC;IACpE,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,SAAS,GAAG;QAAC;IAC1C;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1800, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/asn1.js"], "sourcesContent": ["import invalidKeyInput from './invalid_key_input.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nconst formatPEM = (b64, descriptor) => {\n    const newlined = (b64.match(/.{1,64}/g) || []).join('\\n');\n    return `-----BEGIN ${descriptor}-----\\n${newlined}\\n-----END ${descriptor}-----`;\n};\nconst genericExport = async (keyType, keyFormat, key) => {\n    if (isKeyObject(key)) {\n        if (key.type !== keyType) {\n            throw new TypeError(`key is not a ${keyType} key`);\n        }\n        return key.export({ format: 'pem', type: keyFormat });\n    }\n    if (!isCryptoKey(key)) {\n        throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject'));\n    }\n    if (!key.extractable) {\n        throw new TypeError('CryptoKey is not extractable');\n    }\n    if (key.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return formatPEM(encodeBase64(new Uint8Array(await crypto.subtle.exportKey(keyFormat, key))), `${keyType.toUpperCase()} KEY`);\n};\nexport const toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nexport const toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nconst findOid = (keyData, oid, from = 0) => {\n    if (from === 0) {\n        oid.unshift(oid.length);\n        oid.unshift(0x06);\n    }\n    const i = keyData.indexOf(oid[0], from);\n    if (i === -1)\n        return false;\n    const sub = keyData.subarray(i, i + oid.length);\n    if (sub.length !== oid.length)\n        return false;\n    return sub.every((value, index) => value === oid[index]) || findOid(keyData, oid, i + 1);\n};\nconst getNamedCurve = (keyData) => {\n    switch (true) {\n        case findOid(keyData, [0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07]):\n            return 'P-256';\n        case findOid(keyData, [0x2b, 0x81, 0x04, 0x00, 0x22]):\n            return 'P-384';\n        case findOid(keyData, [0x2b, 0x81, 0x04, 0x00, 0x23]):\n            return 'P-521';\n        default:\n            return undefined;\n    }\n};\nconst genericImport = async (replace, keyFormat, pem, alg, options) => {\n    let algorithm;\n    let keyUsages;\n    const keyData = new Uint8Array(atob(pem.replace(replace, ''))\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n    const isPublic = keyFormat === 'spki';\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            algorithm = { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            algorithm = {\n                name: 'RSA-OAEP',\n                hash: `SHA-${parseInt(alg.slice(-3), 10) || 1}`,\n            };\n            keyUsages = isPublic ? ['encrypt', 'wrapKey'] : ['decrypt', 'unwrapKey'];\n            break;\n        case 'ES256':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ES384':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ES512':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            const namedCurve = getNamedCurve(keyData);\n            algorithm = namedCurve?.startsWith('P-') ? { name: 'ECDH', namedCurve } : { name: 'X25519' };\n            keyUsages = isPublic ? [] : ['deriveBits'];\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA':\n            algorithm = { name: 'Ed25519' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        default:\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (Algorithm) value');\n    }\n    return crypto.subtle.importKey(keyFormat, keyData, algorithm, options?.extractable ?? (isPublic ? true : false), keyUsages);\n};\nexport const fromPKCS8 = (pem, alg, options) => {\n    return genericImport(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, 'pkcs8', pem, alg, options);\n};\nexport const fromSPKI = (pem, alg, options) => {\n    return genericImport(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, 'spki', pem, alg, options);\n};\nfunction getElement(seq) {\n    const result = [];\n    let next = 0;\n    while (next < seq.length) {\n        const nextPart = parseElement(seq.subarray(next));\n        result.push(nextPart);\n        next += nextPart.byteLength;\n    }\n    return result;\n}\nfunction parseElement(bytes) {\n    let position = 0;\n    let tag = bytes[0] & 0x1f;\n    position++;\n    if (tag === 0x1f) {\n        tag = 0;\n        while (bytes[position] >= 0x80) {\n            tag = tag * 128 + bytes[position] - 0x80;\n            position++;\n        }\n        tag = tag * 128 + bytes[position] - 0x80;\n        position++;\n    }\n    let length = 0;\n    if (bytes[position] < 0x80) {\n        length = bytes[position];\n        position++;\n    }\n    else if (length === 0x80) {\n        length = 0;\n        while (bytes[position + length] !== 0 || bytes[position + length + 1] !== 0) {\n            if (length > bytes.byteLength) {\n                throw new TypeError('invalid indefinite form length');\n            }\n            length++;\n        }\n        const byteLength = position + length + 2;\n        return {\n            byteLength,\n            contents: bytes.subarray(position, position + length),\n            raw: bytes.subarray(0, byteLength),\n        };\n    }\n    else {\n        const numberOfDigits = bytes[position] & 0x7f;\n        position++;\n        length = 0;\n        for (let i = 0; i < numberOfDigits; i++) {\n            length = length * 256 + bytes[position];\n            position++;\n        }\n    }\n    const byteLength = position + length;\n    return {\n        byteLength,\n        contents: bytes.subarray(position, byteLength),\n        raw: bytes.subarray(0, byteLength),\n    };\n}\nfunction spkiFromX509(buf) {\n    const tbsCertificate = getElement(getElement(parseElement(buf).contents)[0].contents);\n    return encodeBase64(tbsCertificate[tbsCertificate[0].raw[0] === 0xa0 ? 6 : 5].raw);\n}\nlet createPublicKey;\nfunction getSPKI(x509) {\n    try {\n        createPublicKey ??= globalThis.process?.getBuiltinModule?.('node:crypto')?.createPublicKey;\n    }\n    catch {\n        createPublicKey = 0;\n    }\n    if (createPublicKey) {\n        try {\n            return new createPublicKey(x509).export({ format: 'pem', type: 'spki' });\n        }\n        catch { }\n    }\n    const pem = x509.replace(/(?:-----(?:BEGIN|END) CERTIFICATE-----|\\s)/g, '');\n    const raw = decodeBase64(pem);\n    return formatPEM(spkiFromX509(raw), 'PUBLIC KEY');\n}\nexport const fromX509 = (pem, alg, options) => {\n    let spki;\n    try {\n        spki = getSPKI(pem);\n    }\n    catch (cause) {\n        throw new TypeError('Failed to parse the X.509 certificate', { cause });\n    }\n    return fromSPKI(spki, alg, options);\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AACA,MAAM,YAAY,CAAC,KAAK;IACpB,MAAM,WAAW,CAAC,IAAI,KAAK,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC;IACpD,OAAO,CAAC,WAAW,EAAE,WAAW,OAAO,EAAE,SAAS,WAAW,EAAE,WAAW,KAAK,CAAC;AACpF;AACA,MAAM,gBAAgB,OAAO,SAAS,WAAW;IAC7C,IAAI,CAAA,GAAA,oKAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QAClB,IAAI,IAAI,IAAI,KAAK,SAAS;YACtB,MAAM,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC;QACrD;QACA,OAAO,IAAI,MAAM,CAAC;YAAE,QAAQ;YAAO,MAAM;QAAU;IACvD;IACA,IAAI,CAAC,CAAA,GAAA,oKAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QACnB,MAAM,IAAI,UAAU,CAAA,GAAA,0KAAA,CAAA,UAAe,AAAD,EAAE,KAAK,aAAa;IAC1D;IACA,IAAI,CAAC,IAAI,WAAW,EAAE;QAClB,MAAM,IAAI,UAAU;IACxB;IACA,IAAI,IAAI,IAAI,KAAK,SAAS;QACtB,MAAM,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC;IACrD;IACA,OAAO,UAAU,CAAA,GAAA,+JAAA,CAAA,eAAY,AAAD,EAAE,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,WAAW,QAAQ,GAAG,QAAQ,WAAW,GAAG,IAAI,CAAC;AAChI;AACO,MAAM,SAAS,CAAC;IACnB,OAAO,cAAc,UAAU,QAAQ;AAC3C;AACO,MAAM,UAAU,CAAC;IACpB,OAAO,cAAc,WAAW,SAAS;AAC7C;AACA,MAAM,UAAU,CAAC,SAAS,KAAK,OAAO,CAAC;IACnC,IAAI,SAAS,GAAG;QACZ,IAAI,OAAO,CAAC,IAAI,MAAM;QACtB,IAAI,OAAO,CAAC;IAChB;IACA,MAAM,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE;IAClC,IAAI,MAAM,CAAC,GACP,OAAO;IACX,MAAM,MAAM,QAAQ,QAAQ,CAAC,GAAG,IAAI,IAAI,MAAM;IAC9C,IAAI,IAAI,MAAM,KAAK,IAAI,MAAM,EACzB,OAAO;IACX,OAAO,IAAI,KAAK,CAAC,CAAC,OAAO,QAAU,UAAU,GAAG,CAAC,MAAM,KAAK,QAAQ,SAAS,KAAK,IAAI;AAC1F;AACA,MAAM,gBAAgB,CAAC;IACnB,OAAQ;QACJ,KAAK,QAAQ,SAAS;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;YAClE,OAAO;QACX,KAAK,QAAQ,SAAS;YAAC;YAAM;YAAM;YAAM;YAAM;SAAK;YAChD,OAAO;QACX,KAAK,QAAQ,SAAS;YAAC;YAAM;YAAM;YAAM;YAAM;SAAK;YAChD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,gBAAgB,OAAO,SAAS,WAAW,KAAK,KAAK;IACvD,IAAI;IACJ,IAAI;IACJ,MAAM,UAAU,IAAI,WAAW,KAAK,IAAI,OAAO,CAAC,SAAS,KACpD,KAAK,CAAC,IACN,GAAG,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC;IAC7B,MAAM,WAAW,cAAc;IAC/B,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,YAAY;gBAAE,MAAM;gBAAW,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI;YAAC;YAC5D,YAAY,WAAW;gBAAC;aAAS,GAAG;gBAAC;aAAO;YAC5C;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,YAAY;gBAAE,MAAM;gBAAqB,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI;YAAC;YACtE,YAAY,WAAW;gBAAC;aAAS,GAAG;gBAAC;aAAO;YAC5C;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,YAAY;gBACR,MAAM;gBACN,MAAM,CAAC,IAAI,EAAE,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI,OAAO,GAAG;YACnD;YACA,YAAY,WAAW;gBAAC;gBAAW;aAAU,GAAG;gBAAC;gBAAW;aAAY;YACxE;QACJ,KAAK;YACD,YAAY;gBAAE,MAAM;gBAAS,YAAY;YAAQ;YACjD,YAAY,WAAW;gBAAC;aAAS,GAAG;gBAAC;aAAO;YAC5C;QACJ,KAAK;YACD,YAAY;gBAAE,MAAM;gBAAS,YAAY;YAAQ;YACjD,YAAY,WAAW;gBAAC;aAAS,GAAG;gBAAC;aAAO;YAC5C;QACJ,KAAK;YACD,YAAY;gBAAE,MAAM;gBAAS,YAAY;YAAQ;YACjD,YAAY,WAAW;gBAAC;aAAS,GAAG;gBAAC;aAAO;YAC5C;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAkB;gBACnB,MAAM,aAAa,cAAc;gBACjC,YAAY,YAAY,WAAW,QAAQ;oBAAE,MAAM;oBAAQ;gBAAW,IAAI;oBAAE,MAAM;gBAAS;gBAC3F,YAAY,WAAW,EAAE,GAAG;oBAAC;iBAAa;gBAC1C;YACJ;QACA,KAAK;QACL,KAAK;YACD,YAAY;gBAAE,MAAM;YAAU;YAC9B,YAAY,WAAW;gBAAC;aAAS,GAAG;gBAAC;aAAO;YAC5C;QACJ;YACI,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC;IACnC;IACA,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,WAAW,SAAS,WAAW,SAAS,eAAe,CAAC,WAAW,OAAO,KAAK,GAAG;AACrH;AACO,MAAM,YAAY,CAAC,KAAK,KAAK;IAChC,OAAO,cAAc,+CAA+C,SAAS,KAAK,KAAK;AAC3F;AACO,MAAM,WAAW,CAAC,KAAK,KAAK;IAC/B,OAAO,cAAc,8CAA8C,QAAQ,KAAK,KAAK;AACzF;AACA,SAAS,WAAW,GAAG;IACnB,MAAM,SAAS,EAAE;IACjB,IAAI,OAAO;IACX,MAAO,OAAO,IAAI,MAAM,CAAE;QACtB,MAAM,WAAW,aAAa,IAAI,QAAQ,CAAC;QAC3C,OAAO,IAAI,CAAC;QACZ,QAAQ,SAAS,UAAU;IAC/B;IACA,OAAO;AACX;AACA,SAAS,aAAa,KAAK;IACvB,IAAI,WAAW;IACf,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG;IACrB;IACA,IAAI,QAAQ,MAAM;QACd,MAAM;QACN,MAAO,KAAK,CAAC,SAAS,IAAI,KAAM;YAC5B,MAAM,MAAM,MAAM,KAAK,CAAC,SAAS,GAAG;YACpC;QACJ;QACA,MAAM,MAAM,MAAM,KAAK,CAAC,SAAS,GAAG;QACpC;IACJ;IACA,IAAI,SAAS;IACb,IAAI,KAAK,CAAC,SAAS,GAAG,MAAM;QACxB,SAAS,KAAK,CAAC,SAAS;QACxB;IACJ,OACK,IAAI,WAAW,MAAM;QACtB,SAAS;QACT,MAAO,KAAK,CAAC,WAAW,OAAO,KAAK,KAAK,KAAK,CAAC,WAAW,SAAS,EAAE,KAAK,EAAG;YACzE,IAAI,SAAS,MAAM,UAAU,EAAE;gBAC3B,MAAM,IAAI,UAAU;YACxB;YACA;QACJ;QACA,MAAM,aAAa,WAAW,SAAS;QACvC,OAAO;YACH;YACA,UAAU,MAAM,QAAQ,CAAC,UAAU,WAAW;YAC9C,KAAK,MAAM,QAAQ,CAAC,GAAG;QAC3B;IACJ,OACK;QACD,MAAM,iBAAiB,KAAK,CAAC,SAAS,GAAG;QACzC;QACA,SAAS;QACT,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;YACrC,SAAS,SAAS,MAAM,KAAK,CAAC,SAAS;YACvC;QACJ;IACJ;IACA,MAAM,aAAa,WAAW;IAC9B,OAAO;QACH;QACA,UAAU,MAAM,QAAQ,CAAC,UAAU;QACnC,KAAK,MAAM,QAAQ,CAAC,GAAG;IAC3B;AACJ;AACA,SAAS,aAAa,GAAG;IACrB,MAAM,iBAAiB,WAAW,WAAW,aAAa,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC,QAAQ;IACpF,OAAO,CAAA,GAAA,+JAAA,CAAA,eAAY,AAAD,EAAE,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,OAAO,IAAI,EAAE,CAAC,GAAG;AACrF;AACA,IAAI;AACJ,SAAS,QAAQ,IAAI;IACjB,IAAI;QACA,oBAAoB,WAAW,OAAO,EAAE,mBAAmB,gBAAgB;IAC/E,EACA,OAAM;QACF,kBAAkB;IACtB;IACA,IAAI,iBAAiB;QACjB,IAAI;YACA,OAAO,IAAI,gBAAgB,MAAM,MAAM,CAAC;gBAAE,QAAQ;gBAAO,MAAM;YAAO;QAC1E,EACA,OAAM,CAAE;IACZ;IACA,MAAM,MAAM,KAAK,OAAO,CAAC,+CAA+C;IACxE,MAAM,MAAM,CAAA,GAAA,+JAAA,CAAA,eAAY,AAAD,EAAE;IACzB,OAAO,UAAU,aAAa,MAAM;AACxC;AACO,MAAM,WAAW,CAAC,KAAK,KAAK;IAC/B,IAAI;IACJ,IAAI;QACA,OAAO,QAAQ;IACnB,EACA,OAAO,OAAO;QACV,MAAM,IAAI,UAAU,yCAAyC;YAAE;QAAM;IACzE;IACA,OAAO,SAAS,MAAM,KAAK;AAC/B", "ignoreList": [0]}}, {"offset": {"line": 2107, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/key/import.js"], "sourcesContent": ["import { decode as decodeBase64URL } from '../util/base64url.js';\nimport { fromSPKI, fromPKCS8, fromX509 } from '../lib/asn1.js';\nimport toCryptoKey from '../lib/jwk_to_key.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport isObject from '../lib/is_object.js';\nexport async function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return fromSPKI(spki, alg, options);\n}\nexport async function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return fromX509(x509, alg, options);\n}\nexport async function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return fromPKCS8(pkcs8, alg, options);\n}\nexport async function importJWK(jwk, alg, options) {\n    if (!isObject(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    let ext;\n    alg ??= jwk.alg;\n    ext ??= options?.extractable ?? jwk.ext;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return decodeBase64URL(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return toCryptoKey({ ...jwk, alg, ext });\n        default:\n            throw new JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,eAAe,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO;IAC/C,IAAI,OAAO,SAAS,YAAY,KAAK,OAAO,CAAC,kCAAkC,GAAG;QAC9E,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,KAAK;AAC/B;AACO,eAAe,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO;IAC/C,IAAI,OAAO,SAAS,YAAY,KAAK,OAAO,CAAC,mCAAmC,GAAG;QAC/E,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,KAAK;AAC/B;AACO,eAAe,YAAY,KAAK,EAAE,GAAG,EAAE,OAAO;IACjD,IAAI,OAAO,UAAU,YAAY,MAAM,OAAO,CAAC,mCAAmC,GAAG;QACjF,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK;AACjC;AACO,eAAe,UAAU,GAAG,EAAE,GAAG,EAAE,OAAO;IAC7C,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,MAAM;QAChB,MAAM,IAAI,UAAU;IACxB;IACA,IAAI;IACJ,QAAQ,IAAI,GAAG;IACf,QAAQ,SAAS,eAAe,IAAI,GAAG;IACvC,OAAQ,IAAI,GAAG;QACX,KAAK;YACD,IAAI,OAAO,IAAI,CAAC,KAAK,YAAY,CAAC,IAAI,CAAC,EAAE;gBACrC,MAAM,IAAI,UAAU;YACxB;YACA,OAAO,CAAA,GAAA,mKAAA,CAAA,SAAe,AAAD,EAAE,IAAI,CAAC;QAChC,KAAK;YACD,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,WAAW;gBACvC,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC;YAC/B;QACJ,KAAK;QACL,KAAK;YACD,OAAO,CAAA,GAAA,mKAAA,CAAA,UAAW,AAAD,EAAE;gBAAE,GAAG,GAAG;gBAAE;gBAAK;YAAI;QAC1C;YACI,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC;IACnC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/jwks/local.js"], "sourcesContent": ["import { importJWK } from '../key/import.js';\nimport { J<PERSON><PERSON>Invalid, JOSENotSupported, JWKSNoMatchingKey, JWKSMultipleMatchingKeys, } from '../util/errors.js';\nimport isObject from '../lib/is_object.js';\nfunction getKtyFromAlg(alg) {\n    switch (typeof alg === 'string' && alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            return 'RSA';\n        case 'ES':\n            return 'EC';\n        case 'Ed':\n            return 'OKP';\n        default:\n            throw new JOSENotSupported('Unsupported \"alg\" value for a JSON Web Key Set');\n    }\n}\nfunction isJWKSLike(jwks) {\n    return (jwks &&\n        typeof jwks === 'object' &&\n        Array.isArray(jwks.keys) &&\n        jwks.keys.every(isJWKLike));\n}\nfunction isJWKLike(key) {\n    return isObject(key);\n}\nclass LocalJWKSet {\n    #jwks;\n    #cached = new WeakMap();\n    constructor(jwks) {\n        if (!isJWKSLike(jwks)) {\n            throw new JWKSInvalid('JSON Web Key Set malformed');\n        }\n        this.#jwks = structuredClone(jwks);\n    }\n    jwks() {\n        return this.#jwks;\n    }\n    async getKey(protectedHeader, token) {\n        const { alg, kid } = { ...protectedHeader, ...token?.header };\n        const kty = getKtyFromAlg(alg);\n        const candidates = this.#jwks.keys.filter((jwk) => {\n            let candidate = kty === jwk.kty;\n            if (candidate && typeof kid === 'string') {\n                candidate = kid === jwk.kid;\n            }\n            if (candidate && typeof jwk.alg === 'string') {\n                candidate = alg === jwk.alg;\n            }\n            if (candidate && typeof jwk.use === 'string') {\n                candidate = jwk.use === 'sig';\n            }\n            if (candidate && Array.isArray(jwk.key_ops)) {\n                candidate = jwk.key_ops.includes('verify');\n            }\n            if (candidate) {\n                switch (alg) {\n                    case 'ES256':\n                        candidate = jwk.crv === 'P-256';\n                        break;\n                    case 'ES384':\n                        candidate = jwk.crv === 'P-384';\n                        break;\n                    case 'ES512':\n                        candidate = jwk.crv === 'P-521';\n                        break;\n                    case 'Ed25519':\n                    case 'EdDSA':\n                        candidate = jwk.crv === 'Ed25519';\n                        break;\n                }\n            }\n            return candidate;\n        });\n        const { 0: jwk, length } = candidates;\n        if (length === 0) {\n            throw new JWKSNoMatchingKey();\n        }\n        if (length !== 1) {\n            const error = new JWKSMultipleMatchingKeys();\n            const _cached = this.#cached;\n            error[Symbol.asyncIterator] = async function* () {\n                for (const jwk of candidates) {\n                    try {\n                        yield await importWithAlgCache(_cached, jwk, alg);\n                    }\n                    catch { }\n                }\n            };\n            throw error;\n        }\n        return importWithAlgCache(this.#cached, jwk, alg);\n    }\n}\nasync function importWithAlgCache(cache, jwk, alg) {\n    const cached = cache.get(jwk) || cache.set(jwk, {}).get(jwk);\n    if (cached[alg] === undefined) {\n        const key = await importJWK({ ...jwk, ext: true }, alg);\n        if (key instanceof Uint8Array || key.type !== 'public') {\n            throw new JWKSInvalid('JSON Web Key Set members must be public keys');\n        }\n        cached[alg] = key;\n    }\n    return cached[alg];\n}\nexport function createLocalJWKSet(jwks) {\n    const set = new LocalJWKSet(jwks);\n    const localJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(localJWKSet, {\n        jwks: {\n            value: () => structuredClone(set.jwks()),\n            enumerable: false,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return localJWKSet;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,cAAc,GAAG;IACtB,OAAQ,OAAO,QAAQ,YAAY,IAAI,KAAK,CAAC,GAAG;QAC5C,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,gKAAA,CAAA,mBAAgB,CAAC;IACnC;AACJ;AACA,SAAS,WAAW,IAAI;IACpB,OAAQ,QACJ,OAAO,SAAS,YAChB,MAAM,OAAO,CAAC,KAAK,IAAI,KACvB,KAAK,IAAI,CAAC,KAAK,CAAC;AACxB;AACA,SAAS,UAAU,GAAG;IAClB,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;AACpB;AACA,MAAM;IACF,CAAA,IAAK,CAAC;IACN,CAAA,MAAO,GAAG,IAAI,UAAU;IACxB,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,WAAW,OAAO;YACnB,MAAM,IAAI,gKAAA,CAAA,cAAW,CAAC;QAC1B;QACA,IAAI,CAAC,CAAA,IAAK,GAAG,gBAAgB;IACjC;IACA,OAAO;QACH,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;IACA,MAAM,OAAO,eAAe,EAAE,KAAK,EAAE;QACjC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;YAAE,GAAG,eAAe;YAAE,GAAG,OAAO,MAAM;QAAC;QAC5D,MAAM,MAAM,cAAc;QAC1B,MAAM,aAAa,IAAI,CAAC,CAAA,IAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,YAAY,QAAQ,IAAI,GAAG;YAC/B,IAAI,aAAa,OAAO,QAAQ,UAAU;gBACtC,YAAY,QAAQ,IAAI,GAAG;YAC/B;YACA,IAAI,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;gBAC1C,YAAY,QAAQ,IAAI,GAAG;YAC/B;YACA,IAAI,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;gBAC1C,YAAY,IAAI,GAAG,KAAK;YAC5B;YACA,IAAI,aAAa,MAAM,OAAO,CAAC,IAAI,OAAO,GAAG;gBACzC,YAAY,IAAI,OAAO,CAAC,QAAQ,CAAC;YACrC;YACA,IAAI,WAAW;gBACX,OAAQ;oBACJ,KAAK;wBACD,YAAY,IAAI,GAAG,KAAK;wBACxB;oBACJ,KAAK;wBACD,YAAY,IAAI,GAAG,KAAK;wBACxB;oBACJ,KAAK;wBACD,YAAY,IAAI,GAAG,KAAK;wBACxB;oBACJ,KAAK;oBACL,KAAK;wBACD,YAAY,IAAI,GAAG,KAAK;wBACxB;gBACR;YACJ;YACA,OAAO;QACX;QACA,MAAM,EAAE,GAAG,GAAG,EAAE,MAAM,EAAE,GAAG;QAC3B,IAAI,WAAW,GAAG;YACd,MAAM,IAAI,gKAAA,CAAA,oBAAiB;QAC/B;QACA,IAAI,WAAW,GAAG;YACd,MAAM,QAAQ,IAAI,gKAAA,CAAA,2BAAwB;YAC1C,MAAM,UAAU,IAAI,CAAC,CAAA,MAAO;YAC5B,KAAK,CAAC,OAAO,aAAa,CAAC,GAAG;gBAC1B,KAAK,MAAM,OAAO,WAAY;oBAC1B,IAAI;wBACA,MAAM,MAAM,mBAAmB,SAAS,KAAK;oBACjD,EACA,OAAM,CAAE;gBACZ;YACJ;YACA,MAAM;QACV;QACA,OAAO,mBAAmB,IAAI,CAAC,CAAA,MAAO,EAAE,KAAK;IACjD;AACJ;AACA,eAAe,mBAAmB,KAAK,EAAE,GAAG,EAAE,GAAG;IAC7C,MAAM,SAAS,MAAM,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;IACxD,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;QAC3B,MAAM,MAAM,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE;YAAE,GAAG,GAAG;YAAE,KAAK;QAAK,GAAG;QACnD,IAAI,eAAe,cAAc,IAAI,IAAI,KAAK,UAAU;YACpD,MAAM,IAAI,gKAAA,CAAA,cAAW,CAAC;QAC1B;QACA,MAAM,CAAC,IAAI,GAAG;IAClB;IACA,OAAO,MAAM,CAAC,IAAI;AACtB;AACO,SAAS,kBAAkB,IAAI;IAClC,MAAM,MAAM,IAAI,YAAY;IAC5B,MAAM,cAAc,OAAO,iBAAiB,QAAU,IAAI,MAAM,CAAC,iBAAiB;IAClF,OAAO,gBAAgB,CAAC,aAAa;QACjC,MAAM;YACF,OAAO,IAAM,gBAAgB,IAAI,IAAI;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;QACd;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2306, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/jwks/remote.js"], "sourcesContent": ["import { J<PERSON><PERSON><PERSON>r, JWKSNoMatching<PERSON>ey, JWKSTimeout } from '../util/errors.js';\nimport { createLocalJWKSet } from './local.js';\nimport isObject from '../lib/is_object.js';\nfunction isCloudflareWorkers() {\n    return (typeof WebSocketPair !== 'undefined' ||\n        (typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers') ||\n        (typeof EdgeRuntime !== 'undefined' && EdgeRuntime === 'vercel'));\n}\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'jose';\n    const VERSION = 'v6.0.11';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nexport const customFetch = Symbol();\nasync function fetchJwks(url, headers, signal, fetchImpl = fetch) {\n    const response = await fetchImpl(url, {\n        method: 'GET',\n        signal,\n        redirect: 'manual',\n        headers,\n    }).catch((err) => {\n        if (err.name === 'TimeoutError') {\n            throw new JWKSTimeout();\n        }\n        throw err;\n    });\n    if (response.status !== 200) {\n        throw new JOSEError('Expected 200 OK from the JSON Web Key Set HTTP response');\n    }\n    try {\n        return await response.json();\n    }\n    catch {\n        throw new JOSEError('Failed to parse the JSON Web Key Set HTTP response as JSON');\n    }\n}\nexport const jwksCache = Symbol();\nfunction isFreshJwksCache(input, cacheMaxAge) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || Date.now() - input.uat >= cacheMaxAge) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isObject)) {\n        return false;\n    }\n    return true;\n}\nclass RemoteJWKSet {\n    #url;\n    #timeoutDuration;\n    #cooldownDuration;\n    #cacheMaxAge;\n    #jwksTimestamp;\n    #pendingFetch;\n    #headers;\n    #customFetch;\n    #local;\n    #cache;\n    constructor(url, options) {\n        if (!(url instanceof URL)) {\n            throw new TypeError('url must be an instance of URL');\n        }\n        this.#url = new URL(url.href);\n        this.#timeoutDuration =\n            typeof options?.timeoutDuration === 'number' ? options?.timeoutDuration : 5000;\n        this.#cooldownDuration =\n            typeof options?.cooldownDuration === 'number' ? options?.cooldownDuration : 30000;\n        this.#cacheMaxAge = typeof options?.cacheMaxAge === 'number' ? options?.cacheMaxAge : 600000;\n        this.#headers = new Headers(options?.headers);\n        if (USER_AGENT && !this.#headers.has('User-Agent')) {\n            this.#headers.set('User-Agent', USER_AGENT);\n        }\n        if (!this.#headers.has('accept')) {\n            this.#headers.set('accept', 'application/json');\n            this.#headers.append('accept', 'application/jwk-set+json');\n        }\n        this.#customFetch = options?.[customFetch];\n        if (options?.[jwksCache] !== undefined) {\n            this.#cache = options?.[jwksCache];\n            if (isFreshJwksCache(options?.[jwksCache], this.#cacheMaxAge)) {\n                this.#jwksTimestamp = this.#cache.uat;\n                this.#local = createLocalJWKSet(this.#cache.jwks);\n            }\n        }\n    }\n    pendingFetch() {\n        return !!this.#pendingFetch;\n    }\n    coolingDown() {\n        return typeof this.#jwksTimestamp === 'number'\n            ? Date.now() < this.#jwksTimestamp + this.#cooldownDuration\n            : false;\n    }\n    fresh() {\n        return typeof this.#jwksTimestamp === 'number'\n            ? Date.now() < this.#jwksTimestamp + this.#cacheMaxAge\n            : false;\n    }\n    jwks() {\n        return this.#local?.jwks();\n    }\n    async getKey(protectedHeader, token) {\n        if (!this.#local || !this.fresh()) {\n            await this.reload();\n        }\n        try {\n            return await this.#local(protectedHeader, token);\n        }\n        catch (err) {\n            if (err instanceof JWKSNoMatchingKey) {\n                if (this.coolingDown() === false) {\n                    await this.reload();\n                    return this.#local(protectedHeader, token);\n                }\n            }\n            throw err;\n        }\n    }\n    async reload() {\n        if (this.#pendingFetch && isCloudflareWorkers()) {\n            this.#pendingFetch = undefined;\n        }\n        this.#pendingFetch ||= fetchJwks(this.#url.href, this.#headers, AbortSignal.timeout(this.#timeoutDuration), this.#customFetch)\n            .then((json) => {\n            this.#local = createLocalJWKSet(json);\n            if (this.#cache) {\n                this.#cache.uat = Date.now();\n                this.#cache.jwks = json;\n            }\n            this.#jwksTimestamp = Date.now();\n            this.#pendingFetch = undefined;\n        })\n            .catch((err) => {\n            this.#pendingFetch = undefined;\n            throw err;\n        });\n        await this.#pendingFetch;\n    }\n}\nexport function createRemoteJWKSet(url, options) {\n    const set = new RemoteJWKSet(url, options);\n    const remoteJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(remoteJWKSet, {\n        coolingDown: {\n            get: () => set.coolingDown(),\n            enumerable: true,\n            configurable: false,\n        },\n        fresh: {\n            get: () => set.fresh(),\n            enumerable: true,\n            configurable: false,\n        },\n        reload: {\n            value: () => set.reload(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n        reloading: {\n            get: () => set.pendingFetch(),\n            enumerable: true,\n            configurable: false,\n        },\n        jwks: {\n            value: () => set.jwks(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return remoteJWKSet;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AACA,SAAS;IACL,OAAQ,OAAO,kBAAkB,eAC5B,OAAO,cAAc,eAAe,UAAU,SAAS,KAAK,wBAC5D,4DAAuB,eAAe,qDAAgB;AAC/D;AACA,IAAI;AACJ,IAAI,OAAO,cAAc,eAAe,CAAC,UAAU,SAAS,EAAE,aAAa,iBAAiB;IACxF,MAAM,OAAO;IACb,MAAM,UAAU;IAChB,aAAa,GAAG,KAAK,CAAC,EAAE,SAAS;AACrC;AACO,MAAM,cAAc;AAC3B,eAAe,UAAU,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,KAAK;IAC5D,MAAM,WAAW,MAAM,UAAU,KAAK;QAClC,QAAQ;QACR;QACA,UAAU;QACV;IACJ,GAAG,KAAK,CAAC,CAAC;QACN,IAAI,IAAI,IAAI,KAAK,gBAAgB;YAC7B,MAAM,IAAI,gKAAA,CAAA,cAAW;QACzB;QACA,MAAM;IACV;IACA,IAAI,SAAS,MAAM,KAAK,KAAK;QACzB,MAAM,IAAI,gKAAA,CAAA,YAAS,CAAC;IACxB;IACA,IAAI;QACA,OAAO,MAAM,SAAS,IAAI;IAC9B,EACA,OAAM;QACF,MAAM,IAAI,gKAAA,CAAA,YAAS,CAAC;IACxB;AACJ;AACO,MAAM,YAAY;AACzB,SAAS,iBAAiB,KAAK,EAAE,WAAW;IACxC,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;QAC7C,OAAO;IACX;IACA,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,OAAO,MAAM,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,MAAM,GAAG,IAAI,aAAa;QAC7F,OAAO;IACX;IACA,IAAI,CAAC,CAAC,UAAU,KAAK,KACjB,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,IAAI,KACpB,CAAC,MAAM,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,KAC9B,CAAC,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,kKAAA,CAAA,UAAQ,GAAG;QACxD,OAAO;IACX;IACA,OAAO;AACX;AACA,MAAM;IACF,CAAA,GAAI,CAAC;IACL,CAAA,eAAgB,CAAC;IACjB,CAAA,gBAAiB,CAAC;IAClB,CAAA,WAAY,CAAC;IACb,CAAA,aAAc,CAAC;IACf,CAAA,YAAa,CAAC;IACd,CAAA,OAAQ,CAAC;IACT,CAAA,WAAY,CAAC;IACb,CAAA,KAAM,CAAC;IACP,CAAA,KAAM,CAAC;IACP,YAAY,GAAG,EAAE,OAAO,CAAE;QACtB,IAAI,CAAC,CAAC,eAAe,GAAG,GAAG;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,GAAI,GAAG,IAAI,IAAI,IAAI,IAAI;QAC5B,IAAI,CAAC,CAAA,eAAgB,GACjB,OAAO,SAAS,oBAAoB,WAAW,SAAS,kBAAkB;QAC9E,IAAI,CAAC,CAAA,gBAAiB,GAClB,OAAO,SAAS,qBAAqB,WAAW,SAAS,mBAAmB;QAChF,IAAI,CAAC,CAAA,WAAY,GAAG,OAAO,SAAS,gBAAgB,WAAW,SAAS,cAAc;QACtF,IAAI,CAAC,CAAA,OAAQ,GAAG,IAAI,QAAQ,SAAS;QACrC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,CAAC,eAAe;YAChD,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,CAAC,cAAc;QACpC;QACA,IAAI,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,CAAC,WAAW;YAC9B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,CAAC,UAAU;YAC5B,IAAI,CAAC,CAAA,OAAQ,CAAC,MAAM,CAAC,UAAU;QACnC;QACA,IAAI,CAAC,CAAA,WAAY,GAAG,SAAS,CAAC,YAAY;QAC1C,IAAI,SAAS,CAAC,UAAU,KAAK,WAAW;YACpC,IAAI,CAAC,CAAA,KAAM,GAAG,SAAS,CAAC,UAAU;YAClC,IAAI,iBAAiB,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA,WAAY,GAAG;gBAC3D,IAAI,CAAC,CAAA,aAAc,GAAG,IAAI,CAAC,CAAA,KAAM,CAAC,GAAG;gBACrC,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI;YACpD;QACJ;IACJ;IACA,eAAe;QACX,OAAO,CAAC,CAAC,IAAI,CAAC,CAAA,YAAa;IAC/B;IACA,cAAc;QACV,OAAO,OAAO,IAAI,CAAC,CAAA,aAAc,KAAK,WAChC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA,aAAc,GAAG,IAAI,CAAC,CAAA,gBAAiB,GACzD;IACV;IACA,QAAQ;QACJ,OAAO,OAAO,IAAI,CAAC,CAAA,aAAc,KAAK,WAChC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAA,aAAc,GAAG,IAAI,CAAC,CAAA,WAAY,GACpD;IACV;IACA,OAAO;QACH,OAAO,IAAI,CAAC,CAAA,KAAM,EAAE;IACxB;IACA,MAAM,OAAO,eAAe,EAAE,KAAK,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,CAAA,KAAM,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;YAC/B,MAAM,IAAI,CAAC,MAAM;QACrB;QACA,IAAI;YACA,OAAO,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB;QAC9C,EACA,OAAO,KAAK;YACR,IAAI,eAAe,gKAAA,CAAA,oBAAiB,EAAE;gBAClC,IAAI,IAAI,CAAC,WAAW,OAAO,OAAO;oBAC9B,MAAM,IAAI,CAAC,MAAM;oBACjB,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB;gBACxC;YACJ;YACA,MAAM;QACV;IACJ;IACA,MAAM,SAAS;QACX,IAAI,IAAI,CAAC,CAAA,YAAa,IAAI,uBAAuB;YAC7C,IAAI,CAAC,CAAA,YAAa,GAAG;QACzB;QACA,IAAI,CAAC,CAAA,YAAa,KAAK,UAAU,IAAI,CAAC,CAAA,GAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,OAAQ,EAAE,YAAY,OAAO,CAAC,IAAI,CAAC,CAAA,eAAgB,GAAG,IAAI,CAAC,CAAA,WAAY,EACxH,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE;YAChC,IAAI,IAAI,CAAC,CAAA,KAAM,EAAE;gBACb,IAAI,CAAC,CAAA,KAAM,CAAC,GAAG,GAAG,KAAK,GAAG;gBAC1B,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,GAAG;YACvB;YACA,IAAI,CAAC,CAAA,aAAc,GAAG,KAAK,GAAG;YAC9B,IAAI,CAAC,CAAA,YAAa,GAAG;QACzB,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,CAAA,YAAa,GAAG;YACrB,MAAM;QACV;QACA,MAAM,IAAI,CAAC,CAAA,YAAa;IAC5B;AACJ;AACO,SAAS,mBAAmB,GAAG,EAAE,OAAO;IAC3C,MAAM,MAAM,IAAI,aAAa,KAAK;IAClC,MAAM,eAAe,OAAO,iBAAiB,QAAU,IAAI,MAAM,CAAC,iBAAiB;IACnF,OAAO,gBAAgB,CAAC,cAAc;QAClC,aAAa;YACT,KAAK,IAAM,IAAI,WAAW;YAC1B,YAAY;YACZ,cAAc;QAClB;QACA,OAAO;YACH,KAAK,IAAM,IAAI,KAAK;YACpB,YAAY;YACZ,cAAc;QAClB;QACA,QAAQ;YACJ,OAAO,IAAM,IAAI,MAAM;YACvB,YAAY;YACZ,cAAc;YACd,UAAU;QACd;QACA,WAAW;YACP,KAAK,IAAM,IAAI,YAAY;YAC3B,YAAY;YACZ,cAAc;QAClB;QACA,MAAM;YACF,OAAO,IAAM,IAAI,IAAI;YACrB,YAAY;YACZ,cAAc;YACd,UAAU;QACd;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2484, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/lib/sign.js"], "sourcesContent": ["import subtleAlgorithm from './subtle_dsa.js';\nimport checkKey<PERSON>ength from './check_key_length.js';\nimport getSignKey from './get_sign_verify_key.js';\nexport default async (alg, key, data) => {\n    const cryptoKey = await getSignKey(alg, key, 'sign');\n    checkKeyLength(alg, cryptoKey);\n    const signature = await crypto.subtle.sign(subtleAlgorithm(alg, cryptoKey.algorithm), cryptoKey, data);\n    return new Uint8Array(signature);\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCACe,OAAO,KAAK,KAAK;IAC5B,MAAM,YAAY,MAAM,CAAA,GAAA,4KAAA,CAAA,UAAU,AAAD,EAAE,KAAK,KAAK;IAC7C,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,KAAK;IACpB,MAAM,YAAY,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,mKAAA,CAAA,UAAe,AAAD,EAAE,KAAK,UAAU,SAAS,GAAG,WAAW;IACjG,OAAO,IAAI,WAAW;AAC1B", "ignoreList": [0]}}, {"offset": {"line": 2505, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/jws/flattened/sign.js"], "sourcesContent": ["import { encode as b64u } from '../../util/base64url.js';\nimport sign from '../../lib/sign.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport class FlattenedSign {\n    #payload;\n    #protectedHeader;\n    #unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this.#payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this.#protectedHeader && !this.#unprotectedHeader) {\n            throw new JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!isDisjoint(this.#protectedHeader, this.#unprotectedHeader)) {\n            throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this.#protectedHeader,\n            ...this.#unprotectedHeader,\n        };\n        const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, this.#protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this.#protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        checkKeyType(alg, key, 'sign');\n        let payload = this.#payload;\n        if (b64) {\n            payload = encoder.encode(b64u(payload));\n        }\n        let protectedHeader;\n        if (this.#protectedHeader) {\n            protectedHeader = encoder.encode(b64u(JSON.stringify(this.#protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        const data = concat(protectedHeader, encoder.encode('.'), payload);\n        const k = await normalizeKey(key, alg);\n        const signature = await sign(alg, k, data);\n        const jws = {\n            signature: b64u(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = decoder.decode(payload);\n        }\n        if (this.#unprotectedHeader) {\n            jws.header = this.#unprotectedHeader;\n        }\n        if (this.#protectedHeader) {\n            jws.protected = decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,MAAM;IACT,CAAA,OAAQ,CAAC;IACT,CAAA,eAAgB,CAAC;IACjB,CAAA,iBAAkB,CAAC;IACnB,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,CAAC,mBAAmB,UAAU,GAAG;YAClC,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,OAAQ,GAAG;IACpB;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,CAAA,eAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,eAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,qBAAqB,iBAAiB,EAAE;QACpC,IAAI,IAAI,CAAC,CAAA,iBAAkB,EAAE;YACzB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,iBAAkB,GAAG;QAC1B,OAAO,IAAI;IACf;IACA,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,CAAA,eAAgB,IAAI,CAAC,IAAI,CAAC,CAAA,iBAAkB,EAAE;YACpD,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;QACzB;QACA,IAAI,CAAC,CAAA,GAAA,oKAAA,CAAA,UAAU,AAAD,EAAE,IAAI,CAAC,CAAA,eAAgB,EAAE,IAAI,CAAC,CAAA,iBAAkB,GAAG;YAC7D,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;QACzB;QACA,MAAM,aAAa;YACf,GAAG,IAAI,CAAC,CAAA,eAAgB;YACxB,GAAG,IAAI,CAAC,CAAA,iBAAkB;QAC9B;QACA,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,UAAY,AAAD,EAAE,gKAAA,CAAA,aAAU,EAAE,IAAI,IAAI;YAAC;gBAAC;gBAAO;aAAK;SAAC,GAAG,SAAS,MAAM,IAAI,CAAC,CAAA,eAAgB,EAAE;QAC5G,IAAI,MAAM;QACV,IAAI,WAAW,GAAG,CAAC,QAAQ;YACvB,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,GAAG;YAC/B,IAAI,OAAO,QAAQ,WAAW;gBAC1B,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;YACzB;QACJ;QACA,MAAM,EAAE,GAAG,EAAE,GAAG;QAChB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;QACzB;QACA,CAAA,GAAA,uKAAA,CAAA,UAAY,AAAD,EAAE,KAAK,KAAK;QACvB,IAAI,UAAU,IAAI,CAAC,CAAA,OAAQ;QAC3B,IAAI,KAAK;YACL,UAAU,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAA,GAAA,mKAAA,CAAA,SAAI,AAAD,EAAE;QAClC;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,CAAA,eAAgB,EAAE;YACvB,kBAAkB,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAA,GAAA,mKAAA,CAAA,SAAI,AAAD,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA,eAAgB;QAC9E,OACK;YACD,kBAAkB,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACrC;QACA,MAAM,OAAO,CAAA,GAAA,qKAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,MAAM;QAC1D,MAAM,IAAI,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAY,AAAD,EAAE,KAAK;QAClC,MAAM,YAAY,MAAM,CAAA,GAAA,6JAAA,CAAA,UAAI,AAAD,EAAE,KAAK,GAAG;QACrC,MAAM,MAAM;YACR,WAAW,CAAA,GAAA,mKAAA,CAAA,SAAI,AAAD,EAAE;YAChB,SAAS;QACb;QACA,IAAI,KAAK;YACL,IAAI,OAAO,GAAG,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACjC;QACA,IAAI,IAAI,CAAC,CAAA,iBAAkB,EAAE;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,CAAA,iBAAkB;QACxC;QACA,IAAI,IAAI,CAAC,CAAA,eAAgB,EAAE;YACvB,IAAI,SAAS,GAAG,qKAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACnC;QACA,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2612, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/jws/compact/sign.js"], "sourcesContent": ["import { FlattenedSign } from '../flattened/sign.js';\nexport class CompactSign {\n    #flattened;\n    constructor(payload) {\n        this.#flattened = new FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this.#flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM;IACT,CAAA,SAAU,CAAC;IACX,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,CAAA,SAAU,GAAG,IAAI,0KAAA,CAAA,gBAAa,CAAC;IACxC;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,CAAA,SAAU,CAAC,kBAAkB,CAAC;QACnC,OAAO,IAAI;IACf;IACA,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE;QACrB,MAAM,MAAM,MAAM,IAAI,CAAC,CAAA,SAAU,CAAC,IAAI,CAAC,KAAK;QAC5C,IAAI,IAAI,OAAO,KAAK,WAAW;YAC3B,MAAM,IAAI,UAAU;QACxB;QACA,OAAO,GAAG,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,SAAS,EAAE;IAC7D;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2640, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/jose/dist/webapi/jwt/sign.js"], "sourcesContent": ["import { CompactSign } from '../jws/compact/sign.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport { JWTClaimsBuilder } from '../lib/jwt_claims_set.js';\nexport class SignJWT {\n    #protectedHeader;\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new JWTClaimsBuilder(payload);\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        const sig = new CompactSign(this.#jwt.data());\n        sig.setProtectedHeader(this.#protectedHeader);\n        if (Array.isArray(this.#protectedHeader?.crit) &&\n            this.#protectedHeader.crit.includes('b64') &&\n            this.#protectedHeader.b64 === false) {\n            throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n        }\n        return sig.sign(key, options);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM;IACT,CAAA,eAAgB,CAAC;IACjB,CAAA,GAAI,CAAC;IACL,YAAY,UAAU,CAAC,CAAC,CAAE;QACtB,IAAI,CAAC,CAAA,GAAI,GAAG,IAAI,uKAAA,CAAA,mBAAgB,CAAC;IACrC;IACA,UAAU,MAAM,EAAE;QACd,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,WAAW,OAAO,EAAE;QAChB,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,YAAY,QAAQ,EAAE;QAClB,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,OAAO,KAAK,EAAE;QACV,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,aAAa,KAAK,EAAE;QAChB,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,kBAAkB,KAAK,EAAE;QACrB,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,YAAY,KAAK,EAAE;QACf,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,CAAA,eAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE;QACrB,MAAM,MAAM,IAAI,wKAAA,CAAA,cAAW,CAAC,IAAI,CAAC,CAAA,GAAI,CAAC,IAAI;QAC1C,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAA,eAAgB;QAC5C,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,eAAgB,EAAE,SACrC,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,UACpC,IAAI,CAAC,CAAA,eAAgB,CAAC,GAAG,KAAK,OAAO;YACrC,MAAM,IAAI,gKAAA,CAAA,aAAU,CAAC;QACzB;QACA,OAAO,IAAI,IAAI,CAAC,KAAK;IACzB;AACJ", "ignoreList": [0]}}]}