{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/node_modules_better-call_dist_index_5fc3d526.js", "server/edge/chunks/node_modules_zod_dist_esm_56e6e3e4._.js", "server/edge/chunks/node_modules_better-auth_dist_3fc72b3f._.js", "server/edge/chunks/node_modules_jose_dist_webapi_3c9fb492._.js", "server/edge/chunks/node_modules_kysely_dist_esm_67de29e4._.js", "server/edge/chunks/node_modules_drizzle-orm_84e3d8ae._.js", "server/edge/chunks/node_modules_8834b263._.js", "server/edge/chunks/[root-of-the-server]__7b9e1ef3._.js", "server/edge/chunks/edge-wrapper_576a9097.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|logo.png).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NLazTHWHqG6qrOTca7wVhgjhkQgwyYmk+oHwFJZLI9M=", "__NEXT_PREVIEW_MODE_ID": "a7fc7467d2c307530b577e0ddf96c889", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2e841d72c3ecfb541ad6f4a8c6ce5180a1caf1b5d53a2d5c0090dd8a3eecf7fb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ec2ea8323fc3039f1b49fda3e443e44ecebcb17167f8c2546edff09d5fb68587"}}}, "sortedMiddleware": ["/"], "functions": {}}