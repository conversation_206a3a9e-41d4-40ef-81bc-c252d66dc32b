{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/node_modules_better-call_dist_index_5fc3d526.js", "server/edge/chunks/node_modules_zod_dist_esm_56e6e3e4._.js", "server/edge/chunks/node_modules_better-auth_dist_3fc72b3f._.js", "server/edge/chunks/node_modules_jose_dist_webapi_3c9fb492._.js", "server/edge/chunks/node_modules_kysely_dist_esm_67de29e4._.js", "server/edge/chunks/node_modules_drizzle-orm_84e3d8ae._.js", "server/edge/chunks/node_modules_8834b263._.js", "server/edge/chunks/[root-of-the-server]__7b9e1ef3._.js", "server/edge/chunks/edge-wrapper_576a9097.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|logo.png).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NLazTHWHqG6qrOTca7wVhgjhkQgwyYmk+oHwFJZLI9M=", "__NEXT_PREVIEW_MODE_ID": "2ec9225b4221f6a10dfeee01732b41b6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "119cb0ea7871f8d950fc22b8bae9e1b9c45a2bf6402470d4264d3b6cdf2a4abf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fb3c7302eb0fa7cec043de79e202e441ea58c1277b647991d6690055b468416a"}}}, "instrumentation": null, "functions": {}}