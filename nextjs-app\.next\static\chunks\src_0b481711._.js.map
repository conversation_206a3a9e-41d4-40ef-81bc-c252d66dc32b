{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iPACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useRouter } from \"next/navigation\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Search, Mic, Upload } from \"lucide-react\"\n\ninterface SearchBarProps {\n  value: string\n  onChange: (value: string) => void\n  placeholder?: string\n}\n\nexport function SearchBar({ value, onChange, placeholder = \"Search...\" }: SearchBarProps) {\n  const [isListening, setIsListening] = useState(false)\n  const router = useRouter()\n\n  const handleSearch = () => {\n    if (value.trim()) {\n      router.push(`/study?q=${encodeURIComponent(value.trim())}`)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\") {\n      handleSearch()\n    }\n  }\n\n  const handleVoiceSearch = () => {\n    // Voice search functionality would be implemented here\n    setIsListening(!isListening)\n    // For now, just toggle the state\n  }\n\n  const handleFileUpload = () => {\n    // File upload functionality would be implemented here\n    console.log(\"File upload clicked\")\n  }\n\n  return (\n    <div className=\"relative\">\n      <div className=\"flex items-center space-x-2 p-2 border border-border rounded-lg bg-background shadow-sm\">\n        <Search className=\"h-5 w-5 text-muted-foreground ml-2\" />\n        <Input\n          type=\"text\"\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          onKeyPress={handleKeyPress}\n          placeholder={placeholder}\n          className=\"flex-1 border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0\"\n        />\n        \n        <div className=\"flex items-center space-x-1\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleVoiceSearch}\n            className={`h-8 w-8 ${isListening ? \"text-primary\" : \"text-muted-foreground\"}`}\n            title=\"Voice search\"\n          >\n            <Mic className=\"h-4 w-4\" />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleFileUpload}\n            className=\"h-8 w-8 text-muted-foreground\"\n            title=\"Upload image\"\n          >\n            <Upload className=\"h-4 w-4\" />\n          </Button>\n          \n          <Button\n            onClick={handleSearch}\n            size=\"sm\"\n            disabled={!value.trim()}\n          >\n            Search\n          </Button>\n        </div>\n      </div>\n      \n      {/* Search suggestions could be added here */}\n      {value && (\n        <div className=\"absolute top-full left-0 right-0 mt-1 bg-background border border-border rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\">\n          {/* Placeholder for search suggestions */}\n          <div className=\"p-2 text-sm text-muted-foreground\">\n            Press Enter to search for \"{value}\"\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AANA;;;;;;AAcO,SAAS,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,WAAW,EAAkB;;IACtF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI,MAAM,IAAI,IAAI;YAChB,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,mBAAmB,MAAM,IAAI,KAAK;QAC5D;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,uDAAuD;QACvD,eAAe,CAAC;IAChB,iCAAiC;IACnC;IAEA,MAAM,mBAAmB;QACvB,sDAAsD;QACtD,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC,oIAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,YAAY;wBACZ,aAAa;wBACb,WAAU;;;;;;kCAGZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAW,CAAC,QAAQ,EAAE,cAAc,iBAAiB,yBAAyB;gCAC9E,OAAM;0CAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAGjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,MAAK;gCACL,UAAU,CAAC,MAAM,IAAI;0CACtB;;;;;;;;;;;;;;;;;;YAOJ,uBACC,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;wBAAoC;wBACrB;wBAAM;;;;;;;;;;;;;;;;;;AAM9C;GAlFgB;;QAEC,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\n\ninterface SuggestionChipsProps {\n  onChipClick: (suggestion: string) => void\n}\n\nconst suggestions = [\n  \"Prayer times\",\n  \"Patience in Quran\",\n  \"Stories of prophets\",\n  \"Gratitude verses\",\n  \"Forgiveness\",\n  \"Charity and giving\",\n  \"Faith and belief\",\n  \"Paradise description\"\n]\n\nexport function SuggestionChips({ onChipClick }: SuggestionChipsProps) {\n  return (\n    <div className=\"mt-4\">\n      <p className=\"text-sm text-muted-foreground mb-3 text-center\">\n        Popular searches:\n      </p>\n      <div className=\"flex flex-wrap justify-center gap-2\">\n        {suggestions.map((suggestion) => (\n          <Button\n            key={suggestion}\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onChipClick(suggestion)}\n            className=\"text-xs hover:bg-primary hover:text-primary-foreground transition-colors\"\n          >\n            {suggestion}\n          </Button>\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQA,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,gBAAgB,EAAE,WAAW,EAAwB;IACnE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAiD;;;;;;0BAG9D,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,YAAY;wBAC3B,WAAU;kCAET;uBANI;;;;;;;;;;;;;;;;AAYjB;KArBgB", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/home/<USER>"], "sourcesContent": ["import { <PERSON>, <PERSON>yNote, <PERSON>, Book<PERSON>pen, MessageSquare, Zap } from \"lucide-react\"\n\nconst features = [\n  {\n    icon: Search,\n    title: \"Intelligent Search\",\n    description: \"Search the Quran by meaning, context, or keywords with AI-powered understanding\",\n    color: \"text-blue-500\"\n  },\n  {\n    icon: StickyNote,\n    title: \"Smart Notes\",\n    description: \"Generate personalized study notes, summaries, and insights from any verse\",\n    color: \"text-green-500\"\n  },\n  {\n    icon: Brain,\n    title: \"Interactive Learning\",\n    description: \"Create flashcards, quizzes, and games to reinforce your understanding\",\n    color: \"text-purple-500\"\n  },\n  {\n    icon: BookOpen,\n    title: \"Contextual Tafsir\",\n    description: \"Access multiple tafsir sources with contextual explanations and translations\",\n    color: \"text-orange-500\"\n  },\n  {\n    icon: MessageSquare,\n    title: \"AI Chat Assistant\",\n    description: \"Ask questions and get detailed explanations about verses and concepts\",\n    color: \"text-pink-500\"\n  },\n  {\n    icon: Zap,\n    title: \"OCR & Analysis\",\n    description: \"Upload images of text and get instant verse recognition and analysis\",\n    color: \"text-yellow-500\"\n  }\n]\n\nexport function FeatureCards() {\n  return (\n    <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {features.map((feature) => {\n        const IconComponent = feature.icon\n        return (\n          <div\n            key={feature.title}\n            className=\"p-6 border border-border rounded-lg hover:shadow-md transition-shadow bg-card\"\n          >\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <div className={`p-2 rounded-lg bg-muted ${feature.color}`}>\n                <IconComponent className=\"h-5 w-5\" />\n              </div>\n              <h3 className=\"font-semibold\">{feature.title}</h3>\n            </div>\n            <p className=\"text-sm text-muted-foreground\">\n              {feature.description}\n            </p>\n          </div>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,WAAW;IACf;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,iNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,2NAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC;YACb,MAAM,gBAAgB,QAAQ,IAAI;YAClC,qBACE,6LAAC;gBAEC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,KAAK,EAAE;0CACxD,cAAA,6LAAC;oCAAc,WAAU;;;;;;;;;;;0CAE3B,6LAAC;gCAAG,WAAU;0CAAiB,QAAQ,KAAK;;;;;;;;;;;;kCAE9C,6LAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;;eAVjB,QAAQ,KAAK;;;;;QAcxB;;;;;;AAGN;KAxBgB", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Coding%20Project/Tadabbur%20AI/tadabbur-ai-v2/nextjs-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Image from \"next/image\"\nimport Link from \"next/link\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { SearchBar } from \"@/components/home/<USER>\"\nimport { SuggestionChips } from \"@/components/home/<USER>\"\nimport { FeatureCards } from \"@/components/home/<USER>\"\nimport { Search, Menu, User } from \"lucide-react\"\n\nexport function HomePage() {\n  const [searchQuery, setSearchQuery] = useState(\"\")\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b border-border\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"Tadabbur AI\"\n                width={32}\n                height={32}\n                className=\"rounded\"\n              />\n              <span className=\"text-xl font-semibold\">Tadabbur AI</span>\n            </Link>\n\n            <nav className=\"hidden md:flex items-center space-x-6\">\n              <Link href=\"/features\" className=\"text-sm hover:text-primary\">\n                Features\n              </Link>\n              <Link href=\"/about\" className=\"text-sm hover:text-primary\">\n                About\n              </Link>\n              <Link href=\"/contact\" className=\"text-sm hover:text-primary\">\n                Contact\n              </Link>\n            </nav>\n\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/auth/login\">\n                <Button variant=\"ghost\">Sign In</Button>\n              </Link>\n              <Link href=\"/auth/register\">\n                <Button>Get Started</Button>\n              </Link>\n              <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n                <Menu className=\"h-5 w-5\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-12\">\n        {/* Hero Section */}\n        <div className=\"text-center space-y-6 mb-12\">\n          <h1 className=\"text-4xl md:text-6xl font-bold\">\n            Deep Quranic{\" \"}\n            <span className=\"text-primary\">Contemplation</span>\n          </h1>\n          <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n            Explore the Quran with AI-powered insights. Search verses, generate study notes, \n            and deepen your understanding with intelligent Islamic learning tools.\n          </p>\n        </div>\n\n        {/* Search Section */}\n        <div className=\"max-w-2xl mx-auto mb-12\">\n          <SearchBar \n            value={searchQuery}\n            onChange={setSearchQuery}\n            placeholder=\"Search for verses, topics, or concepts...\"\n          />\n          <SuggestionChips onChipClick={setSearchQuery} />\n        </div>\n\n        {/* Feature Preview */}\n        <div className=\"mb-16\">\n          <h2 className=\"text-2xl font-bold text-center mb-8\">\n            Powerful Features for Islamic Learning\n          </h2>\n          <FeatureCards />\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center space-y-6 bg-muted rounded-lg p-8\">\n          <h2 className=\"text-3xl font-bold\">\n            Start Your Journey Today\n          </h2>\n          <p className=\"text-muted-foreground max-w-md mx-auto\">\n            Join thousands of Muslims exploring the Quran with AI-powered insights\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/auth/register\">\n              <Button size=\"lg\" className=\"w-full sm:w-auto\">\n                Create Free Account\n              </Button>\n            </Link>\n            <Link href=\"/study\">\n              <Button variant=\"outline\" size=\"lg\" className=\"w-full sm:w-auto\">\n                Try Demo\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t border-border mt-16\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div className=\"space-y-4\">\n              <Link href=\"/\" className=\"flex items-center space-x-3\">\n                <Image\n                  src=\"/logo.png\"\n                  alt=\"Tadabbur AI\"\n                  width={24}\n                  height={24}\n                  className=\"rounded\"\n                />\n                <span className=\"font-semibold\">Tadabbur AI</span>\n              </Link>\n              <p className=\"text-sm text-muted-foreground\">\n                Deep Quranic contemplation powered by AI\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <h3 className=\"font-semibold\">Product</h3>\n              <div className=\"space-y-2\">\n                <Link href=\"/features\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  Features\n                </Link>\n                <Link href=\"/pricing\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  Pricing\n                </Link>\n                <Link href=\"/api\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  API\n                </Link>\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <h3 className=\"font-semibold\">Company</h3>\n              <div className=\"space-y-2\">\n                <Link href=\"/about\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  About\n                </Link>\n                <Link href=\"/contact\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  Contact\n                </Link>\n                <Link href=\"/privacy\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  Privacy\n                </Link>\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <h3 className=\"font-semibold\">Support</h3>\n              <div className=\"space-y-2\">\n                <Link href=\"/help\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  Help Center\n                </Link>\n                <Link href=\"/docs\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  Documentation\n                </Link>\n                <Link href=\"/community\" className=\"block text-sm text-muted-foreground hover:text-foreground\">\n                  Community\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"border-t border-border mt-8 pt-8 text-center text-sm text-muted-foreground\">\n            <p>&copy; 2024 Tadabbur AI. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAYO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAA6B;;;;;;kDAG9D,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA6B;;;;;;kDAG3D,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA6B;;;;;;;;;;;;0CAK/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAQ;;;;;;;;;;;kDAE1B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;kDAEV,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,WAAU;kDAC5C,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAiC;oCAChC;kDACb,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEjC,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAOjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8IAAA,CAAA,YAAS;gCACR,OAAO;gCACP,UAAU;gCACV,aAAY;;;;;;0CAEd,6LAAC,oJAAA,CAAA,kBAAe;gCAAC,aAAa;;;;;;;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC,iJAAA,CAAA,eAAY;;;;;;;;;;;kCAIf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CAGnC,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAmB;;;;;;;;;;;kDAIjD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAK/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAA4D;;;;;;8DAG7F,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAA4D;;;;;;8DAG5F,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;8DAA4D;;;;;;;;;;;;;;;;;;8CAM5F,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAA4D;;;;;;8DAG1F,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAA4D;;;;;;8DAG5F,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAA4D;;;;;;;;;;;;;;;;;;8CAMhG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;8DAA4D;;;;;;8DAGzF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;8DAA4D;;;;;;8DAGzF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;8DAA4D;;;;;;;;;;;;;;;;;;;;;;;;sCAOpG,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GA/KgB;KAAA", "debugId": null}}]}