/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
    }
  }
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.relative {
  position: relative;
}

.static {
  position: static;
}

.top-1\/2 {
  top: 50%;
}

.top-full {
  top: 100%;
}

.z-10 {
  z-index: 10;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.container {
  width: 100%;
}

.mx-auto {
  margin-inline: auto;
}

.mr-auto {
  margin-right: auto;
}

.ml-auto {
  margin-left: auto;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.block {
  display: block;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.inline-block {
  display: inline-block;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.min-h-\[60px\] {
  min-height: 60px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-full {
  width: 100%;
}

.max-w-\[80\%\] {
  max-width: 80%;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.-translate-x-full {
  --tw-translate-x: -100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.-translate-y-1\/2 {
  --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.rotate-y-180 {
  --tw-rotate-y: rotateY(180deg);
  transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
}

.transform {
  transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
}

.cursor-pointer {
  cursor: pointer;
}

.resize-none {
  resize: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.flex-col {
  flex-direction: column;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-start {
  align-items: flex-start;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.rounded {
  border-radius: var(--radius);
}

.rounded-full {
  border-radius: 3.40282e38px;
}

.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}

.border-0 {
  border-style: var(--tw-border-style);
  border-width: 0;
}

.border-2 {
  border-style: var(--tw-border-style);
  border-width: 2px;
}

.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}

.border-r {
  border-right-style: var(--tw-border-style);
  border-right-width: 1px;
}

.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}

.border-l {
  border-left-style: var(--tw-border-style);
  border-left-width: 1px;
}

.border-dashed {
  --tw-border-style: dashed;
  border-style: dashed;
}

.border-border {
  border-color: var(--border);
}

.border-destructive\/20 {
  border-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .border-destructive\/20 {
    border-color: color-mix(in oklab, var(--destructive) 20%, transparent);
  }
}

.border-input {
  border-color: var(--input);
}

.border-primary {
  border-color: var(--primary);
}

.border-primary\/20 {
  border-color: var(--primary);
}

@supports (color: color-mix(in lab, red, red)) {
  .border-primary\/20 {
    border-color: color-mix(in oklab, var(--primary) 20%, transparent);
  }
}

.bg-accent {
  background-color: var(--accent);
}

.bg-background {
  background-color: var(--background);
}

.bg-destructive {
  background-color: var(--destructive);
}

.bg-destructive\/10 {
  background-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-destructive\/10 {
    background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
  }
}

.bg-muted {
  background-color: var(--muted);
}

.bg-primary {
  background-color: var(--primary);
}

.bg-primary\/5 {
  background-color: var(--primary);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-primary\/5 {
    background-color: color-mix(in oklab, var(--primary) 5%, transparent);
  }
}

.bg-primary\/10 {
  background-color: var(--primary);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-primary\/10 {
    background-color: color-mix(in oklab, var(--primary) 10%, transparent);
  }
}

.bg-secondary {
  background-color: var(--secondary);
}

.bg-transparent {
  background-color: #0000;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-sans {
  font-family: system-ui, -apple-system, sans-serif;
}

.leading-none {
  --tw-leading: 1;
  line-height: 1;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.text-destructive {
  color: var(--destructive);
}

.text-destructive-foreground {
  color: var(--destructive-foreground);
}

.text-foreground {
  color: var(--foreground);
}

.text-muted-foreground {
  color: var(--muted-foreground);
}

.text-primary {
  color: var(--primary);
}

.text-primary-foreground {
  color: var(--primary-foreground);
}

.text-secondary-foreground {
  color: var(--secondary-foreground);
}

.uppercase {
  text-transform: uppercase;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.opacity-60 {
  opacity: .6;
}

.opacity-80 {
  opacity: .8;
}

.ring-offset-background {
  --tw-ring-offset-color: var(--background);
}

.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}

.filter {
  filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
}

.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.duration-300 {
  --tw-duration: .3s;
  transition-duration: .3s;
}

.duration-500 {
  --tw-duration: .5s;
  transition-duration: .5s;
}

.backface-hidden {
  backface-visibility: hidden;
}

.file\:border-0::file-selector-button {
  border-style: var(--tw-border-style);
  border-width: 0;
}

.file\:bg-transparent::file-selector-button {
  background-color: #0000;
}

.placeholder\:text-muted-foreground::placeholder {
  color: var(--muted-foreground);
}

@media (hover: hover) {
  .hover\:border-primary\/50:hover {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:border-primary\/50:hover {
      border-color: color-mix(in oklab, var(--primary) 50%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-accent:hover {
    background-color: var(--accent);
  }
}

@media (hover: hover) {
  .hover\:bg-destructive\/90:hover {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-destructive\/90:hover {
      background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-primary:hover {
    background-color: var(--primary);
  }
}

@media (hover: hover) {
  .hover\:bg-primary\/90:hover {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-primary\/90:hover {
      background-color: color-mix(in oklab, var(--primary) 90%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-secondary\/80:hover {
    background-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-secondary\/80:hover {
      background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-transparent:hover {
    background-color: #0000;
  }
}

@media (hover: hover) {
  .hover\:text-accent-foreground:hover {
    color: var(--accent-foreground);
  }
}

@media (hover: hover) {
  .hover\:text-foreground:hover {
    color: var(--foreground);
  }
}

@media (hover: hover) {
  .hover\:text-primary:hover {
    color: var(--primary);
  }
}

@media (hover: hover) {
  .hover\:text-primary-foreground:hover {
    color: var(--primary-foreground);
  }
}

@media (hover: hover) {
  .hover\:underline:hover {
    text-decoration-line: underline;
  }
}

.focus-visible\:ring-0:focus-visible {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: var(--ring);
}

.focus-visible\:ring-offset-0:focus-visible {
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
}

.focus-visible\:outline-none:focus-visible {
  --tw-outline-style: none;
  outline-style: none;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: var(--background);
}

.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: var(--foreground);
}

:root {
  --background: #fff;
  --foreground: #171717;
  --primary: #10b981;
  --primary-foreground: #fff;
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #374151;
  --destructive: #ef4444;
  --destructive-foreground: #fff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #10b981;
  --radius: .5rem;
}

:root, :host {
  --font-sans: system-ui, -apple-system, sans-serif;
  --font-arabic: "Amiri", serif;
  --radius: var(--radius);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #10b981;
    --primary-foreground: #fff;
    --secondary: #1f2937;
    --secondary-foreground: #f9fafb;
    --muted: #111827;
    --muted-foreground: #9ca3af;
    --accent: #1f2937;
    --accent-foreground: #f9fafb;
    --destructive: #ef4444;
    --destructive-foreground: #fff;
    --border: #374151;
    --input: #374151;
    --ring: #10b981;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
}

.arabic-text {
  font-family: var(--font-arabic);
  text-align: right;
  direction: rtl;
  line-height: 1.8;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/