# Tadabbur AI - Deep Quranic Contemplation

A modern web application for deep Quranic study powered by AI, built with Next.js 15, TypeScript, and Tailwind CSS.

## Features

- 🔍 **Intelligent Search**: AI-powered Quranic verse search with semantic understanding
- 📝 **Smart Notes**: Generate personalized study notes, summaries, and insights
- 🧠 **Interactive Learning**: Create flashcards, quizzes, and educational games
- 📖 **Contextual Tafsir**: Access multiple tafsir sources with explanations
- 💬 **AI Chat Assistant**: Ask questions and get detailed explanations
- 📱 **OCR & Analysis**: Upload images for verse recognition and analysis
- 🔐 **Secure Authentication**: Better Auth with email/password and Google OAuth
- 📁 **Organization**: Folder-based note management system

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React
- **Deployment**: Vercel-ready

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database (local or cloud)
- Google OAuth credentials (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tadabbur-ai-v2/nextjs-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Fill in your environment variables:
   ```env
   # Database
   DATABASE_URL=your_postgresql_connection_string

   # Better Auth
   BETTER_AUTH_SECRET=your_secret_key_here
   BETTER_AUTH_URL=http://localhost:3000

   # OAuth (optional)
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret

   # App URL
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Set up the database**
   ```bash
   # Generate database schema
   npm run db:generate

   # Push schema to database
   npm run db:push
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Database Setup

### Using Supabase (Recommended)

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > Database
3. Copy the connection string and add it to your `.env.local`
4. Run the database commands above

### Using Local PostgreSQL

1. Install PostgreSQL locally
2. Create a new database
3. Update the `DATABASE_URL` in `.env.local`
4. Run the database commands above

## Authentication Setup

### Google OAuth (Optional)

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)
6. Add the credentials to your `.env.local`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate database migrations
- `npm run db:migrate` - Run database migrations
- `npm run db:push` - Push schema to database
- `npm run db:studio` - Open Drizzle Studio

## Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── api/            # API routes
│   ├── auth/           # Authentication pages
│   ├── study/          # Study interface
│   └── notes/          # Notes management
├── components/         # React components
│   ├── ui/             # Base UI components
│   ├── auth/           # Authentication components
│   ├── chat/           # Chat interface components
│   ├── notes/          # Notes components
│   └── home/           # Landing page components
├── lib/                # Utility libraries
│   ├── auth/           # Authentication configuration
│   ├── db/             # Database schema and connection
│   ├── ai/             # AI services
│   └── actions/        # Server actions
└── types/              # TypeScript type definitions
```

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on push

### Other Platforms

The app can be deployed to any platform that supports Node.js:
- Railway
- Render
- DigitalOcean App Platform
- AWS Amplify
- Netlify (with serverless functions)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue on GitHub or contact the development team.

## Acknowledgments

- Quran text and translations from various Islamic sources
- UI components built with Radix UI
- Icons from Lucide React
- Authentication powered by Better Auth
