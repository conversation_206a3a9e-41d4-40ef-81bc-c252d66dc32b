/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://tadabbur-ai.vercel.app',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: [
    '/api/*',
    '/auth/*',
    '/admin/*',
    '/server-sitemap.xml',
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/auth/', '/admin/'],
      },
    ],
    additionalSitemaps: [
      `${process.env.NEXT_PUBLIC_APP_URL || 'https://tadabbur-ai.vercel.app'}/server-sitemap.xml`,
    ],
  },
  transform: async (config, path) => {
    // Custom priority and changefreq for different pages
    const customConfig = {
      '/': {
        priority: 1.0,
        changefreq: 'daily',
      },
      '/study': {
        priority: 0.9,
        changefreq: 'daily',
      },
      '/notes': {
        priority: 0.8,
        changefreq: 'weekly',
      },
    }

    return {
      loc: path,
      changefreq: customConfig[path]?.changefreq || 'monthly',
      priority: customConfig[path]?.priority || 0.7,
      lastmod: new Date().toISOString(),
    }
  },
}
