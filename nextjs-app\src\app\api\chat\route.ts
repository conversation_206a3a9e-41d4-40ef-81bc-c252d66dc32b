import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"
import { db } from "@/lib/db"
import { verses, tafsirs, translations } from "@/lib/db/schema"
import { like, or, eq } from "drizzle-orm"

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { message, type = "general" } = body

    if (!message || message.trim().length === 0) {
      return NextResponse.json(
        { error: "Message is required" },
        { status: 400 }
      )
    }

    let response = ""
    let relatedVerses = []
    let relatedTafsirs = []

    // Handle different types of queries
    switch (type) {
      case "verse_search":
        // Search for verses based on content or reference
        const searchResults = await searchVerses(message)
        relatedVerses = searchResults.verses
        relatedTafsirs = searchResults.tafsirs
        response = generateVerseSearchResponse(message, searchResults)
        break
      
      case "concept_explanation":
        // Explain Islamic concepts
        response = await explainConcept(message)
        break
      
      case "general":
      default:
        // General AI chat response
        response = await generateGeneralResponse(message)
        // Also search for related verses
        const generalSearchResults = await searchVerses(message)
        relatedVerses = generalSearchResults.verses.slice(0, 3) // Limit to 3 verses
        break
    }

    return NextResponse.json({
      success: true,
      data: {
        response,
        relatedVerses,
        relatedTafsirs,
        timestamp: new Date().toISOString(),
      }
    })
  } catch (error) {
    console.error("Error processing chat message:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

async function searchVerses(query: string) {
  try {
    // Search in verse text and translations
    const verseResults = await db
      .select({
        id: verses.id,
        surahNumber: verses.surahNumber,
        ayahNumber: verses.ayahNumber,
        verseKeys: verses.verseKeys,
        textArabic: verses.textArabic,
        textClean: verses.textClean,
      })
      .from(verses)
      .where(
        or(
          like(verses.textClean, `%${query}%`),
          like(verses.verseKeys, `%${query}%`)
        )
      )
      .limit(5)

    // Search for translations
    const translationResults = await db
      .select({
        verseKeys: translations.verseKeys,
        text: translations.text,
        translator: translations.translator,
        language: translations.language,
      })
      .from(translations)
      .where(
        or(
          like(translations.text, `%${query}%`),
          like(translations.verseKeys, `%${query}%`)
        )
      )
      .limit(5)

    // Search for tafsirs
    const tafsirResults = await db
      .select({
        id: tafsirs.id,
        verseKeys: tafsirs.verseKeys,
        text: tafsirs.text,
        language: tafsirs.language,
      })
      .from(tafsirs)
      .where(
        or(
          like(tafsirs.text, `%${query}%`),
          like(tafsirs.verseKeys, `%${query}%`)
        )
      )
      .limit(3)

    return {
      verses: verseResults,
      translations: translationResults,
      tafsirs: tafsirResults,
    }
  } catch (error) {
    console.error("Error searching verses:", error)
    return { verses: [], translations: [], tafsirs: [] }
  }
}

function generateVerseSearchResponse(query: string, searchResults: any) {
  const { verses, translations, tafsirs } = searchResults
  
  if (verses.length === 0 && translations.length === 0) {
    return `I couldn't find any verses directly matching "${query}". Try searching with different keywords or ask me to explain a specific concept.`
  }

  let response = `I found ${verses.length + translations.length} verses related to "${query}":\n\n`
  
  // Add verse results
  verses.forEach((verse: any, index: number) => {
    response += `${index + 1}. **${verse.verseKeys}**\n${verse.textArabic}\n\n`
  })

  // Add translation results
  translations.forEach((translation: any, index: number) => {
    response += `**Translation (${translation.translator}):** ${translation.text}\n\n`
  })

  if (tafsirs.length > 0) {
    response += `\n**Related Commentary:**\n${tafsirs[0].text.substring(0, 200)}...\n\n`
  }

  response += "Would you like me to explain any of these verses in more detail?"

  return response
}

async function explainConcept(concept: string) {
  // This would integrate with an AI service in a real implementation
  // For now, return a template response
  return `The concept of "${concept}" in Islam is multifaceted and deeply rooted in Quranic teachings. Let me explain this concept and provide relevant verses that discuss it. Would you like me to search for specific verses about ${concept}?`
}

async function generateGeneralResponse(message: string) {
  // This would integrate with an AI service in a real implementation
  // For now, return a helpful template response
  const lowerMessage = message.toLowerCase()
  
  if (lowerMessage.includes("prayer") || lowerMessage.includes("salah")) {
    return "Prayer (Salah) is one of the five pillars of Islam. The Quran mentions prayer in many verses, emphasizing its importance for spiritual connection with Allah. Would you like me to show you verses about prayer times, the importance of prayer, or how to perform prayer?"
  }
  
  if (lowerMessage.includes("patience") || lowerMessage.includes("sabr")) {
    return "Patience (Sabr) is a fundamental virtue in Islam. The Quran frequently mentions patience as a quality of the righteous and a means to earn Allah's reward. Let me find some verses about patience for you."
  }
  
  if (lowerMessage.includes("gratitude") || lowerMessage.includes("thankful")) {
    return "Gratitude (Shukr) is highly emphasized in the Quran. Allah mentions that those who are grateful will be given more blessings. I can show you verses about gratitude and its importance in a believer's life."
  }
  
  return `Thank you for your question about "${message}". I'm here to help you explore the Quran and understand Islamic teachings. I can search for verses, explain concepts, or help you study specific topics. What would you like to learn about?`
}
