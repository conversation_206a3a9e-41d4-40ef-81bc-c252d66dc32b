import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"
import { db } from "@/lib/db"
import { folders, notes } from "@/lib/db/schema"
import { eq, count } from "drizzle-orm"

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get folders with note counts
    const userFolders = await db
      .select({
        id: folders.id,
        name: folders.name,
        color: folders.color,
        createdAt: folders.createdAt,
        updatedAt: folders.updatedAt,
        noteCount: count(notes.id),
      })
      .from(folders)
      .leftJoin(notes, eq(folders.id, notes.folderId))
      .where(eq(folders.userId, session.user.id))
      .groupBy(folders.id)
      .orderBy(folders.createdAt)

    return NextResponse.json({ 
      success: true, 
      data: userFolders 
    })
  } catch (error) {
    console.error("Error fetching folders:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { name, color } = body

    if (!name || name.trim().length === 0) {
      return NextResponse.json(
        { error: "Folder name is required" },
        { status: 400 }
      )
    }

    // Check if folder name already exists for this user
    const existingFolder = await db
      .select()
      .from(folders)
      .where(eq(folders.userId, session.user.id))
      .where(eq(folders.name, name.trim()))
      .limit(1)

    if (existingFolder.length > 0) {
      return NextResponse.json(
        { error: "Folder name already exists" },
        { status: 400 }
      )
    }

    const newFolder = await db.insert(folders).values({
      userId: session.user.id,
      name: name.trim(),
      color: color || "#10b981",
      noteCount: 0,
    }).returning()

    return NextResponse.json({ 
      success: true, 
      data: newFolder[0] 
    }, { status: 201 })
  } catch (error) {
    console.error("Error creating folder:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
