import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"
import { NoteGenerator } from "@/lib/ai/note-generator"

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { query, verseKeys, context, type = "summary", language = "en" } = body

    if (!query && !verseKeys) {
      return NextResponse.json(
        { error: "Either query or verseKeys is required" },
        { status: 400 }
      )
    }

    // Generate notes using AI service
    const generatedNotes = await NoteGenerator.generateNotes({
      query,
      verseKeys,
      context,
      type,
      language,
    })

    return NextResponse.json({
      success: true,
      data: generatedNotes,
    })
  } catch (error) {
    console.error("Error generating notes:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
