import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"
import { db } from "@/lib/db"
import { notes } from "@/lib/db/schema"
import { eq, and } from "drizzle-orm"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const noteId = parseInt(params.id)
    if (isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    const note = await db
      .select()
      .from(notes)
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))
      .limit(1)

    if (note.length === 0) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }

    return NextResponse.json({ 
      success: true, 
      data: note[0] 
    })
  } catch (error) {
    console.error("Error fetching note:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const noteId = parseInt(params.id)
    if (isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    const body = await request.json()
    const { title, shortDescription, content, folderId, language } = body

    // Check if note exists and belongs to user
    const existingNote = await db
      .select()
      .from(notes)
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))
      .limit(1)

    if (existingNote.length === 0) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }

    const updatedNote = await db
      .update(notes)
      .set({
        title: title || existingNote[0].title,
        shortDescription: shortDescription !== undefined ? shortDescription : existingNote[0].shortDescription,
        content: content !== undefined ? content : existingNote[0].content,
        folderId: folderId !== undefined ? folderId : existingNote[0].folderId,
        language: language || existingNote[0].language,
        updatedAt: new Date(),
      })
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))
      .returning()

    return NextResponse.json({ 
      success: true, 
      data: updatedNote[0] 
    })
  } catch (error) {
    console.error("Error updating note:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const noteId = parseInt(params.id)
    if (isNaN(noteId)) {
      return NextResponse.json({ error: "Invalid note ID" }, { status: 400 })
    }

    // Check if note exists and belongs to user
    const existingNote = await db
      .select()
      .from(notes)
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))
      .limit(1)

    if (existingNote.length === 0) {
      return NextResponse.json({ error: "Note not found" }, { status: 404 })
    }

    await db
      .delete(notes)
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))

    return NextResponse.json({ 
      success: true, 
      message: "Note deleted successfully" 
    })
  } catch (error) {
    console.error("Error deleting note:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
