import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"
import { db } from "@/lib/db"
import { notes, folders } from "@/lib/db/schema"
import { eq, and, desc, asc, like, or } from "drizzle-orm"

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const folderId = searchParams.get("folderId")
    const search = searchParams.get("search")
    const sortBy = searchParams.get("sortBy") || "updatedAt"
    const order = searchParams.get("order") || "desc"

    let query = db
      .select({
        id: notes.id,
        verseKeys: notes.verseKeys,
        title: notes.title,
        shortDescription: notes.shortDescription,
        content: notes.content,
        language: notes.language,
        folderId: notes.folderId,
        createdAt: notes.createdAt,
        updatedAt: notes.updatedAt,
        folderName: folders.name,
      })
      .from(notes)
      .leftJoin(folders, eq(notes.folderId, folders.id))
      .where(eq(notes.userId, session.user.id))

    // Apply folder filter
    if (folderId && folderId !== "all") {
      query = query.where(and(
        eq(notes.userId, session.user.id),
        eq(notes.folderId, parseInt(folderId))
      ))
    }

    // Apply search filter
    if (search) {
      query = query.where(and(
        eq(notes.userId, session.user.id),
        or(
          like(notes.title, `%${search}%`),
          like(notes.shortDescription, `%${search}%`),
          like(notes.content, `%${search}%`),
          like(notes.verseKeys, `%${search}%`)
        )
      ))
    }

    // Apply sorting
    if (sortBy === "title") {
      query = order === "desc" 
        ? query.orderBy(desc(notes.title))
        : query.orderBy(asc(notes.title))
    } else {
      query = order === "desc" 
        ? query.orderBy(desc(notes.updatedAt))
        : query.orderBy(asc(notes.updatedAt))
    }

    const userNotes = await query

    return NextResponse.json({ 
      success: true, 
      data: userNotes 
    })
  } catch (error) {
    console.error("Error fetching notes:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { verseKeys, title, shortDescription, content, folderId, language } = body

    if (!verseKeys || !title) {
      return NextResponse.json(
        { error: "Verse keys and title are required" },
        { status: 400 }
      )
    }

    const newNote = await db.insert(notes).values({
      verseKeys,
      userId: session.user.id,
      folderId: folderId || null,
      title,
      shortDescription: shortDescription || null,
      content: content || null,
      language: language || "en",
    }).returning()

    // Update folder note count if folder is specified
    if (folderId) {
      await db
        .update(folders)
        .set({ 
          noteCount: db.select({ count: notes.id }).from(notes).where(eq(notes.folderId, folderId))
        })
        .where(eq(folders.id, folderId))
    }

    return NextResponse.json({ 
      success: true, 
      data: newNote[0] 
    }, { status: 201 })
  } catch (error) {
    console.error("Error creating note:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
