import { NextRequest, NextResponse } from "next/server"
import { VerseSearch } from "@/lib/search/verse-search"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("q")
    const searchType = searchParams.get("type") as "text" | "reference" | "concept" | "all" || "all"
    const language = searchParams.get("language") || "en"
    const includeTranslations = searchParams.get("translations") === "true"
    const includeTafsir = searchParams.get("tafsir") === "true"
    const limit = parseInt(searchParams.get("limit") || "20")

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: "Search query is required" },
        { status: 400 }
      )
    }

    const searchResults = await VerseSearch.search({
      query: query.trim(),
      searchType,
      language,
      includeTranslations,
      includeTafsir,
      limit,
    })

    return NextResponse.json({
      success: true,
      data: searchResults,
    })
  } catch (error) {
    console.error("Error in search API:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      query,
      searchType = "all",
      language = "en",
      includeTranslations = true,
      includeTafsir = false,
      limit = 20,
      filters = {}
    } = body

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: "Search query is required" },
        { status: 400 }
      )
    }

    // Advanced search with filters
    const searchResults = await VerseSearch.search({
      query: query.trim(),
      searchType,
      language,
      includeTranslations,
      includeTafsir,
      limit,
    })

    // Apply additional filters if provided
    let filteredResults = searchResults.verses

    if (filters.surahNumber) {
      filteredResults = filteredResults.filter(
        verse => verse.surahNumber === filters.surahNumber
      )
    }

    if (filters.minRelevanceScore) {
      filteredResults = filteredResults.filter(
        verse => (verse.relevanceScore || 0) >= filters.minRelevanceScore
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        ...searchResults,
        verses: filteredResults,
        totalCount: filteredResults.length,
      },
    })
  } catch (error) {
    console.error("Error in advanced search API:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
