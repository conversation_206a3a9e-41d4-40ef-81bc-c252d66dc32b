import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"
import { db } from "@/lib/db"
import { uploads, verses } from "@/lib/db/schema"
import { like, or } from "drizzle-orm"

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get("file") as File

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/webp", "application/pdf"]
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: "Invalid file type" }, { status: 400 })
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: "File too large" }, { status: 400 })
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // In a real implementation, you would:
    // 1. Upload file to cloud storage (AWS S3, Cloudinary, etc.)
    // 2. Use OCR service (Google Vision API, AWS Textract, etc.)
    // 3. Process the extracted text to detect Quranic verses

    // For demo purposes, we'll simulate OCR and verse detection
    const mockOcrText = simulateOCR(file.name, file.type)
    const detectedVerses = await detectVerses(mockOcrText)
    
    // Save upload record to database
    const uploadRecord = await db.insert(uploads).values({
      userId: session.user.id,
      attachmentUrl: `uploads/${Date.now()}-${file.name}`, // Mock URL
      ocrText: mockOcrText,
      detectedVersesKeys: detectedVerses,
    }).returning()

    return NextResponse.json({
      success: true,
      data: {
        id: uploadRecord[0].id.toString(),
        url: uploadRecord[0].attachmentUrl,
        ocrText: mockOcrText,
        detectedVerses: detectedVerses,
        confidence: 0.95, // Mock confidence score
      }
    })
  } catch (error) {
    console.error("Error processing upload:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

function simulateOCR(fileName: string, fileType: string): string {
  // Simulate OCR results based on file name or type
  const mockTexts = [
    "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ\nالْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ\nالرَّحْمَٰنِ الرَّحِيمِ\nمَالِكِ يَوْمِ الدِّينِ",
    "اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ ۚ لَّهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ",
    "قُلْ هُوَ اللَّهُ أَحَدٌ\nاللَّهُ الصَّمَدُ\nلَمْ يَلِدْ وَلَمْ يُولَدْ\nوَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ"
  ]
  
  return mockTexts[Math.floor(Math.random() * mockTexts.length)]
}

async function detectVerses(ocrText: string): Promise<string[]> {
  try {
    // In a real implementation, you would use NLP/ML to match text with verses
    // For demo, we'll do simple text matching
    
    const detectedVerses: string[] = []
    
    // Check for common verse patterns
    if (ocrText.includes("بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ")) {
      detectedVerses.push("1:1")
    }
    
    if (ocrText.includes("الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ")) {
      detectedVerses.push("1:2")
    }
    
    if (ocrText.includes("اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ")) {
      detectedVerses.push("2:255")
    }
    
    if (ocrText.includes("قُلْ هُوَ اللَّهُ أَحَدٌ")) {
      detectedVerses.push("112:1")
    }
    
    // Try to find more verses by searching in database
    const searchTerms = ocrText.split(/\s+/).filter(term => term.length > 3)
    
    for (const term of searchTerms.slice(0, 5)) { // Limit search terms
      try {
        const matchingVerses = await db
          .select({ verseKeys: verses.verseKeys })
          .from(verses)
          .where(
            or(
              like(verses.textArabic, `%${term}%`),
              like(verses.textClean, `%${term}%`)
            )
          )
          .limit(3)
        
        matchingVerses.forEach(verse => {
          if (!detectedVerses.includes(verse.verseKeys)) {
            detectedVerses.push(verse.verseKeys)
          }
        })
      } catch (error) {
        console.error("Error searching verses:", error)
      }
    }
    
    return detectedVerses.slice(0, 10) // Limit to 10 detected verses
  } catch (error) {
    console.error("Error detecting verses:", error)
    return []
  }
}
