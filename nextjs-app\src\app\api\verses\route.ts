import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { verses, translations, tafsirs } from "@/lib/db/schema"
import { eq, like, or, and } from "drizzle-orm"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("q")
    const verseKey = searchParams.get("verseKey")
    const surah = searchParams.get("surah")
    const ayah = searchParams.get("ayah")
    const limit = parseInt(searchParams.get("limit") || "10")

    let verseQuery = db.select().from(verses)

    // Search by specific verse key (e.g., "2:255")
    if (verseKey) {
      verseQuery = verseQuery.where(eq(verses.verseKeys, verseKey))
    }
    // Search by surah and ayah numbers
    else if (surah && ayah) {
      verseQuery = verseQuery.where(
        and(
          eq(verses.surahNumber, parseInt(surah)),
          eq(verses.ayahNumber, parseInt(ayah))
        )
      )
    }
    // Search by surah number only
    else if (surah) {
      verseQuery = verseQuery.where(eq(verses.surahNumber, parseInt(surah)))
    }
    // Text search
    else if (query) {
      verseQuery = verseQuery.where(
        or(
          like(verses.textArabic, `%${query}%`),
          like(verses.textClean, `%${query}%`),
          like(verses.verseKeys, `%${query}%`)
        )
      )
    }

    const verseResults = await verseQuery.limit(limit)

    // Get translations for the found verses
    const verseKeys = verseResults.map(v => v.verseKeys)
    let translationResults = []
    let tafsirResults = []

    if (verseKeys.length > 0) {
      // Get translations
      translationResults = await db
        .select()
        .from(translations)
        .where(
          or(...verseKeys.map(key => eq(translations.verseKeys, key)))
        )

      // Get tafsirs
      tafsirResults = await db
        .select()
        .from(tafsirs)
        .where(
          or(...verseKeys.map(key => eq(tafsirs.verseKeys, key)))
        )
    }

    // Combine results
    const combinedResults = verseResults.map(verse => ({
      ...verse,
      translations: translationResults.filter(t => t.verseKeys === verse.verseKeys),
      tafsirs: tafsirResults.filter(t => t.verseKeys === verse.verseKeys),
    }))

    return NextResponse.json({
      success: true,
      data: {
        verses: combinedResults,
        total: combinedResults.length,
        query: query || verseKey || `${surah}:${ayah}`,
      }
    })
  } catch (error) {
    console.error("Error searching verses:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Get random verses for inspiration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type = "random", count = 1 } = body

    let verseQuery = db.select().from(verses)

    if (type === "popular") {
      // Return some well-known verses
      const popularVerses = ["2:255", "1:1", "112:1", "2:286", "17:78"]
      verseQuery = verseQuery.where(
        or(...popularVerses.map(key => eq(verses.verseKeys, key)))
      )
    } else {
      // Random verses - in a real implementation, you'd use a proper random selection
      verseQuery = verseQuery.limit(count * 2) // Get more to filter from
    }

    const verseResults = await verseQuery.limit(count)

    // Get translations for the verses
    const verseKeys = verseResults.map(v => v.verseKeys)
    let translationResults = []

    if (verseKeys.length > 0) {
      translationResults = await db
        .select()
        .from(translations)
        .where(
          or(...verseKeys.map(key => eq(translations.verseKeys, key)))
        )
    }

    // Combine results
    const combinedResults = verseResults.map(verse => ({
      ...verse,
      translations: translationResults.filter(t => t.verseKeys === verse.verseKeys),
    }))

    return NextResponse.json({
      success: true,
      data: {
        verses: combinedResults,
        type,
        count: combinedResults.length,
      }
    })
  } catch (error) {
    console.error("Error fetching verses:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
