@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #10b981;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #374151;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #10b981;
  --radius: 0.5rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: system-ui, -apple-system, sans-serif;
  --font-arabic: 'Amiri', serif;
  --radius: var(--radius);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #10b981;
    --primary-foreground: #ffffff;
    --secondary: #1f2937;
    --secondary-foreground: #f9fafb;
    --muted: #111827;
    --muted-foreground: #9ca3af;
    --accent: #1f2937;
    --accent-foreground: #f9fafb;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #374151;
    --input: #374151;
    --ring: #10b981;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
}

.arabic-text {
  font-family: var(--font-arabic);
  direction: rtl;
  text-align: right;
  line-height: 1.8;
}


