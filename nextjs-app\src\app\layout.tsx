import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: {
    default: "Tadabbur AI - Deep Quranic Contemplation",
    template: "%s | Tadabbur AI"
  },
  description: "Deep Quranic contemplation powered by AI. Explore verses, generate study notes, and deepen your understanding of the Quran with intelligent insights.",
  keywords: ["Quran", "Islam", "AI", "Study", "Tafsir", "Arabic", "Islamic Studies"],
  authors: [{ name: "Tadabbur AI Team" }],
  creator: "Tadabbur AI",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://tadabbur-ai.com",
    title: "Tadabbur AI - Deep Quranic Contemplation",
    description: "Deep Quranic contemplation powered by AI",
    siteName: "Tadabbur AI",
  },
  twitter: {
    card: "summary_large_image",
    title: "Tadabbur AI - Deep Quranic Contemplation",
    description: "Deep Quranic contemplation powered by AI",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/logo.png" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
