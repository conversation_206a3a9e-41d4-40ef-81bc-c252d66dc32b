import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search, StickyNote, Brain, BookOpen } from "lucide-react"

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  showSocialLogin?: boolean
  footerText?: string
  footerLinkText?: string
  footerLinkHref?: string
}

export function AuthLayout({
  children,
  title,
  subtitle,
  showSocialLogin = false,
  footerText,
  footerLinkText,
  footerLinkHref,
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center space-x-3">
            <Image
              src="/logo.png"
              alt="Tadabbur AI"
              width={32}
              height={32}
              className="rounded"
            />
            <span className="text-xl font-semibold"><PERSON><PERSON><PERSON> AI</span>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Auth Form */}
          <div className="flex items-center justify-center">
            <div className="w-full max-w-md space-y-6">
              {/* Form Header */}
              <div className="text-center space-y-2">
                <h1 className="text-2xl font-bold">{title}</h1>
                <p className="text-muted-foreground">{subtitle}</p>
              </div>

              {/* Form */}
              {children}

              {/* Social Login */}
              {showSocialLogin && (
                <>
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-border" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-2 text-muted-foreground">or</span>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full" disabled>
                    <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                      <path
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                        fill="#4285F4"
                      />
                      <path
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                        fill="#34A853"
                      />
                      <path
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                        fill="#FBBC05"
                      />
                      <path
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                        fill="#EA4335"
                      />
                    </svg>
                    Continue with Google
                  </Button>
                </>
              )}

              {/* Footer */}
              {footerText && footerLinkText && footerLinkHref && (
                <div className="text-center text-sm">
                  <span className="text-muted-foreground">{footerText} </span>
                  <Link href={footerLinkHref} className="text-primary hover:underline">
                    {footerLinkText}
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Side Info */}
          <div className="hidden lg:flex items-center justify-center">
            <div className="max-w-md space-y-6">
              <h2 className="text-2xl font-bold">Deepen Your Understanding</h2>
              <p className="text-muted-foreground">
                Join thousands of Muslims exploring the Quran with AI-powered insights,
                personalized study notes, and interactive learning tools.
              </p>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Search className="h-5 w-5 text-primary" />
                  <span className="text-sm">Intelligent Quranic search</span>
                </div>
                <div className="flex items-center space-x-3">
                  <StickyNote className="h-5 w-5 text-primary" />
                  <span className="text-sm">Personalized study notes</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Brain className="h-5 w-5 text-primary" />
                  <span className="text-sm">Interactive learning tools</span>
                </div>
                <div className="flex items-center space-x-3">
                  <BookOpen className="h-5 w-5 text-primary" />
                  <span className="text-sm">Contextual explanations</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
