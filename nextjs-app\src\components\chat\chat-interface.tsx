"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ChatMessage } from "@/components/chat/chat-message"
import { VerseCard } from "@/components/chat/verse-card"
import { Send, Mic, Upload, Loader2 } from "lucide-react"
import { ChatMessage as ChatMessageType } from "@/types"

interface ChatInterfaceProps {
  onQueryChange: (query: string) => void
  onVerseSelect: (verseKey: string) => void
  currentQuery: string
}

export function ChatInterface({ onQueryChange, onVerseSelect, currentQuery }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessageType[]>([
    {
      id: "1",
      role: "assistant",
      content: "Welcome to Tadabbur AI! I'm here to help you explore and understand the Quran. You can ask me about verses, concepts, or upload images for analysis. How can I assist you today?",
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Mock verses data - in real app this would come from API
  const mockVerses = [
    {
      id: "2:255",
      arabic: "اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ",
      translation: "Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence. Neither drowsiness overtakes Him nor sleep.",
      reference: "Al-Baqarah 2:255"
    }
  ]

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    onQueryChange(inputValue)
  }, [inputValue, onQueryChange])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: ChatMessageType = {
      id: Date.now().toString(),
      role: "user",
      content: inputValue,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: ChatMessageType = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: `I understand you're asking about "${userMessage.content}". Let me help you explore this topic with relevant verses and insights.`,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, aiResponse])
      setIsLoading(false)
    }, 1500)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleVoiceInput = () => {
    setIsListening(!isListening)
    // Voice input functionality would be implemented here
  }

  const handleFileUpload = () => {
    // File upload functionality would be implemented here
    console.log("File upload clicked")
  }

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="p-4 border-b border-border">
        <h1 className="text-xl font-semibold">Quranic Study Assistant</h1>
        <p className="text-sm text-muted-foreground">
          Ask questions, search verses, or upload images for analysis
        </p>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}

        {/* Sample Verses */}
        {messages.length <= 2 && (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground text-center">
              Here are some verses to get you started:
            </p>
            {mockVerses.map((verse) => (
              <VerseCard
                key={verse.id}
                verse={verse}
                onSelect={() => onVerseSelect(verse.id)}
              />
            ))}
          </div>
        )}

        {isLoading && (
          <div className="flex items-center space-x-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">AI is thinking...</span>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-border">
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask about verses, concepts, or upload an image..."
              className="min-h-[60px] max-h-32 resize-none"
              rows={2}
            />
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleVoiceInput}
              className={isListening ? "text-primary" : "text-muted-foreground"}
              title="Voice input"
            >
              <Mic className="h-4 w-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={handleFileUpload}
              className="text-muted-foreground"
              title="Upload image"
            >
              <Upload className="h-4 w-4" />
            </Button>
            
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              size="icon"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <p className="text-xs text-muted-foreground mt-2">
          Press Enter to send, Shift+Enter for new line
        </p>
      </div>
    </div>
  )
}
