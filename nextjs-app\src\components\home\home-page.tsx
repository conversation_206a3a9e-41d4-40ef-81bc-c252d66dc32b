"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SearchBar } from "@/components/home/<USER>"
import { SuggestionChips } from "@/components/home/<USER>"
import { FeatureCards } from "@/components/home/<USER>"
import { Search, Menu, User } from "lucide-react"

export function HomePage() {
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/logo.png"
                alt="Tadabbur AI"
                width={32}
                height={32}
                className="rounded"
              />
              <span className="text-xl font-semibold">Tadabbur AI</span>
            </Link>

            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/features" className="text-sm hover:text-primary">
                Features
              </Link>
              <Link href="/about" className="text-sm hover:text-primary">
                About
              </Link>
              <Link href="/contact" className="text-sm hover:text-primary">
                Contact
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link href="/auth/login">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link href="/auth/register">
                <Button>Get Started</Button>
              </Link>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center space-y-6 mb-12">
          <h1 className="text-4xl md:text-6xl font-bold">
            Deep Quranic{" "}
            <span className="text-primary">Contemplation</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Explore the Quran with AI-powered insights. Search verses, generate study notes, 
            and deepen your understanding with intelligent Islamic learning tools.
          </p>
        </div>

        {/* Search Section */}
        <div className="max-w-2xl mx-auto mb-12">
          <SearchBar 
            value={searchQuery}
            onChange={setSearchQuery}
            placeholder="Search for verses, topics, or concepts..."
          />
          <SuggestionChips onChipClick={setSearchQuery} />
        </div>

        {/* Feature Preview */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-center mb-8">
            Powerful Features for Islamic Learning
          </h2>
          <FeatureCards />
        </div>

        {/* CTA Section */}
        <div className="text-center space-y-6 bg-muted rounded-lg p-8">
          <h2 className="text-3xl font-bold">
            Start Your Journey Today
          </h2>
          <p className="text-muted-foreground max-w-md mx-auto">
            Join thousands of Muslims exploring the Quran with AI-powered insights
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="w-full sm:w-auto">
                Create Free Account
              </Button>
            </Link>
            <Link href="/study">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                Try Demo
              </Button>
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-border mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <Link href="/" className="flex items-center space-x-3">
                <Image
                  src="/logo.png"
                  alt="Tadabbur AI"
                  width={24}
                  height={24}
                  className="rounded"
                />
                <span className="font-semibold">Tadabbur AI</span>
              </Link>
              <p className="text-sm text-muted-foreground">
                Deep Quranic contemplation powered by AI
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">Product</h3>
              <div className="space-y-2">
                <Link href="/features" className="block text-sm text-muted-foreground hover:text-foreground">
                  Features
                </Link>
                <Link href="/pricing" className="block text-sm text-muted-foreground hover:text-foreground">
                  Pricing
                </Link>
                <Link href="/api" className="block text-sm text-muted-foreground hover:text-foreground">
                  API
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">Company</h3>
              <div className="space-y-2">
                <Link href="/about" className="block text-sm text-muted-foreground hover:text-foreground">
                  About
                </Link>
                <Link href="/contact" className="block text-sm text-muted-foreground hover:text-foreground">
                  Contact
                </Link>
                <Link href="/privacy" className="block text-sm text-muted-foreground hover:text-foreground">
                  Privacy
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">Support</h3>
              <div className="space-y-2">
                <Link href="/help" className="block text-sm text-muted-foreground hover:text-foreground">
                  Help Center
                </Link>
                <Link href="/docs" className="block text-sm text-muted-foreground hover:text-foreground">
                  Documentation
                </Link>
                <Link href="/community" className="block text-sm text-muted-foreground hover:text-foreground">
                  Community
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t border-border mt-8 pt-8 text-center text-sm text-muted-foreground">
            <p>&copy; 2024 Tadabbur AI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
