"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Mic, Upload } from "lucide-react"

interface SearchBarProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

export function SearchBar({ value, onChange, placeholder = "Search..." }: SearchBarProps) {
  const [isListening, setIsListening] = useState(false)
  const router = useRouter()

  const handleSearch = () => {
    if (value.trim()) {
      router.push(`/study?q=${encodeURIComponent(value.trim())}`)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch()
    }
  }

  const handleVoiceSearch = () => {
    // Voice search functionality would be implemented here
    setIsListening(!isListening)
    // For now, just toggle the state
  }

  const handleFileUpload = () => {
    // File upload functionality would be implemented here
    console.log("File upload clicked")
  }

  return (
    <div className="relative">
      <div className="flex items-center space-x-2 p-2 border border-border rounded-lg bg-background shadow-sm">
        <Search className="h-5 w-5 text-muted-foreground ml-2" />
        <Input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          className="flex-1 border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
        />
        
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleVoiceSearch}
            className={`h-8 w-8 ${isListening ? "text-primary" : "text-muted-foreground"}`}
            title="Voice search"
          >
            <Mic className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={handleFileUpload}
            className="h-8 w-8 text-muted-foreground"
            title="Upload image"
          >
            <Upload className="h-4 w-4" />
          </Button>
          
          <Button
            onClick={handleSearch}
            size="sm"
            disabled={!value.trim()}
          >
            Search
          </Button>
        </div>
      </div>
      
      {/* Search suggestions could be added here */}
      {value && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-background border border-border rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
          {/* Placeholder for search suggestions */}
          <div className="p-2 text-sm text-muted-foreground">
            Press Enter to search for "{value}"
          </div>
        </div>
      )}
    </div>
  )
}
