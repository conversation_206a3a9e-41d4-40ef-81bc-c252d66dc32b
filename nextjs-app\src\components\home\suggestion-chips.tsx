"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

interface SuggestionChipsProps {
  onChipClick: (suggestion: string) => void
}

const suggestions = [
  "Prayer times",
  "Patience in Quran",
  "Stories of prophets",
  "Gratitude verses",
  "Forgiveness",
  "Charity and giving",
  "Faith and belief",
  "Paradise description"
]

export function SuggestionChips({ onChipClick }: SuggestionChipsProps) {
  return (
    <div className="mt-4">
      <p className="text-sm text-muted-foreground mb-3 text-center">
        Popular searches:
      </p>
      <div className="flex flex-wrap justify-center gap-2">
        {suggestions.map((suggestion) => (
          <Button
            key={suggestion}
            variant="outline"
            size="sm"
            onClick={() => onChipClick(suggestion)}
            className="text-xs hover:bg-primary hover:text-primary-foreground transition-colors"
          >
            {suggestion}
          </Button>
        ))}
      </div>
    </div>
  )
}
