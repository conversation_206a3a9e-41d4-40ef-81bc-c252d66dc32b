"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { RotateCcw, Plus, Shuffle, Eye, EyeOff } from "lucide-react"

interface FlashcardsTabProps {
  selectedVerse?: string | null
  currentQuery?: string
}

interface Flashcard {
  id: string
  question: string
  answer: string
}

export function FlashcardsTab({ selectedVerse, currentQuery }: FlashcardsTabProps) {
  const [flashcards, setFlashcards] = useState<Flashcard[]>([
    {
      id: "1",
      question: "What is the meaning of 'Tadabbur' in Arabic?",
      answer: "Deep contemplation and reflection, especially in the context of understanding the Quran"
    },
    {
      id: "2",
      question: "Which verse is known as Ayat al-Kursi?",
      answer: "Al-Baqarah 2:255 - the Throne Verse"
    }
  ])
  
  const [currentCardIndex, setCurrentCardIndex] = useState(0)
  const [isFlipped, setIsFlipped] = useState(false)
  const [studyMode, setStudyMode] = useState<'sequential' | 'random'>('sequential')

  const currentCard = flashcards[currentCardIndex]
  const hasCards = flashcards.length > 0

  const nextCard = () => {
    if (studyMode === 'random') {
      setCurrentCardIndex(Math.floor(Math.random() * flashcards.length))
    } else {
      setCurrentCardIndex((prev) => (prev + 1) % flashcards.length)
    }
    setIsFlipped(false)
  }

  const prevCard = () => {
    setCurrentCardIndex((prev) => (prev - 1 + flashcards.length) % flashcards.length)
    setIsFlipped(false)
  }

  const flipCard = () => {
    setIsFlipped(!isFlipped)
  }

  const generateFlashcards = () => {
    // In real app, this would call AI to generate flashcards
    const newCards: Flashcard[] = [
      {
        id: Date.now().toString(),
        question: `What is the main theme of "${currentQuery || selectedVerse}"?`,
        answer: "This would be an AI-generated answer based on the query or verse"
      }
    ]
    setFlashcards(prev => [...prev, ...newCards])
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Flashcards</h3>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setStudyMode(studyMode === 'sequential' ? 'random' : 'sequential')}
          >
            <Shuffle className="h-3 w-3 mr-1" />
            {studyMode === 'sequential' ? 'Sequential' : 'Random'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={generateFlashcards}
          >
            <Plus className="h-3 w-3 mr-1" />
            Generate
          </Button>
        </div>
      </div>

      {/* Flashcard Display */}
      {hasCards ? (
        <div className="flex-1 flex flex-col">
          {/* Card Counter */}
          <div className="text-center mb-4">
            <span className="text-sm text-muted-foreground">
              Card {currentCardIndex + 1} of {flashcards.length}
            </span>
          </div>

          {/* Flashcard */}
          <div className="flex-1 flex items-center justify-center">
            <div 
              className="w-full max-w-md h-64 cursor-pointer perspective-1000"
              onClick={flipCard}
            >
              <div className={`relative w-full h-full transition-transform duration-500 transform-style-preserve-3d ${
                isFlipped ? 'rotate-y-180' : ''
              }`}>
                {/* Front of card (Question) */}
                <Card className="absolute inset-0 backface-hidden p-6 flex items-center justify-center border-2 border-primary/20">
                  <div className="text-center space-y-4">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                      <span className="text-primary font-semibold">Q</span>
                    </div>
                    <p className="text-sm font-medium">{currentCard.question}</p>
                    <p className="text-xs text-muted-foreground">Click to reveal answer</p>
                  </div>
                </Card>

                {/* Back of card (Answer) */}
                <Card className="absolute inset-0 backface-hidden rotate-y-180 p-6 flex items-center justify-center border-2 border-green-200 bg-green-50">
                  <div className="text-center space-y-4">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                      <span className="text-green-600 font-semibold">A</span>
                    </div>
                    <p className="text-sm">{currentCard.answer}</p>
                    <p className="text-xs text-muted-foreground">Click to see question</p>
                  </div>
                </Card>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between mt-4">
            <Button
              variant="outline"
              onClick={prevCard}
              disabled={flashcards.length <= 1}
            >
              Previous
            </Button>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={flipCard}
              >
                {isFlipped ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setCurrentCardIndex(0)
                  setIsFlipped(false)
                }}
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="outline"
              onClick={nextCard}
              disabled={flashcards.length <= 1}
            >
              Next
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-4 max-w-sm">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
              <RotateCcw className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">No flashcards yet</h4>
              <p className="text-sm text-muted-foreground">
                Generate flashcards from your study content to test your knowledge
              </p>
            </div>
            <Button onClick={generateFlashcards}>
              <Plus className="h-4 w-4 mr-2" />
              Create Flashcards
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
