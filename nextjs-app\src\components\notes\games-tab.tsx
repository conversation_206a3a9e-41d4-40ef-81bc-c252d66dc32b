"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Gamepad2, Target, Shuffle, Trophy, Play } from "lucide-react"

interface GamesTabProps {
  selectedVerse?: string | null
  currentQuery?: string
}

export function GamesTab({ selectedVerse, currentQuery }: GamesTabProps) {
  const [selectedGame, setSelectedGame] = useState<string | null>(null)
  const [gameScore, setGameScore] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)

  const games = [
    {
      id: "word-match",
      title: "Word Matching",
      description: "Match Arabic words with their English translations",
      icon: Target,
      difficulty: "Easy"
    },
    {
      id: "verse-complete",
      title: "Complete the Verse",
      description: "Fill in the missing words from Quranic verses",
      icon: Shuffle,
      difficulty: "Medium"
    },
    {
      id: "memory-game",
      title: "Memory Challenge",
      description: "Memorize and recall verse sequences",
      icon: Trophy,
      difficulty: "Hard"
    }
  ]

  const startGame = (gameId: string) => {
    setSelectedGame(gameId)
    setIsPlaying(true)
    setGameScore(0)
  }

  const endGame = () => {
    setIsPlaying(false)
    setSelectedGame(null)
  }

  if (selectedGame && isPlaying) {
    return (
      <div className="h-full flex flex-col space-y-4">
        {/* Game Header */}
        <div className="flex items-center justify-between">
          <h3 className="font-medium">
            {games.find(g => g.id === selectedGame)?.title}
          </h3>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">Score: {gameScore}</span>
            <Button variant="outline" size="sm" onClick={endGame}>
              End Game
            </Button>
          </div>
        </div>

        {/* Game Content */}
        <Card className="flex-1 p-6">
          {selectedGame === "word-match" && <WordMatchGame onScoreUpdate={setGameScore} />}
          {selectedGame === "verse-complete" && <VerseCompleteGame onScoreUpdate={setGameScore} />}
          {selectedGame === "memory-game" && <MemoryGame onScoreUpdate={setGameScore} />}
        </Card>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Learning Games</h3>
        <span className="text-sm text-muted-foreground">Choose a game to start</span>
      </div>

      {/* Games Grid */}
      <div className="flex-1 space-y-4">
        {games.map((game) => {
          const IconComponent = game.icon
          return (
            <Card key={game.id} className="p-4 hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <IconComponent className="h-6 w-6 text-primary" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="font-medium">{game.title}</h4>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      game.difficulty === "Easy" ? "bg-green-100 text-green-700" :
                      game.difficulty === "Medium" ? "bg-yellow-100 text-yellow-700" :
                      "bg-red-100 text-red-700"
                    }`}>
                      {game.difficulty}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">{game.description}</p>
                </div>
                <Button onClick={() => startGame(game.id)}>
                  <Play className="h-4 w-4 mr-2" />
                  Play
                </Button>
              </div>
            </Card>
          )
        })}
      </div>

      {/* Tips */}
      <div className="p-3 bg-muted rounded-lg">
        <p className="text-xs text-muted-foreground">
          🎮 <strong>Tip:</strong> Games are generated based on your study content. 
          The more you study, the more personalized the games become!
        </p>
      </div>
    </div>
  )
}

// Simple Word Match Game Component
function WordMatchGame({ onScoreUpdate }: { onScoreUpdate: (score: number) => void }) {
  const [matches, setMatches] = useState(0)
  const [currentPair, setCurrentPair] = useState({ arabic: "سلام", english: "Peace" })

  const handleMatch = () => {
    const newScore = matches + 1
    setMatches(newScore)
    onScoreUpdate(newScore * 10)
    // Generate new pair (in real app, this would be dynamic)
    setCurrentPair({ arabic: "رحمة", english: "Mercy" })
  }

  return (
    <div className="text-center space-y-6">
      <h4 className="text-lg font-medium">Match the words</h4>
      <div className="space-y-4">
        <div className="text-2xl arabic-text">{currentPair.arabic}</div>
        <div className="space-y-2">
          <Button variant="outline" onClick={handleMatch} className="w-full">
            {currentPair.english}
          </Button>
          <Button variant="outline" className="w-full">
            Love
          </Button>
          <Button variant="outline" className="w-full">
            Hope
          </Button>
        </div>
      </div>
      <p className="text-sm text-muted-foreground">Matches: {matches}</p>
    </div>
  )
}

// Simple Verse Complete Game Component
function VerseCompleteGame({ onScoreUpdate }: { onScoreUpdate: (score: number) => void }) {
  const [answer, setAnswer] = useState("")
  const [correct, setCorrect] = useState(0)

  const checkAnswer = () => {
    if (answer.toLowerCase().includes("allah")) {
      const newScore = correct + 1
      setCorrect(newScore)
      onScoreUpdate(newScore * 15)
      setAnswer("")
    }
  }

  return (
    <div className="space-y-6">
      <h4 className="text-lg font-medium text-center">Complete the verse</h4>
      <div className="text-center space-y-4">
        <p className="arabic-text text-xl">بِسْمِ _____ الرَّحْمَٰنِ الرَّحِيمِ</p>
        <Input
          value={answer}
          onChange={(e) => setAnswer(e.target.value)}
          placeholder="Fill in the missing word"
          className="text-center"
        />
        <Button onClick={checkAnswer}>Submit</Button>
      </div>
      <p className="text-sm text-muted-foreground text-center">Correct answers: {correct}</p>
    </div>
  )
}

// Simple Memory Game Component
function MemoryGame({ onScoreUpdate }: { onScoreUpdate: (score: number) => void }) {
  const [level, setLevel] = useState(1)
  const [sequence, setSequence] = useState(["الله", "الرحمن"])

  return (
    <div className="text-center space-y-6">
      <h4 className="text-lg font-medium">Memorize the sequence</h4>
      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">Level {level}</p>
        <div className="arabic-text text-xl space-x-2">
          {sequence.map((word, index) => (
            <span key={index} className="inline-block mx-2">{word}</span>
          ))}
        </div>
        <Button onClick={() => {
          setLevel(level + 1)
          onScoreUpdate(level * 20)
        }}>
          Next Level
        </Button>
      </div>
    </div>
  )
}
