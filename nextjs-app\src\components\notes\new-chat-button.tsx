"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MessageSquare, Send, Sparkles } from "lucide-react"

export function NewChatButton() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [chatQuery, setChatQuery] = useState("")
  const router = useRouter()

  const handleStartChat = () => {
    if (chatQuery.trim()) {
      // Navigate to study page with the query
      router.push(`/study?q=${encodeURIComponent(chatQuery.trim())}`)
    } else {
      // Navigate to study page without query
      router.push('/study')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleStartChat()
    }
  }

  const quickPrompts = [
    "Explain the concept of patience in the Quran",
    "What does the Quran say about gratitude?",
    "Stories of Prophet Ibrahim",
    "Verses about prayer and worship"
  ]

  if (!isExpanded) {
    return (
      <div className="space-y-3">
        <Button 
          onClick={() => setIsExpanded(true)}
          className="w-full justify-start h-12 text-left"
          size="lg"
        >
          <MessageSquare className="h-5 w-5 mr-3" />
          <div className="flex-1">
            <div className="font-medium">Start a new chat</div>
            <div className="text-xs opacity-80">Ask questions about the Quran</div>
          </div>
          <Sparkles className="h-4 w-4 opacity-60" />
        </Button>
        
        <p className="text-xs text-muted-foreground text-center">
          Or click on any note above to continue studying
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Chat Input */}
      <div className="flex items-center space-x-2">
        <div className="flex-1 relative">
          <Input
            type="text"
            value={chatQuery}
            onChange={(e) => setChatQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about verses, concepts, or upload an image..."
            className="pr-10"
            autoFocus
          />
          <Button
            variant="ghost"
            size="icon"
            onClick={handleStartChat}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        <Button
          variant="outline"
          onClick={() => {
            setIsExpanded(false)
            setChatQuery("")
          }}
        >
          Cancel
        </Button>
      </div>

      {/* Quick Prompts */}
      <div className="space-y-2">
        <p className="text-xs text-muted-foreground">Quick prompts:</p>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {quickPrompts.map((prompt, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={() => setChatQuery(prompt)}
              className="text-left justify-start h-auto p-2 text-xs"
            >
              {prompt}
            </Button>
          ))}
        </div>
      </div>

      {/* Tips */}
      <div className="p-3 bg-muted rounded-lg">
        <p className="text-xs text-muted-foreground">
          💡 <strong>Tip:</strong> You can ask about specific verses, upload images for analysis, 
          or explore topics like prayer, patience, or stories of the prophets.
        </p>
      </div>
    </div>
  )
}
