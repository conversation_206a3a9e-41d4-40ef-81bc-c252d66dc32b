"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, SortAsc, SortDesc, Filter, ChevronRight, Home } from "lucide-react"
import { Folder } from "@/types"
import Link from "next/link"

interface NotesHeaderProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  sortBy: "date" | "title"
  onSortChange: (sort: "date" | "title") => void
  selectedFolder: number | null
  onFolderChange: (folderId: number | null) => void
  folders: Folder[]
  currentFolder?: Folder
}

export function NotesHeader({
  searchQuery,
  onSearchChange,
  sortBy,
  onSortChange,
  selectedFolder,
  onFolderChange,
  folders,
  currentFolder
}: NotesHeaderProps) {
  return (
    <div className="border-b border-border bg-background">
      {/* Breadcrumb */}
      <div className="px-6 py-3 border-b border-border">
        <nav className="flex items-center space-x-2 text-sm">
          <Link href="/" className="text-muted-foreground hover:text-foreground">
            <Home className="h-4 w-4" />
          </Link>
          <ChevronRight className="h-4 w-4 text-muted-foreground" />
          <Link href="/notes" className="text-muted-foreground hover:text-foreground">
            Notes
          </Link>
          {currentFolder && currentFolder.id !== 1 && (
            <>
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
              <span className="text-foreground font-medium">{currentFolder.name}</span>
            </>
          )}
        </nav>
      </div>

      {/* Main Header */}
      <div className="px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">
              {currentFolder?.name || "All Notes"}
            </h1>
            <p className="text-muted-foreground">
              {currentFolder?.noteCount || 0} notes
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Sort Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSortChange(sortBy === "date" ? "title" : "date")}
            >
              {sortBy === "date" ? (
                <>
                  <SortDesc className="h-4 w-4 mr-2" />
                  Date
                </>
              ) : (
                <>
                  <SortAsc className="h-4 w-4 mr-2" />
                  Title
                </>
              )}
            </Button>

            {/* Folder Filter */}
            <select
              value={selectedFolder || ""}
              onChange={(e) => onFolderChange(e.target.value ? parseInt(e.target.value) : null)}
              className="px-3 py-2 border border-border rounded-md text-sm bg-background"
            >
              <option value="">All Folders</option>
              {folders.map((folder) => (
                <option key={folder.id} value={folder.id}>
                  {folder.name} ({folder.noteCount})
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search notes by title, content, or verse reference..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
    </div>
  )
}
