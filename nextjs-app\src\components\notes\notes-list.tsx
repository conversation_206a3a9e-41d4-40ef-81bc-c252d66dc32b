"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Note, Folder } from "@/types"
import { MoreVertical, Edit, Trash2, Share, Copy, BookOpen, Calendar, Folder as FolderIcon } from "lucide-react"

interface NotesListProps {
  notes: Note[]
  folders: Folder[]
  searchQuery: string
}

export function NotesList({ notes, folders, searchQuery }: NotesListProps) {
  const [selectedNote, setSelectedNote] = useState<number | null>(null)

  const getFolderName = (folderId?: number) => {
    if (!folderId) return "Uncategorized"
    const folder = folders.find(f => f.id === folderId)
    return folder?.name || "Unknown Folder"
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(new Date(date))
  }

  const highlightText = (text: string, query: string) => {
    if (!query) return text
    
    const regex = new RegExp(`(${query})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 text-yellow-900 px-1 rounded">
          {part}
        </mark>
      ) : part
    )
  }

  const handleNoteAction = (action: string, noteId: number) => {
    switch (action) {
      case 'edit':
        // Navigate to edit page
        console.log('Edit note:', noteId)
        break
      case 'delete':
        // Delete note
        console.log('Delete note:', noteId)
        break
      case 'share':
        // Share note
        console.log('Share note:', noteId)
        break
      case 'copy':
        // Copy note content
        console.log('Copy note:', noteId)
        break
    }
    setSelectedNote(null)
  }

  if (notes.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center space-y-4 max-w-md">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
            <BookOpen className="h-8 w-8 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {searchQuery ? "No notes found" : "No notes yet"}
            </h3>
            <p className="text-muted-foreground">
              {searchQuery 
                ? `No notes match "${searchQuery}". Try a different search term.`
                : "Start your Quranic study journey by creating your first note."
              }
            </p>
          </div>
          {!searchQuery && (
            <Link href="/study">
              <Button>
                <BookOpen className="h-4 w-4 mr-2" />
                Start Studying
              </Button>
            </Link>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-4">
      {notes.map((note) => (
        <Card key={note.id} className="p-4 hover:shadow-md transition-shadow">
          <div className="space-y-3">
            {/* Note Header */}
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <Link href={`/study?note=${note.id}`}>
                  <h3 className="font-medium text-lg hover:text-primary cursor-pointer truncate">
                    {highlightText(note.title, searchQuery)}
                  </h3>
                </Link>
                {note.shortDescription && (
                  <p className="text-muted-foreground text-sm mt-1 line-clamp-2">
                    {highlightText(note.shortDescription, searchQuery)}
                  </p>
                )}
              </div>
              
              {/* Actions Menu */}
              <div className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSelectedNote(selectedNote === note.id ? null : note.id)}
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
                
                {selectedNote === note.id && (
                  <div className="absolute right-0 top-8 w-48 bg-background border border-border rounded-md shadow-lg z-10">
                    <div className="py-1">
                      <button
                        onClick={() => handleNoteAction('edit', note.id)}
                        className="flex items-center w-full px-3 py-2 text-sm hover:bg-accent"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </button>
                      <button
                        onClick={() => handleNoteAction('copy', note.id)}
                        className="flex items-center w-full px-3 py-2 text-sm hover:bg-accent"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </button>
                      <button
                        onClick={() => handleNoteAction('share', note.id)}
                        className="flex items-center w-full px-3 py-2 text-sm hover:bg-accent"
                      >
                        <Share className="h-4 w-4 mr-2" />
                        Share
                      </button>
                      <button
                        onClick={() => handleNoteAction('delete', note.id)}
                        className="flex items-center w-full px-3 py-2 text-sm hover:bg-accent text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Note Footer */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <FolderIcon className="h-3 w-3" />
                  <span>{getFolderName(note.folderId)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <BookOpen className="h-3 w-3" />
                  <span>{note.verseKeys}</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                <Calendar className="h-3 w-3" />
                <span>{formatDate(note.updatedAt)}</span>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  )
}
