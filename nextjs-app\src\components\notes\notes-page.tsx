"use client"

import { useState } from "react"
import { Sidebar } from "@/components/sidebar"
import { NotesHeader } from "@/components/notes/notes-header"
import { NotesList } from "@/components/notes/notes-list"
import { NewChatButton } from "@/components/notes/new-chat-button"
import { Note, Folder } from "@/types"

export function NotesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFolder, setSelectedFolder] = useState<number | null>(null)
  const [sortBy, setSortBy] = useState<"date" | "title">("date")

  // Mock user data
  const currentUser = {
    name: "<PERSON>",
    email: "<EMAIL>"
  }

  // Mock folders data
  const folders: Folder[] = [
    { id: 1, userId: "1", name: "All notes", noteCount: 4, color: "#10b981", createdAt: new Date(), updatedAt: new Date() },
    { id: 2, userId: "1", name: "<PERSON><PERSON>", noteCount: 2, color: "#f59e0b", createdAt: new Date(), updatedAt: new Date() },
    { id: 3, userId: "1", name: "Prayer", noteCount: 1, color: "#8b5cf6", createdAt: new Date(), updatedAt: new Date() },
    { id: 4, userId: "1", name: "Tafsir Studies", noteCount: 3, color: "#ef4444", createdAt: new Date(), updatedAt: new Date() },
  ]

  // Mock notes data
  const notes: Note[] = [
    {
      id: 1,
      verseKeys: "2:255",
      userId: "1",
      folderId: 1,
      title: "Ayat al-Kursi Deep Study",
      shortDescription: "Comprehensive analysis of the Throne Verse and its profound meanings",
      content: "This verse is one of the most powerful in the Quran...",
      language: "en",
      createdAt: new Date("2024-01-15"),
      updatedAt: new Date("2024-01-15"),
    },
    {
      id: 2,
      verseKeys: "1:1-7",
      userId: "1",
      folderId: 2,
      title: "Al-Fatiha Reflection",
      shortDescription: "Personal reflections on the opening chapter of the Quran",
      content: "The opening of the Quran sets the tone...",
      language: "en",
      createdAt: new Date("2024-01-10"),
      updatedAt: new Date("2024-01-12"),
    },
    {
      id: 3,
      verseKeys: "17:78-79",
      userId: "1",
      folderId: 3,
      title: "Prayer Times and Tahajjud",
      shortDescription: "Understanding the prescribed prayer times and night prayer",
      content: "The verse mentions the prayer times...",
      language: "en",
      createdAt: new Date("2024-01-08"),
      updatedAt: new Date("2024-01-08"),
    },
    {
      id: 4,
      verseKeys: "24:35",
      userId: "1",
      folderId: 4,
      title: "The Light Verse",
      shortDescription: "Exploring the metaphor of divine light in the Quran",
      content: "Allah is the light of the heavens and earth...",
      language: "en",
      createdAt: new Date("2024-01-05"),
      updatedAt: new Date("2024-01-06"),
    },
  ]

  // Filter notes based on search and folder
  const filteredNotes = notes.filter(note => {
    const matchesSearch = !searchQuery || 
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.shortDescription?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.verseKeys.includes(searchQuery)
    
    const matchesFolder = !selectedFolder || selectedFolder === 1 || note.folderId === selectedFolder
    
    return matchesSearch && matchesFolder
  })

  // Sort notes
  const sortedNotes = [...filteredNotes].sort((a, b) => {
    if (sortBy === "date") {
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    } else {
      return a.title.localeCompare(b.title)
    }
  })

  const currentFolder = selectedFolder ? folders.find(f => f.id === selectedFolder) : folders[0]

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <Sidebar 
        folders={folders.map(f => ({ id: f.id, name: f.name, count: f.noteCount, icon: "📁" }))}
        currentUser={currentUser}
        className="hidden md:block"
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <NotesHeader
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          sortBy={sortBy}
          onSortChange={setSortBy}
          selectedFolder={selectedFolder}
          onFolderChange={setSelectedFolder}
          folders={folders}
          currentFolder={currentFolder}
        />

        {/* New Chat Button */}
        <div className="px-6 py-4 border-b border-border">
          <NewChatButton />
        </div>

        {/* Notes List */}
        <div className="flex-1 overflow-y-auto">
          <NotesList 
            notes={sortedNotes}
            folders={folders}
            searchQuery={searchQuery}
          />
        </div>
      </div>
    </div>
  )
}
