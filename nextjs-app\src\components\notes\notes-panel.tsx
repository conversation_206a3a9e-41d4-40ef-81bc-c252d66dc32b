"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { SummaryTab } from "@/components/notes/summary-tab"
import { FlashcardsTab } from "@/components/notes/flashcards-tab"
import { QuizTab } from "@/components/notes/quiz-tab"
import { GamesTab } from "@/components/notes/games-tab"
import { FileText, Zap, Brain, Gamepad2, Save } from "lucide-react"

interface NotesPanelProps {
  selectedVerse?: string | null
  currentQuery?: string
}

export function NotesPanel({ selectedVerse, currentQuery }: NotesPanelProps) {
  const [activeTab, setActiveTab] = useState("summary")
  const [noteContent, setNoteContent] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)

  // Auto-generate notes when user types or selects a verse
  useEffect(() => {
    if (currentQuery && currentQuery.trim().length > 3) {
      generateNotes()
    }
  }, [currentQuery])

  const generateNotes = async () => {
    setIsGenerating(true)
    // Simulate AI note generation
    setTimeout(() => {
      setNoteContent(`# Study Notes for: "${currentQuery || selectedVerse}"\n\n## Key Points\n- This is an AI-generated summary\n- Based on your query or selected verse\n- Includes relevant context and insights\n\n## Reflection Questions\n1. What is the main message?\n2. How does this apply to daily life?\n3. What other verses relate to this topic?`)
      setIsGenerating(false)
    }, 2000)
  }

  const handleSaveNotes = () => {
    // Save notes functionality would be implemented here
    console.log("Saving notes:", noteContent)
  }

  return (
    <div className="h-full flex flex-col">
      {/* Notes Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 m-4 mb-0">
          <TabsTrigger value="summary" className="flex items-center space-x-1">
            <FileText className="h-3 w-3" />
            <span className="hidden sm:inline">Summary</span>
          </TabsTrigger>
          <TabsTrigger value="flashcards" className="flex items-center space-x-1">
            <Zap className="h-3 w-3" />
            <span className="hidden sm:inline">Cards</span>
          </TabsTrigger>
          <TabsTrigger value="quiz" className="flex items-center space-x-1">
            <Brain className="h-3 w-3" />
            <span className="hidden sm:inline">Quiz</span>
          </TabsTrigger>
          <TabsTrigger value="games" className="flex items-center space-x-1">
            <Gamepad2 className="h-3 w-3" />
            <span className="hidden sm:inline">Games</span>
          </TabsTrigger>
        </TabsList>

        {/* Tab Contents */}
        <div className="flex-1 overflow-hidden">
          <TabsContent value="summary" className="h-full m-0 p-4">
            <SummaryTab
              content={noteContent}
              onContentChange={setNoteContent}
              isGenerating={isGenerating}
              onGenerate={generateNotes}
              selectedVerse={selectedVerse}
              currentQuery={currentQuery}
            />
          </TabsContent>

          <TabsContent value="flashcards" className="h-full m-0 p-4">
            <FlashcardsTab
              selectedVerse={selectedVerse}
              currentQuery={currentQuery}
            />
          </TabsContent>

          <TabsContent value="quiz" className="h-full m-0 p-4">
            <QuizTab
              selectedVerse={selectedVerse}
              currentQuery={currentQuery}
            />
          </TabsContent>

          <TabsContent value="games" className="h-full m-0 p-4">
            <GamesTab
              selectedVerse={selectedVerse}
              currentQuery={currentQuery}
            />
          </TabsContent>
        </div>
      </Tabs>

      {/* Save Button */}
      {noteContent && (
        <div className="p-4 border-t border-border">
          <Button onClick={handleSaveNotes} className="w-full">
            <Save className="h-4 w-4 mr-2" />
            Save Notes
          </Button>
        </div>
      )}
    </div>
  )
}
