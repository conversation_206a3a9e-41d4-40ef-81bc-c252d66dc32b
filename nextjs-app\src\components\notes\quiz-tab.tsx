"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Brain, CheckCircle, XCircle, RotateCcw, Plus } from "lucide-react"

interface QuizTabProps {
  selectedVerse?: string | null
  currentQuery?: string
}

interface QuizQuestion {
  id: string
  question: string
  options: string[]
  correctAnswer: number
  explanation: string
}

export function QuizTab({ selectedVerse, currentQuery }: QuizTabProps) {
  const [quizQuestions, setQuizQuestions] = useState<QuizQuestion[]>([
    {
      id: "1",
      question: "What does 'Bismillah' mean?",
      options: [
        "In the name of <PERSON>",
        "Praise be to <PERSON>", 
        "Allah is great",
        "There is no god but <PERSON>"
      ],
      correctAnswer: 0,
      explanation: "<PERSON><PERSON><PERSON><PERSON> means 'In the name of <PERSON>' and is recited before starting any good deed."
    }
  ])
  
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)
  const [showResult, setShowResult] = useState(false)
  const [score, setScore] = useState(0)
  const [answeredQuestions, setAnsweredQuestions] = useState<number[]>([])

  const currentQuestion = quizQuestions[currentQuestionIndex]
  const hasQuestions = quizQuestions.length > 0
  const isQuizComplete = answeredQuestions.length === quizQuestions.length

  const handleAnswerSelect = (answerIndex: number) => {
    if (showResult) return
    setSelectedAnswer(answerIndex)
  }

  const handleSubmitAnswer = () => {
    if (selectedAnswer === null) return
    
    setShowResult(true)
    
    if (selectedAnswer === currentQuestion.correctAnswer) {
      setScore(prev => prev + 1)
    }
    
    setAnsweredQuestions(prev => [...prev, currentQuestionIndex])
  }

  const handleNextQuestion = () => {
    if (currentQuestionIndex < quizQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
      setSelectedAnswer(null)
      setShowResult(false)
    }
  }

  const resetQuiz = () => {
    setCurrentQuestionIndex(0)
    setSelectedAnswer(null)
    setShowResult(false)
    setScore(0)
    setAnsweredQuestions([])
  }

  const generateQuiz = () => {
    // In real app, this would call AI to generate quiz questions
    const newQuestion: QuizQuestion = {
      id: Date.now().toString(),
      question: `What is the main lesson from "${currentQuery || selectedVerse}"?`,
      options: [
        "AI-generated option 1",
        "AI-generated option 2", 
        "AI-generated option 3",
        "AI-generated option 4"
      ],
      correctAnswer: 0,
      explanation: "This would be an AI-generated explanation"
    }
    setQuizQuestions(prev => [...prev, newQuestion])
  }

  if (!hasQuestions) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4 max-w-sm">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
            <Brain className="h-8 w-8 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">No quiz questions yet</h4>
            <p className="text-sm text-muted-foreground">
              Generate quiz questions from your study content to test your understanding
            </p>
          </div>
          <Button onClick={generateQuiz}>
            <Plus className="h-4 w-4 mr-2" />
            Create Quiz
          </Button>
        </div>
      </div>
    )
  }

  if (isQuizComplete) {
    return (
      <div className="h-full flex items-center justify-center">
        <Card className="p-6 max-w-md w-full text-center space-y-4">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
            <Brain className="h-8 w-8 text-primary" />
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Quiz Complete!</h3>
            <p className="text-3xl font-bold text-primary">
              {score}/{quizQuestions.length}
            </p>
            <p className="text-sm text-muted-foreground">
              {score === quizQuestions.length 
                ? "Perfect score! Excellent understanding!" 
                : score >= quizQuestions.length * 0.7 
                ? "Great job! You have a good understanding." 
                : "Keep studying! Review the material and try again."}
            </p>
          </div>
          <div className="flex space-x-2">
            <Button onClick={resetQuiz} variant="outline" className="flex-1">
              <RotateCcw className="h-4 w-4 mr-2" />
              Retry
            </Button>
            <Button onClick={generateQuiz} className="flex-1">
              <Plus className="h-4 w-4 mr-2" />
              More Questions
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Quiz</h3>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            Question {currentQuestionIndex + 1} of {quizQuestions.length}
          </span>
          <Button variant="outline" size="sm" onClick={generateQuiz}>
            <Plus className="h-3 w-3 mr-1" />
            Add Question
          </Button>
        </div>
      </div>

      {/* Progress */}
      <div className="w-full bg-muted rounded-full h-2">
        <div 
          className="bg-primary h-2 rounded-full transition-all duration-300"
          style={{ width: `${(answeredQuestions.length / quizQuestions.length) * 100}%` }}
        />
      </div>

      {/* Question */}
      <Card className="flex-1 p-6">
        <div className="space-y-6">
          <h4 className="text-lg font-medium">{currentQuestion.question}</h4>
          
          <div className="space-y-3">
            {currentQuestion.options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(index)}
                disabled={showResult}
                className={`w-full p-3 text-left rounded-lg border transition-colors ${
                  selectedAnswer === index
                    ? showResult
                      ? index === currentQuestion.correctAnswer
                        ? "border-green-500 bg-green-50 text-green-700"
                        : "border-red-500 bg-red-50 text-red-700"
                      : "border-primary bg-primary/5"
                    : showResult && index === currentQuestion.correctAnswer
                    ? "border-green-500 bg-green-50 text-green-700"
                    : "border-border hover:border-primary/50"
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className="w-6 h-6 rounded-full border flex items-center justify-center text-sm">
                    {String.fromCharCode(65 + index)}
                  </span>
                  <span>{option}</span>
                  {showResult && selectedAnswer === index && (
                    index === currentQuestion.correctAnswer ? (
                      <CheckCircle className="h-5 w-5 text-green-500 ml-auto" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500 ml-auto" />
                    )
                  )}
                </div>
              </button>
            ))}
          </div>

          {showResult && (
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm font-medium mb-2">Explanation:</p>
              <p className="text-sm text-muted-foreground">{currentQuestion.explanation}</p>
            </div>
          )}
        </div>
      </Card>

      {/* Controls */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={resetQuiz}>
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset Quiz
        </Button>
        
        {!showResult ? (
          <Button 
            onClick={handleSubmitAnswer}
            disabled={selectedAnswer === null}
          >
            Submit Answer
          </Button>
        ) : (
          <Button 
            onClick={handleNextQuestion}
            disabled={currentQuestionIndex >= quizQuestions.length - 1}
          >
            Next Question
          </Button>
        )}
      </div>
    </div>
  )
}
