"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, <PERSON>rk<PERSON>, RefreshCw } from "lucide-react"

interface SummaryTabProps {
  content: string
  onContentChange: (content: string) => void
  isGenerating: boolean
  onGenerate: () => void
  selectedVerse?: string | null
  currentQuery?: string
}

export function SummaryTab({
  content,
  onContentChange,
  isGenerating,
  onGenerate,
  selectedVerse,
  currentQuery
}: SummaryTabProps) {
  const hasContent = content.trim().length > 0
  const hasQuery = (currentQuery && currentQuery.trim().length > 0) || selectedVerse

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Study Summary</h3>
        <div className="flex items-center space-x-2">
          {hasContent && (
            <Button
              variant="outline"
              size="sm"
              onClick={onGenerate}
              disabled={isGenerating}
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Regenerate
            </Button>
          )}
          {hasQuery && !hasContent && (
            <Button
              variant="outline"
              size="sm"
              onClick={onGenerate}
              disabled={isGenerating}
            >
              {isGenerating ? (
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              ) : (
                <Sparkles className="h-3 w-3 mr-1" />
              )}
              Generate Notes
            </Button>
          )}
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 flex flex-col">
        {isGenerating ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-3">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
              <p className="text-sm text-muted-foreground">
                Generating AI-powered study notes...
              </p>
            </div>
          </div>
        ) : hasContent ? (
          <Textarea
            value={content}
            onChange={(e) => onContentChange(e.target.value)}
            placeholder="Your study notes will appear here..."
            className="flex-1 resize-none font-mono text-sm"
          />
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-4 max-w-sm">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
                <Sparkles className="h-8 w-8 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">No notes yet</h4>
                <p className="text-sm text-muted-foreground">
                  Start typing in the chat or select a verse to automatically generate study notes
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tips */}
      {!hasContent && !isGenerating && (
        <div className="p-3 bg-muted rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 <strong>Tip:</strong> Notes are automatically generated when you ask questions or select verses. 
            You can edit and customize them as needed.
          </p>
        </div>
      )}
    </div>
  )
}
