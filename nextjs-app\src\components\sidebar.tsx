"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Folder, Plus, HelpCircle, Crown, Settings, X, Menu } from "lucide-react"
import { cn } from "@/lib/utils"

interface SidebarProps {
  className?: string
  folders?: Array<{
    id: number
    name: string
    count: number
    icon?: string
  }>
  currentUser?: {
    name: string
    email: string
    avatar?: string
  }
}

export function Sidebar({ className, folders = [], currentUser }: SidebarProps) {
  const [isOpen, setIsOpen] = useState(false)

  const defaultFolders = [
    { id: 1, name: "All notes", count: 4, icon: "📝" },
    { id: 2, name: "bio", count: 1, icon: "👤" },
  ]

  const displayFolders = folders.length > 0 ? folders : defaultFolders

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsOpen(true)}
      >
        <Menu className="h-6 w-6" />
      </Button>

      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed left-0 top-0 z-50 h-full w-80 bg-background border-r border-border transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0",
          isOpen ? "translate-x-0" : "-translate-x-full",
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <Link href="/" className="flex items-center space-x-3">
            <Image
              src="/logo.png"
              alt="Tadabbur AI"
              width={32}
              height={32}
              className="rounded"
            />
            <span className="text-xl font-semibold">Tadabbur AI</span>
          </Link>
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setIsOpen(false)}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Folders Section */}
        <div className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Folder className="h-5 w-5 text-muted-foreground" />
            <span className="font-medium">Folders</span>
          </div>

          <div className="space-y-2">
            {displayFolders.map((folder) => (
              <Link
                key={folder.id}
                href={`/notes?folder=${folder.id}`}
                className="flex items-center justify-between p-3 rounded-lg hover:bg-accent transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{folder.icon || "📁"}</span>
                  <span className="text-sm">{folder.name}</span>
                </div>
                <span className="text-xs text-muted-foreground">({folder.count})</span>
              </Link>
            ))}
          </div>

          <Button variant="ghost" className="w-full justify-start mt-4">
            <Plus className="h-4 w-4 mr-2" />
            Create new folder
          </Button>
        </div>

        {/* Bottom Section */}
        <div className="absolute bottom-0 left-0 right-0 p-6 space-y-4">
          {/* Support */}
          <Button variant="ghost" className="w-full justify-start">
            <HelpCircle className="h-4 w-4 mr-2" />
            Support
          </Button>

          {/* Upgrade */}
          <div className="space-y-2">
            <Button variant="ghost" className="w-full justify-start">
              <Crown className="h-4 w-4 mr-2" />
              Upgrade plan
            </Button>
            <p className="text-xs text-muted-foreground">
              Get more features and unlimited access
            </p>
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>3 / 3 Notes free</span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2">
                <div className="bg-primary h-2 rounded-full w-full"></div>
              </div>
            </div>
          </div>

          {/* Profile */}
          {currentUser && (
            <div className="flex items-center space-x-3 p-3 rounded-lg bg-accent">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">
                {currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{currentUser.name}</p>
                <p className="text-xs text-muted-foreground truncate">{currentUser.email}</p>
              </div>
              <Button variant="ghost" size="icon">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </aside>
    </>
  )
}
