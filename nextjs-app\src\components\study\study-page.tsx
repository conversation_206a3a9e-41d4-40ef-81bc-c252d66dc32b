"use client"

import { useState, useEffect } from "react"
import { Sidebar } from "@/components/sidebar"
import { ChatInterface } from "@/components/chat/chat-interface"
import { NotesPanel } from "@/components/notes/notes-panel"
import { Button } from "@/components/ui/button"
import { PanelRightOpen, PanelRightClose } from "lucide-react"

export function StudyPage() {
  const [isNotesPanelOpen, setIsNotesPanelOpen] = useState(true)
  const [currentQuery, setCurrentQuery] = useState("")
  const [selectedVerse, setSelectedVerse] = useState<string | null>(null)

  // Mock user data - in real app this would come from auth
  const currentUser = {
    name: "<PERSON>",
    email: "<EMAIL>"
  }

  // Mock folders data - in real app this would come from API
  const folders = [
    { id: 1, name: "All notes", count: 4, icon: "📝" },
    { id: 2, name: "<PERSON><PERSON>", count: 2, icon: "⭐" },
    { id: 3, name: "Prayer", count: 1, icon: "🤲" },
  ]

  // Auto-open notes panel when user starts typing
  const handleQueryChange = (query: string) => {
    setCurrentQuery(query)
    if (query.trim() && !isNotesPanelOpen) {
      setIsNotesPanelOpen(true)
    }
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <Sidebar 
        folders={folders}
        currentUser={currentUser}
        className="hidden md:block"
      />

      {/* Main Content Area */}
      <div className="flex-1 flex">
        {/* Chat Interface */}
        <div className={`flex-1 transition-all duration-300 ${
          isNotesPanelOpen ? "md:w-1/2" : "w-full"
        }`}>
          <ChatInterface 
            onQueryChange={handleQueryChange}
            onVerseSelect={setSelectedVerse}
            currentQuery={currentQuery}
          />
        </div>

        {/* Notes Panel */}
        <div className={`transition-all duration-300 border-l border-border ${
          isNotesPanelOpen 
            ? "w-full md:w-1/2 flex" 
            : "w-0 hidden"
        }`}>
          <div className="flex-1 flex flex-col">
            {/* Notes Panel Header */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <h2 className="font-semibold">Study Notes</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsNotesPanelOpen(false)}
              >
                <PanelRightClose className="h-4 w-4" />
              </Button>
            </div>

            {/* Notes Panel Content */}
            <div className="flex-1">
              <NotesPanel 
                selectedVerse={selectedVerse}
                currentQuery={currentQuery}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Toggle Notes Panel Button (when closed) */}
      {!isNotesPanelOpen && (
        <Button
          variant="outline"
          size="icon"
          className="fixed right-4 top-1/2 transform -translate-y-1/2 z-10"
          onClick={() => setIsNotesPanelOpen(true)}
        >
          <PanelRightOpen className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}
