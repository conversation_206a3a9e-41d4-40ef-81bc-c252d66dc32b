"use client"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Upload, X, FileText, Image, Loader2, CheckCircle, AlertCircle } from "lucide-react"

interface FileUploadProps {
  onUploadComplete?: (result: UploadResult) => void
  onUploadStart?: () => void
  acceptedTypes?: string[]
  maxSize?: number // in MB
}

interface UploadResult {
  id: string
  url: string
  ocrText?: string
  detectedVerses?: string[]
  confidence?: number
}

export function FileUpload({ 
  onUploadComplete, 
  onUploadStart,
  acceptedTypes = ["image/jpeg", "image/png", "image/webp", "application/pdf"],
  maxSize = 10 
}: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleFileSelect = (file: File) => {
    setError(null)
    
    // Validate file type
    if (!acceptedTypes.includes(file.type)) {
      setError(`File type not supported. Please upload: ${acceptedTypes.join(", ")}`)
      return
    }

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`File size too large. Maximum size is ${maxSize}MB`)
      return
    }

    setUploadedFile(file)
  }

  const handleUpload = async () => {
    if (!uploadedFile) return

    setIsUploading(true)
    setError(null)
    onUploadStart?.()

    try {
      const formData = new FormData()
      formData.append("file", uploadedFile)

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Upload failed")
      }

      const result = await response.json()
      
      if (result.success) {
        setUploadResult(result.data)
        onUploadComplete?.(result.data)
      } else {
        throw new Error(result.error || "Upload failed")
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Upload failed")
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveFile = () => {
    setUploadedFile(null)
    setUploadResult(null)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith("image/")) {
      return <Image className="h-8 w-8 text-blue-500" />
    } else if (file.type === "application/pdf") {
      return <FileText className="h-8 w-8 text-red-500" />
    }
    return <FileText className="h-8 w-8 text-gray-500" />
  }

  if (uploadResult) {
    return (
      <Card className="p-6">
        <div className="text-center space-y-4">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
          <div>
            <h3 className="font-medium">Upload Successful!</h3>
            <p className="text-sm text-muted-foreground">
              File processed and text extracted
            </p>
          </div>
          
          {uploadResult.ocrText && (
            <div className="text-left bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">Extracted Text:</h4>
              <p className="text-sm whitespace-pre-wrap">{uploadResult.ocrText}</p>
            </div>
          )}
          
          {uploadResult.detectedVerses && uploadResult.detectedVerses.length > 0 && (
            <div className="text-left bg-primary/5 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Detected Verses:</h4>
              <div className="flex flex-wrap gap-2">
                {uploadResult.detectedVerses.map((verse, index) => (
                  <span key={index} className="bg-primary/10 text-primary px-2 py-1 rounded text-sm">
                    {verse}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          <Button onClick={handleRemoveFile} variant="outline">
            Upload Another File
          </Button>
        </div>
      </Card>
    )
  }

  if (uploadedFile) {
    return (
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            {getFileIcon(uploadedFile)}
            <div className="flex-1 min-w-0">
              <p className="font-medium truncate">{uploadedFile.name}</p>
              <p className="text-sm text-muted-foreground">
                {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleRemoveFile}
              disabled={isUploading}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          {error && (
            <div className="flex items-center space-x-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}
          
          <div className="flex space-x-2">
            <Button 
              onClick={handleUpload} 
              disabled={isUploading}
              className="flex-1"
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                "Process File"
              )}
            </Button>
            <Button 
              variant="outline" 
              onClick={handleRemoveFile}
              disabled={isUploading}
            >
              Cancel
            </Button>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card 
      className={`p-6 border-2 border-dashed transition-colors cursor-pointer ${
        isDragging 
          ? "border-primary bg-primary/5" 
          : "border-border hover:border-primary/50"
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => fileInputRef.current?.click()}
    >
      <div className="text-center space-y-4">
        <Upload className="h-12 w-12 text-muted-foreground mx-auto" />
        <div>
          <h3 className="font-medium">Upload Image or PDF</h3>
          <p className="text-sm text-muted-foreground">
            Drag and drop or click to select a file
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Supports JPEG, PNG, WebP, PDF (max {maxSize}MB)
          </p>
        </div>
        <Button variant="outline">
          Choose File
        </Button>
      </div>
      
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(",")}
        onChange={handleFileInputChange}
        className="hidden"
      />
    </Card>
  )
}
