"use server"

import { auth } from "@/lib/auth/config"
import { db } from "@/lib/db"
import { folders, notes } from "@/lib/db/schema"
import { eq, and, count } from "drizzle-orm"
import { revalidatePath } from "next/cache"
import { headers } from "next/headers"
import { redirect } from "next/navigation"

export async function createFolder(formData: FormData) {
  const session = await auth.api.getSession({
    headers: await headers(),
  })
  
  if (!session) {
    redirect("/auth/login")
  }

  const name = formData.get("name") as string
  const color = formData.get("color") as string

  if (!name || name.trim().length === 0) {
    throw new Error("Folder name is required")
  }

  try {
    // Check if folder name already exists for this user
    const existingFolder = await db
      .select()
      .from(folders)
      .where(and(
        eq(folders.userId, session.user.id),
        eq(folders.name, name.trim())
      ))
      .limit(1)

    if (existingFolder.length > 0) {
      throw new Error("Folder name already exists")
    }

    const newFolder = await db.insert(folders).values({
      userId: session.user.id,
      name: name.trim(),
      color: color || "#10b981",
      noteCount: 0,
    }).returning()

    revalidatePath("/notes")
    return { success: true, data: newFolder[0] }
  } catch (error) {
    console.error("Error creating folder:", error)
    throw new Error("Failed to create folder")
  }
}

export async function updateFolder(folderId: number, formData: FormData) {
   const session = await auth.api.getSession({
    headers: await headers(),
  })
  
  if (!session) {
    redirect("/auth/login")
  }

  const name = formData.get("name") as string
  const color = formData.get("color") as string

  try {
    // Check if folder exists and belongs to user
    const existingFolder = await db
      .select()
      .from(folders)
      .where(and(
        eq(folders.id, folderId),
        eq(folders.userId, session.user.id)
      ))
      .limit(1)

    if (existingFolder.length === 0) {
      throw new Error("Folder not found")
    }

    // Check if new name conflicts with existing folders
    if (name && name.trim() !== existingFolder[0].name) {
      const nameConflict = await db
        .select()
        .from(folders)
        .where(and(
          eq(folders.userId, session.user.id),
          eq(folders.name, name.trim())
        ))
        .limit(1)

      if (nameConflict.length > 0) {
        throw new Error("Folder name already exists")
      }
    }

    const updatedFolder = await db
      .update(folders)
      .set({
        name: name?.trim() || existingFolder[0].name,
        color: color || existingFolder[0].color,
        updatedAt: new Date(),
      })
      .where(and(
        eq(folders.id, folderId),
        eq(folders.userId, session.user.id)
      ))
      .returning()

    revalidatePath("/notes")
    return { success: true, data: updatedFolder[0] }
  } catch (error) {
    console.error("Error updating folder:", error)
    throw new Error("Failed to update folder")
  }
}

export async function deleteFolder(folderId: number, moveNotesToFolderId?: number) {
   const session = await auth.api.getSession({
    headers: await headers(),
  })
  
  if (!session) {
    redirect("/auth/login")
  }

  try {
    // Check if folder exists and belongs to user
    const existingFolder = await db
      .select()
      .from(folders)
      .where(and(
        eq(folders.id, folderId),
        eq(folders.userId, session.user.id)
      ))
      .limit(1)

    if (existingFolder.length === 0) {
      throw new Error("Folder not found")
    }

    // Handle notes in the folder
    if (moveNotesToFolderId) {
      // Move notes to another folder
      await db
        .update(notes)
        .set({ folderId: moveNotesToFolderId })
        .where(eq(notes.folderId, folderId))
    } else {
      // Set notes to uncategorized (null folder)
      await db
        .update(notes)
        .set({ folderId: null })
        .where(eq(notes.folderId, folderId))
    }

    // Delete the folder
    await db
      .delete(folders)
      .where(and(
        eq(folders.id, folderId),
        eq(folders.userId, session.user.id)
      ))

    // Update note count for destination folder if notes were moved
    if (moveNotesToFolderId) {
      await updateFolderNoteCount(moveNotesToFolderId)
    }

    revalidatePath("/notes")
    return { success: true, message: "Folder deleted successfully" }
  } catch (error) {
    console.error("Error deleting folder:", error)
    throw new Error("Failed to delete folder")
  }
}

export async function getUserFolders() {
   const session = await auth.api.getSession({
    headers: await headers(),
  })
  
  if (!session) {
    redirect("/auth/login")
  }

  try {
    // Get folders with note counts
    const userFolders = await db
      .select({
        id: folders.id,
        name: folders.name,
        color: folders.color,
        createdAt: folders.createdAt,
        updatedAt: folders.updatedAt,
        noteCount: count(notes.id),
      })
      .from(folders)
      .leftJoin(notes, eq(folders.id, notes.folderId))
      .where(eq(folders.userId, session.user.id))
      .groupBy(folders.id)
      .orderBy(folders.createdAt)

    return userFolders
  } catch (error) {
    console.error("Error fetching user folders:", error)
    throw new Error("Failed to fetch folders")
  }
}

async function updateFolderNoteCount(folderId: number) {
  try {
    const noteCount = await db
      .select({ count: notes.id })
      .from(notes)
      .where(eq(notes.folderId, folderId))

    await db
      .update(folders)
      .set({ noteCount: noteCount.length })
      .where(eq(folders.id, folderId))
  } catch (error) {
    console.error("Error updating folder note count:", error)
  }
}
