"use server"

import { auth } from "@/lib/auth/config"
import { db } from "@/lib/db"
import { notes, folders } from "@/lib/db/schema"
import { eq, and, desc, like, or, SQL } from "drizzle-orm"
import { revalidatePath } from "next/cache"
import { headers } from "next/headers"
import { redirect } from "next/navigation"

export async function createNote(formData: FormData) {
   const session = await auth.api.getSession({
      headers: await headers(),
    })
  
  if (!session) {
    redirect("/auth/login")
  }

  const verseKeys = formData.get("verseKeys") as string
  const title = formData.get("title") as string
  const shortDescription = formData.get("shortDescription") as string
  const content = formData.get("content") as string
  const folderId = formData.get("folderId") as string

  if (!verseKeys || !title) {
    throw new Error("Verse keys and title are required")
  }

  try {
    const newNote = await db.insert(notes).values({
      verseKeys,
      userId: session.user.id,
      folderId: folderId ? parseInt(folderId) : null,
      title,
      shortDescription: shortDescription || null,
      content: content || null,
      language: "en",
    }).returning()

    // Update folder note count
    if (folderId) {
      await updateFolderNoteCount(parseInt(folderId))
    }

    revalidatePath("/notes")
    return { success: true, data: newNote[0] }
  } catch (error) {
    console.error("Error creating note:", error)
    throw new Error("Failed to create note")
  }
}

export async function updateNote(noteId: number, formData: FormData) {
   const session = await auth.api.getSession({
    headers: await headers(),
  })
  
  if (!session) {
    redirect("/auth/login")
  }

  const title = formData.get("title") as string
  const shortDescription = formData.get("shortDescription") as string
  const content = formData.get("content") as string
  const folderId = formData.get("folderId") as string

  try {
    // Check if note exists and belongs to user
    const existingNote = await db
      .select()
      .from(notes)
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))
      .limit(1)

    if (existingNote.length === 0) {
      throw new Error("Note not found")
    }

    const oldFolderId = existingNote[0].folderId
    const newFolderId = folderId ? parseInt(folderId) : null

    const updatedNote = await db
      .update(notes)
      .set({
        title: title || existingNote[0].title,
        shortDescription: shortDescription !== undefined ? shortDescription : existingNote[0].shortDescription,
        content: content !== undefined ? content : existingNote[0].content,
        folderId: newFolderId,
        updatedAt: new Date(),
      })
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))
      .returning()

    // Update folder note counts if folder changed
    if (oldFolderId !== newFolderId) {
      if (oldFolderId) await updateFolderNoteCount(oldFolderId)
      if (newFolderId) await updateFolderNoteCount(newFolderId)
    }

    revalidatePath("/notes")
    return { success: true, data: updatedNote[0] }
  } catch (error) {
    console.error("Error updating note:", error)
    throw new Error("Failed to update note")
  }
}

export async function deleteNote(noteId: number) {
   const session = await auth.api.getSession({
    headers: await headers(),
  })
  
  if (!session) {
    redirect("/auth/login")
  }

  try {
    // Check if note exists and belongs to user
    const existingNote = await db
      .select()
      .from(notes)
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))
      .limit(1)

    if (existingNote.length === 0) {
      throw new Error("Note not found")
    }

    const folderId = existingNote[0].folderId

    await db
      .delete(notes)
      .where(and(
        eq(notes.id, noteId),
        eq(notes.userId, session.user.id)
      ))

    // Update folder note count
    if (folderId) {
      await updateFolderNoteCount(folderId)
    }

    revalidatePath("/notes")
    return { success: true, message: "Note deleted successfully" }
  } catch (error) {
    console.error("Error deleting note:", error)
    throw new Error("Failed to delete note")
  }
}

export async function getUserNotes(searchQuery?: string, folderId?: number) {
   const session = await auth.api.getSession({
    headers: await headers(),
  })
  
  if (!session) {
    redirect("/auth/login")
  }

  try {

      let conditions: SQL<unknown> | undefined = undefined

      conditions = eq(notes.userId, session.user.id)

      if (folderId) {
        conditions = and(
          conditions,
          eq(notes.folderId, folderId)
        )
      }

      if (searchQuery) {
        const searchFilter = or(
          like(notes.title, `%${searchQuery}%`),
          like(notes.shortDescription, `%${searchQuery}%`),
          like(notes.content, `%${searchQuery}%`),
          like(notes.verseKeys, `%${searchQuery}%`)
        )

        conditions = and(conditions, searchFilter)
      }

    const query = db
      .select({
        id: notes.id,
        verseKeys: notes.verseKeys,
        title: notes.title,
        shortDescription: notes.shortDescription,
        content: notes.content,
        language: notes.language,
        folderId: notes.folderId,
        createdAt: notes.createdAt,
        updatedAt: notes.updatedAt,
        folderName: folders.name,
      })
      .from(notes)
      .leftJoin(folders, eq(notes.folderId, folders.id))
      .where(conditions);

    const userNotes = await query.orderBy(desc(notes.updatedAt))

    return userNotes
  } catch (error) {
    console.error("Error fetching user notes:", error)
    throw new Error("Failed to fetch notes")
  }
}

async function updateFolderNoteCount(folderId: number) {
  try {
    const noteCount = await db
      .select({ count: notes.id })
      .from(notes)
      .where(eq(notes.folderId, folderId))

    await db
      .update(folders)
      .set({ noteCount: noteCount.length })
      .where(eq(folders.id, folderId))
  } catch (error) {
    console.error("Error updating folder note count:", error)
  }
}
