import { db } from "@/lib/db"
import { verses, translations, tafsirs, kitabs } from "@/lib/db/schema"
import { like, or, eq, and, desc } from "drizzle-orm"

export interface SearchOptions {
  query: string
  searchType?: "text" | "reference" | "concept" | "all"
  language?: string
  includeTranslations?: boolean
  includeTafsir?: boolean
  limit?: number
}

export interface SearchResult {
  verses: Array<{
    id: number
    surahNumber: number
    ayahNumber: number
    verseKeys: string
    textArabic: string
    textClean?: string
    translations?: Array<{
      text: string
      translator?: string
      language: string
    }>
    tafsirs?: Array<{
      text: string
      kitabTitle?: string
      language?: string
    }>
    relevanceScore?: number
  }>
  totalCount: number
  searchQuery: string
  suggestions?: string[]
}

export class VerseSearch {
  static async search(options: SearchOptions): Promise<SearchResult> {
    const {
      query,
      searchType = "all",
      language = "en",
      includeTranslations = true,
      includeTafsir = false,
      limit = 20
    } = options

    let verseResults: any[] = []
    let totalCount = 0

    try {
      // Search based on type
      switch (searchType) {
        case "reference":
          verseResults = await this.searchByReference(query, limit)
          break
        case "text":
          verseResults = await this.searchByText(query, limit)
          break
        case "concept":
          verseResults = await this.searchByConcept(query, limit)
          break
        case "all":
        default:
          verseResults = await this.searchAll(query, limit)
          break
      }

      totalCount = verseResults.length

      // Enhance results with translations and tafsir if requested
      if (includeTranslations || includeTafsir) {
        verseResults = await this.enhanceResults(
          verseResults,
          includeTranslations,
          includeTafsir,
          language
        )
      }

      // Calculate relevance scores
      verseResults = this.calculateRelevanceScores(verseResults, query)

      // Generate search suggestions
      const suggestions = await this.generateSuggestions(query)

      return {
        verses: verseResults,
        totalCount,
        searchQuery: query,
        suggestions
      }
    } catch (error) {
      console.error("Error in verse search:", error)
      return {
        verses: [],
        totalCount: 0,
        searchQuery: query,
        suggestions: []
      }
    }
  }

  private static async searchByReference(query: string, limit: number) {
    // Search by verse reference (e.g., "2:255", "Al-Baqarah 255")
    const cleanQuery = query.replace(/[^\d:]/g, "")
    
    return await db
      .select()
      .from(verses)
      .where(
        or(
          like(verses.verseKeys, `%${cleanQuery}%`),
          like(verses.verseKeys, `%${query}%`)
        )
      )
      .limit(limit)
  }

  private static async searchByText(query: string, limit: number) {
    // Search in Arabic text and transliteration
    const searchTerms = query.split(/\s+/).filter(term => term.length > 2)
    
    let conditions = []
    for (const term of searchTerms) {
      conditions.push(
        like(verses.textArabic, `%${term}%`),
        like(verses.textClean, `%${term}%`)
      )
    }

    return await db
      .select()
      .from(verses)
      .where(or(...conditions))
      .limit(limit)
  }

  private static async searchByConcept(query: string, limit: number) {
    // Search by Islamic concepts - this would use semantic search in a real implementation
    const conceptKeywords = this.getConceptKeywords(query)
    
    let conditions = []
    for (const keyword of conceptKeywords) {
      conditions.push(
        like(verses.textArabic, `%${keyword}%`),
        like(verses.textClean, `%${keyword}%`)
      )
    }

    if (conditions.length === 0) {
      return []
    }

    return await db
      .select()
      .from(verses)
      .where(or(...conditions))
      .limit(limit)
  }

  private static async searchAll(query: string, limit: number) {
    // Comprehensive search across all fields
    const searchTerms = query.split(/\s+/).filter(term => term.length > 1)
    
    let conditions = []
    for (const term of searchTerms) {
      conditions.push(
        like(verses.verseKeys, `%${term}%`),
        like(verses.textArabic, `%${term}%`),
        like(verses.textClean, `%${term}%`)
      )
    }

    return await db
      .select()
      .from(verses)
      .where(or(...conditions))
      .limit(limit)
  }

  private static async enhanceResults(
    verseResults: any[],
    includeTranslations: boolean,
    includeTafsir: boolean,
    language: string
  ) {
    const verseKeys = verseResults.map(v => v.verseKeys)
    
    if (verseKeys.length === 0) return verseResults

    let translationResults = []
    let tafsirResults = []

    if (includeTranslations) {
      translationResults = await db
        .select({
          verseKeys: translations.verseKeys,
          text: translations.text,
          translator: translations.translator,
          language: translations.language,
        })
        .from(translations)
        .where(
          and(
            or(...verseKeys.map(key => eq(translations.verseKeys, key))),
            eq(translations.language, language)
          )
        )
    }

    if (includeTafsir) {
      tafsirResults = await db
        .select({
          verseKeys: tafsirs.verseKeys,
          text: tafsirs.text,
          language: tafsirs.language,
          kitabTitle: kitabs.title,
        })
        .from(tafsirs)
        .leftJoin(kitabs, eq(tafsirs.kitabId, kitabs.id))
        .where(
          and(
            or(...verseKeys.map(key => eq(tafsirs.verseKeys, key))),
            eq(tafsirs.language, language)
          )
        )
    }

    // Combine results
    return verseResults.map(verse => ({
      ...verse,
      translations: translationResults.filter(t => t.verseKeys === verse.verseKeys),
      tafsirs: tafsirResults.filter(t => t.verseKeys === verse.verseKeys),
    }))
  }

  private static calculateRelevanceScores(results: any[], query: string): any[] {
    const queryTerms = query.toLowerCase().split(/\s+/)
    
    return results.map(verse => {
      let score = 0
      const searchText = `${verse.verseKeys} ${verse.textArabic} ${verse.textClean || ""}`.toLowerCase()
      
      // Exact match bonus
      if (searchText.includes(query.toLowerCase())) {
        score += 10
      }
      
      // Term frequency scoring
      queryTerms.forEach(term => {
        const matches = (searchText.match(new RegExp(term, 'g')) || []).length
        score += matches * 2
      })
      
      // Reference match bonus
      if (verse.verseKeys.includes(query)) {
        score += 20
      }
      
      return {
        ...verse,
        relevanceScore: score
      }
    }).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
  }

  private static async generateSuggestions(query: string): Promise<string[]> {
    // Generate search suggestions based on common queries
    const suggestions = [
      "prayer times",
      "patience in Quran",
      "stories of prophets",
      "gratitude verses",
      "forgiveness",
      "charity and giving",
      "faith and belief",
      "paradise description",
      "day of judgment",
      "Allah's attributes"
    ]

    // Filter suggestions based on query
    const filtered = suggestions.filter(suggestion => 
      suggestion.toLowerCase().includes(query.toLowerCase()) ||
      query.toLowerCase().includes(suggestion.toLowerCase())
    )

    return filtered.slice(0, 5)
  }

  private static getConceptKeywords(concept: string): string[] {
    const conceptMap: Record<string, string[]> = {
      "prayer": ["صلاة", "صلى", "يصلي", "صلوا"],
      "patience": ["صبر", "صابر", "اصبر", "صبروا"],
      "gratitude": ["شكر", "شاكر", "اشكر", "شكروا"],
      "forgiveness": ["غفر", "غافر", "اغفر", "غفروا"],
      "charity": ["زكاة", "صدقة", "انفق", "انفقوا"],
      "faith": ["ايمان", "آمن", "مؤمن", "آمنوا"],
      "paradise": ["جنة", "جنات", "فردوس"],
      "hell": ["نار", "جهنم", "سعير"],
      "prophet": ["نبي", "رسول", "انبياء", "رسل"],
      "allah": ["الله", "رب", "إله"]
    }

    const lowerConcept = concept.toLowerCase()
    for (const [key, keywords] of Object.entries(conceptMap)) {
      if (lowerConcept.includes(key)) {
        return keywords
      }
    }

    return []
  }
}
