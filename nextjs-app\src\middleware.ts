import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"

export async function middleware(request: NextRequest) {
  const session = await auth.api.getSession({
    headers: request.headers,
  })

  const isAuthPage = request.nextUrl.pathname.startsWith("/auth")
  const isProtectedPage = ["/study", "/notes", "/profile"].some(path => 
    request.nextUrl.pathname.startsWith(path)
  )

  // If user is not authenticated and trying to access protected pages
  if (!session && isProtectedPage) {
    return NextResponse.redirect(new URL("/auth/login", request.url))
  }

  // If user is authenticated and trying to access auth pages
  if (session && isAuthPage) {
    return NextResponse.redirect(new URL("/study", request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|logo.png).*)",
  ],
}
